# 新版VisualizationForm使用指南

## 概述

新版的`VisualizationForm`已经完全重新设计，包含三个主要功能模块：

1. **脆性/塑性矿物比例饼状图**（带深度轴控制）
2. **矿物含量分布堆叠柱状图**（使用MineralStackedBarChartControl）
3. **手动坐标轴控件示例**（ManualAxisControl）

## 功能特性

### 1. 脆性/塑性矿物比例饼状图

#### 功能描述
- 显示当前选定深度下脆性矿物与塑性矿物的比例
- 通过深度滑动条可以切换不同深度的数据
- 实时更新饼状图显示

#### 控件组成
- **深度滑动条**：用于选择不同深度
- **当前深度标签**：显示当前选中的深度值
- **饼状图**：显示脆性矿物（蓝色）和塑性矿物（绿色）的比例

#### 使用方法
1. 拖动深度滑动条选择不同深度
2. 饼状图会自动更新显示该深度的矿物比例
3. 图例显示具体的百分比数值

### 2. 矿物含量分布堆叠柱状图

#### 功能描述
- 使用改进的`MineralStackedBarChartControl`控件
- 显示所有深度的矿物含量分布
- 包含可视化坐标轴控制面板

#### 特性
- **手动坐标轴设置**：可以自定义X轴和Y轴参数
- **优化的图例显示**：图例项水平均分，避免拥挤
- **深度数据绑定**：Y轴自动绑定深度数据

### 3. 手动坐标轴控件示例

#### 功能描述
- 展示如何使用`ManualAxisControl`控件
- 提供完全自定义的坐标轴绘制
- 支持深度数据绑定和手动X轴设置

#### 控件特性
- **自定义绘制**：使用GDI+绘制坐标轴和网格
- **数据绑定**：Y轴自动绑定深度数据
- **交互设置**：可以通过界面设置X轴参数

## 技术实现

### ManualAxisControl控件

#### 核心功能
```csharp
// 设置X轴参数
manualAxisControl.SetXAxis(0, 100, 20, "矿物含量/%");

// 设置深度数据
manualAxisControl.SetDepthData(depthList, "深度/m");

// 坐标转换
Point pixelPoint = manualAxisControl.DataToPixel(x, y);
PointF dataPoint = manualAxisControl.PixelToData(pixelPoint);
```

#### 属性设置
- `XMinimum`, `XMaximum`, `XInterval`：X轴范围和间隔
- `XTitle`, `YTitle`：坐标轴标题
- `ShowGrid`：是否显示网格
- `AxisColor`, `GridColor`：颜色设置

### 深度轴饼状图实现

#### 核心逻辑
```csharp
// 深度滑动条事件
private void DepthTrackBar_ValueChanged(object sender, EventArgs e)
{
    _currentDepthIndex = _depthTrackBar.Value;
    UpdateCurrentDepthLabel();
    UpdatePieChart();
}

// 更新饼状图
private void UpdatePieChart()
{
    double currentDepth = _depths[_currentDepthIndex];
    // 查找当前深度的数据行
    // 计算脆性矿物和塑性矿物总量
    // 更新饼状图显示
}
```

## 使用场景

### 场景1：深度分析
使用饼状图功能分析不同深度下的矿物组成变化：
1. 拖动深度滑动条到目标深度
2. 观察脆性/塑性矿物比例变化
3. 识别岩性变化层位

### 场景2：整体分布分析
使用堆叠柱状图分析整体矿物分布：
1. 查看所有深度的矿物含量分布
2. 使用坐标轴控制面板调整显示范围
3. 分析矿物含量的纵向变化趋势

### 场景3：自定义可视化
使用手动坐标轴控件创建自定义图表：
1. 设置合适的X轴范围和间隔
2. 系统自动绑定深度数据到Y轴
3. 在坐标系上绘制自定义数据

## 数据要求

### 输入数据格式
```csharp
// 构造函数参数
public VisualizationForm(
    DataTable resultData,           // 计算结果数据表
    List<string> brittleMinerals,   // 脆性矿物列表
    List<string> ductileMinerals    // 塑性矿物列表
)
```

### 数据表结构要求
- 必须包含"顶深/m"列作为深度数据
- 必须包含脆性矿物和塑性矿物的含量列
- 数据值应为数值类型（double）

## 调用示例

### 从AlgorithmFormulaCal调用
```csharp
// 在AlgorithmFormulaCal中
private void ShowVisualization()
{
    if (_resultData != null && _resultData.Rows.Count > 0)
    {
        var visualizationForm = new VisualizationForm(
            _resultData,
            _brittleMinerals,
            _ductileMinerals
        );
        visualizationForm.Show();
    }
}
```

### 从MineralogicalForm调用
```csharp
// 在MineralogicalForm中
public void ShowVisualization(DataTable data, List<string> brittle, List<string> ductile)
{
    var visualizationForm = new VisualizationForm(data, brittle, ductile);
    visualizationForm.ShowDialog();
}
```

## 注意事项

1. **数据完整性**：确保传入的数据包含所需的深度和矿物含量列
2. **性能考虑**：大量数据时饼状图更新可能较慢
3. **内存管理**：及时释放不需要的图表资源
4. **线程安全**：UI更新操作需要在主线程中执行

## 故障排除

### 问题：饼状图不更新
- 检查深度数据是否正确加载
- 确认矿物列表是否与数据表列名匹配

### 问题：手动坐标轴显示异常
- 检查深度数据是否已设置
- 确认X轴参数设置是否合理

### 问题：堆叠柱状图显示不完整
- 检查MineralStackedBarChartControl的数据绑定
- 确认矿物列表设置正确
