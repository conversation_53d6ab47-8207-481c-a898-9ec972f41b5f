# 增强版静态岩石力学参数法系统

## 项目简介

增强版静态岩石力学参数法系统是一个专业的脆性指数计算软件，基于静态岩石力学参数（密度、纵波速度、横波速度）进行计算。本系统在原有功能基础上，大幅增强了数据兼容性和对比分析能力。

## 核心特性

### 🚀 增强功能
- **多格式数据导入**：支持 Excel、CSV、JSON 格式
- **智能文件识别**：自动识别文件命名格式
- **批量数据处理**：一次性处理多个数据源
- **图片关联显示**：自动关联相关图片文件
- **增强对比图**：多系统数据对比分析

### 📊 计算功能
- **动态弹性参数计算**：杨氏模量、泊松比
- **脆性指数计算**：基于归一化方法
- **实时图表显示**：交互式数据可视化
- **结果导出**：多种格式输出

### 🔄 兼容性
- **向后兼容**：完全兼容原有系统数据格式
- **跨系统数据交换**：标准化数据接口
- **多版本支持**：支持不同版本数据格式

## 技术架构

### 开发环境
- **.NET 6.0**：现代化的开发框架
- **Windows Forms**：成熟的桌面应用界面
- **NPOI**：Excel 文件处理
- **Newtonsoft.Json**：JSON 数据处理

### 项目结构
```
EnhancedStaticRockMechanicsSystem/
├── Forms/                          # 窗体文件
│   ├── StaticRockMechanicsForm.cs   # 主窗体
│   ├── EnhancedComparisonChartForm.cs # 增强对比图窗体
│   └── BatchImportWizard.cs         # 批量导入向导
├── Services/                        # 服务层
│   ├── UnifiedComparisonDataManager.cs # 统一数据管理器
│   ├── ExtendedCsvImportService.cs     # CSV导入服务
│   ├── ExtendedExcelImportService.cs   # Excel导入服务
│   ├── ImageAssociationService.cs      # 图片关联服务
│   ├── ComparisonFileParser.cs         # 文件解析器
│   └── LoggingService.cs               # 日志服务
├── Models/                          # 数据模型
│   ├── BrittlenessDataPoint.cs      # 脆性指数数据点
│   └── ComparisonDataSet.cs         # 对比数据集
├── Resources/                       # 资源文件
├── Documentation/                   # 文档
└── SampleData/                     # 示例数据
```

## 快速开始

### 环境准备
1. 安装 .NET 6.0 Desktop Runtime
2. 克隆或下载项目代码
3. 使用 Visual Studio 2022 或 VS Code 打开项目

### 编译运行
```bash
# 编译项目
dotnet build

# 运行项目
dotnet run

# 或者直接运行启动脚本
./启动系统.bat
```

### 基本使用
1. **导入数据**：点击"导入数据"按钮，选择包含岩石力学参数的Excel文件
2. **计算脆性指数**：点击"计算脆性指数"按钮进行计算
3. **查看结果**：在图表和数据表格中查看计算结果
4. **保存对比数据**：点击"存为对比图"保存数据供对比使用

## 数据格式

### 输入数据要求
Excel文件应包含以下列：
- `顶深/m`：井深起始位置（必需）
- `底深/m`：井深结束位置（可选）
- `密度/(g/cm³)`：岩石密度（必需）
- `纵波速度/(m/s)`：纵波传播速度（必需）
- `横波速度/(m/s)`：横波传播速度（必需）

### 支持的文件格式
- **Excel**：.xlsx, .xls
- **CSV**：.csv（UTF-8编码）
- **JSON**：.json（标准化格式）

### 文件命名规范
系统支持智能识别以下命名格式：
- `系统名_对比数据_YYYYMMDD_HHMMSS.扩展名`
- `SystemName_ComparisonData_YYYYMMDD.扩展名`
- 包含关键词的其他格式

## 计算原理

### 动态弹性参数
```
动态杨氏模量：E = ρ × vs² × (3vp² - 4vs²) / (vp² - vs²)
动态泊松比：ν = (vp² - 2vs²) / (2(vp² - vs²))
```

### 脆性指数计算
```
归一化杨氏模量：E_norm = (E - E_min) / (E_max - E_min)
归一化泊松比：ν_norm = (ν_max - ν) / (ν_max - ν_min)
脆性指数：BI = (E_norm + ν_norm) / 2 × 100
```

## 系统集成

### 与矿物组分法系统对比
本系统可以与矿物组分法系统进行数据对比：
1. 两个系统都支持"存为对比图"功能
2. 使用统一的数据交换格式
3. 支持多系统数据同时显示

### 数据交换接口
```json
{
  "FormatVersion": "3.0",
  "ExportInfo": {
    "SystemName": "增强版静态岩石力学参数法",
    "ExportTime": "2025-07-01T18:16:02",
    "DataCount": 100
  },
  "DataPoints": [
    {
      "TopDepth": 2500.0,
      "BottomDepth": 2501.0,
      "BrittleIndex": 65.8,
      "GeoID": "GEO_2500.00_2501.00_65.8000_A1B2C3D4"
    }
  ],
  "AssociatedImages": ["chart_20250701.png"],
  "Metadata": {}
}
```

## 开发指南

### 添加新的导入格式
1. 在 `Services` 文件夹中创建新的导入服务类
2. 实现 `IDataImportService` 接口
3. 在 `UnifiedComparisonDataManager` 中注册新服务

### 扩展文件识别规则
在 `ComparisonFileParser.cs` 中添加新的正则表达式模式：
```csharp
var patterns = new[]
{
    @"^(.+?)_对比数据_(\d{8})_(\d{6})$",
    @"^(.+?)_NewFormat_(\d{8})$",  // 新格式
    // 添加更多模式...
};
```

### 自定义计算公式
在 `StaticRockMechanicsForm.cs` 中修改计算方法：
```csharp
private double CalculateBrittlenessFromMechanics(double youngModulus, double poissonRatio)
{
    // 实现自定义计算逻辑
    return customBrittlenessIndex;
}
```

## 测试

### 单元测试
```bash
dotnet test
```

### 集成测试
使用 `SampleData` 文件夹中的示例数据进行测试：
1. 导入 `静态岩石力学参数数据_示例.xlsx`
2. 执行完整的计算流程
3. 验证结果的正确性

## 部署

### 发布版本
```bash
# 发布为单文件可执行程序
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 发布为框架依赖版本
dotnet publish -c Release -r win-x64 --self-contained false
```

### 安装包制作
使用 Inno Setup 或 WiX Toolset 制作安装包。

## 贡献指南

### 代码规范
- 使用 C# 命名约定
- 添加适当的注释和文档
- 遵循 SOLID 原则
- 编写单元测试

### 提交流程
1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

- **项目维护者**：BritSystem 开发团队
- **邮箱**：<EMAIL>
- **技术支持**：<EMAIL>
- **官网**：https://www.britsystem.com

## 更新日志

### v2.0.0 (2025-07-01)
- 🎉 增强版发布
- ✨ 新增批量导入功能
- ✨ 新增智能文件识别
- ✨ 新增图片关联显示
- ✨ 增强对比图功能
- 🔧 改进系统架构
- 📚 完善文档

### v1.0.0 (2024-12-01)
- 🎉 初始版本发布
- ✨ 基础计算功能
- ✨ Excel 数据处理
- ✨ 图表显示

---

**感谢使用增强版静态岩石力学参数法系统！**
