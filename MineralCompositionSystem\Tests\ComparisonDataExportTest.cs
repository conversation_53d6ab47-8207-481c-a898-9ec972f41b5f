using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using MineralCompositionSystem.Services;

namespace MineralCompositionSystem.Tests
{
    /// <summary>
    /// 对比图数据导出功能测试
    /// </summary>
    public class ComparisonDataExportTest
    {
        /// <summary>
        /// 测试数据导出功能
        /// </summary>
        public static async Task TestExportFunctionality()
        {
            try
            {
                Console.WriteLine("开始测试对比图数据导出功能...");
                
                // 创建测试数据
                var testData = CreateTestData();
                Console.WriteLine($"创建了 {testData.Count} 个测试数据点");
                
                // 创建导出服务
                var exportService = new ComparisonDataExportService();
                
                // 创建导出选项
                var options = new ComparisonDataExportOptions
                {
                    OutputPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TestExport"),
                    SystemName = "测试系统",
                    IncludeMetadata = true,
                    CompressOutput = false,
                    OpenAfterExport = false
                };
                
                // 确保输出目录存在
                Directory.CreateDirectory(options.OutputPath);
                
                // 测试单一格式导出
                Console.WriteLine("\n测试单一格式导出:");
                await TestSingleFormatExport(exportService, testData, options);
                
                // 测试多格式导出
                Console.WriteLine("\n测试多格式导出:");
                await TestMultipleFormatExport(exportService, testData, options);
                
                Console.WriteLine("\n所有测试完成！");
                Console.WriteLine($"导出文件保存在: {options.OutputPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
        
        private static async Task TestSingleFormatExport(ComparisonDataExportService exportService, 
            List<object> testData, ComparisonDataExportOptions options)
        {
            var formats = new[] { ExportFormat.JSON, ExportFormat.Excel, ExportFormat.CSV, ExportFormat.XML };
            
            foreach (var format in formats)
            {
                try
                {
                    Console.Write($"  导出 {format} 格式... ");
                    
                    string filePath = await exportService.ExportSingleFormat(testData, options, format, 
                        progress => {
                            if (progress % 25 == 0) Console.Write($"{progress}% ");
                        });
                    
                    if (File.Exists(filePath))
                    {
                        var fileInfo = new FileInfo(filePath);
                        Console.WriteLine($"成功! 文件大小: {fileInfo.Length} 字节");
                    }
                    else
                    {
                        Console.WriteLine("失败! 文件未创建");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"失败! 错误: {ex.Message}");
                }
            }
        }
        
        private static async Task TestMultipleFormatExport(ComparisonDataExportService exportService,
            List<object> testData, ComparisonDataExportOptions options)
        {
            try
            {
                Console.Write("  导出多种格式... ");
                
                var formats = new List<ExportFormat> { ExportFormat.JSON, ExportFormat.Excel, ExportFormat.CSV };
                
                var filePaths = await exportService.ExportMultipleFormats(testData, options, formats,
                    progress => {
                        if (progress % 20 == 0) Console.Write($"{progress}% ");
                    });
                
                Console.WriteLine($"成功! 导出了 {filePaths.Count} 个文件");
                
                foreach (var filePath in filePaths)
                {
                    if (File.Exists(filePath))
                    {
                        var fileInfo = new FileInfo(filePath);
                        Console.WriteLine($"    - {Path.GetFileName(filePath)}: {fileInfo.Length} 字节");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"失败! 错误: {ex.Message}");
            }
        }
        
        private static List<object> CreateTestData()
        {
            var testData = new List<object>();
            var random = new Random();
            
            // 创建50个测试数据点
            for (int i = 0; i < 50; i++)
            {
                double depth = 100 + i * 10 + random.NextDouble() * 5; // 深度从100m开始
                double brittleIndex = 20 + random.NextDouble() * 60;   // 脆性指数20-80%
                
                testData.Add(new
                {
                    TopDepth = depth,
                    BottomDepth = depth + 5,
                    BrittleIndex = Math.Round(brittleIndex, 2),
                    SystemName = "测试系统",
                    ExportTime = DateTime.Now,
                    GeoID = $"TEST_{i + 1:D3}"
                });
            }
            
            return testData;
        }
        
        /// <summary>
        /// 验证导出文件的内容
        /// </summary>
        public static void ValidateExportedFiles(string outputPath)
        {
            try
            {
                Console.WriteLine("\n验证导出文件:");
                
                var files = Directory.GetFiles(outputPath, "*.*", SearchOption.TopDirectoryOnly);
                
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    Console.WriteLine($"  {fileInfo.Name}: {fileInfo.Length} 字节, 修改时间: {fileInfo.LastWriteTime}");
                    
                    // 验证文件内容（简单检查）
                    if (fileInfo.Extension.ToLower() == ".json")
                    {
                        ValidateJsonFile(file);
                    }
                    else if (fileInfo.Extension.ToLower() == ".csv")
                    {
                        ValidateCsvFile(file);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证文件时出错: {ex.Message}");
            }
        }
        
        private static void ValidateJsonFile(string filePath)
        {
            try
            {
                string content = File.ReadAllText(filePath);
                var data = Newtonsoft.Json.JsonConvert.DeserializeObject(content);
                Console.WriteLine($"    JSON文件验证通过，内容长度: {content.Length}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    JSON文件验证失败: {ex.Message}");
            }
        }
        
        private static void ValidateCsvFile(string filePath)
        {
            try
            {
                var lines = File.ReadAllLines(filePath);
                Console.WriteLine($"    CSV文件验证通过，行数: {lines.Length}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    CSV文件验证失败: {ex.Message}");
            }
        }
    }
}
