using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace BritSystem
{
    /// <summary>
    /// 测试VisualizationForm编译和运行
    /// </summary>
    public partial class TestVisualizationFormCompilation : Form
    {
        private Button btnTestCompilation;
        private Button btnTestRuntime;
        private Label lblCompilationStatus;
        private Label lblRuntimeStatus;
        private TextBox txtLog;

        public TestVisualizationFormCompilation()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 创建编译测试按钮
            this.btnTestCompilation = new Button();
            this.btnTestCompilation.Text = "测试编译";
            this.btnTestCompilation.Size = new Size(120, 40);
            this.btnTestCompilation.Location = new Point(20, 20);
            this.btnTestCompilation.Click += BtnTestCompilation_Click;

            // 创建运行时测试按钮
            this.btnTestRuntime = new Button();
            this.btnTestRuntime.Text = "测试运行时";
            this.btnTestRuntime.Size = new Size(120, 40);
            this.btnTestRuntime.Location = new Point(160, 20);
            this.btnTestRuntime.Click += BtnTestRuntime_Click;

            // 创建编译状态标签
            this.lblCompilationStatus = new Label();
            this.lblCompilationStatus.Text = "编译状态: 未测试";
            this.lblCompilationStatus.Size = new Size(300, 25);
            this.lblCompilationStatus.Location = new Point(20, 80);
            this.lblCompilationStatus.ForeColor = Color.Blue;

            // 创建运行时状态标签
            this.lblRuntimeStatus = new Label();
            this.lblRuntimeStatus.Text = "运行时状态: 未测试";
            this.lblRuntimeStatus.Size = new Size(300, 25);
            this.lblRuntimeStatus.Location = new Point(20, 110);
            this.lblRuntimeStatus.ForeColor = Color.Blue;

            // 创建日志文本框
            this.txtLog = new TextBox();
            this.txtLog.Multiline = true;
            this.txtLog.ScrollBars = ScrollBars.Vertical;
            this.txtLog.Location = new Point(20, 150);
            this.txtLog.Size = new Size(540, 300);
            this.txtLog.ReadOnly = true;
            this.txtLog.Font = new Font("Consolas", 9);

            // 窗体设置
            this.Text = "VisualizationForm编译和运行测试";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Controls.Add(this.btnTestCompilation);
            this.Controls.Add(this.btnTestRuntime);
            this.Controls.Add(this.lblCompilationStatus);
            this.Controls.Add(this.lblRuntimeStatus);
            this.Controls.Add(this.txtLog);

            this.ResumeLayout(false);
        }

        private void BtnTestCompilation_Click(object sender, EventArgs e)
        {
            try
            {
                LogMessage("=== 开始编译测试 ===");
                
                // 测试是否可以创建VisualizationForm类型
                Type visualFormType = typeof(VisualizationForm);
                LogMessage($"✅ VisualizationForm类型存在: {visualFormType.FullName}");

                // 检查构造函数
                var constructors = visualFormType.GetConstructors();
                LogMessage($"✅ 找到 {constructors.Length} 个构造函数");

                // 检查Designer.cs中的字段是否存在
                var fields = visualFormType.GetFields(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                LogMessage($"✅ 找到 {fields.Length} 个私有字段");

                // 检查关键控件字段
                string[] expectedControls = { "trackBarDepth", "chartPie", "panelStackedChart", "lblCurrentDepth" };
                int foundControls = 0;
                
                foreach (string controlName in expectedControls)
                {
                    var field = visualFormType.GetField(controlName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (field != null)
                    {
                        LogMessage($"✅ 找到控件字段: {controlName} ({field.FieldType.Name})");
                        foundControls++;
                    }
                    else
                    {
                        LogMessage($"❌ 缺少控件字段: {controlName}");
                    }
                }

                // 检查InitializeComponent方法
                var initMethod = visualFormType.GetMethod("InitializeComponent", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (initMethod != null)
                {
                    LogMessage($"✅ 找到InitializeComponent方法");
                }
                else
                {
                    LogMessage($"❌ 缺少InitializeComponent方法");
                }

                if (foundControls == expectedControls.Length && initMethod != null)
                {
                    lblCompilationStatus.Text = "编译状态: ✅ 通过";
                    lblCompilationStatus.ForeColor = Color.Green;
                    LogMessage("=== 编译测试通过 ===");
                }
                else
                {
                    lblCompilationStatus.Text = "编译状态: ❌ 失败";
                    lblCompilationStatus.ForeColor = Color.Red;
                    LogMessage("=== 编译测试失败 ===");
                }
            }
            catch (Exception ex)
            {
                lblCompilationStatus.Text = "编译状态: ❌ 异常";
                lblCompilationStatus.ForeColor = Color.Red;
                LogMessage($"❌ 编译测试异常: {ex.Message}");
                LogMessage($"异常类型: {ex.GetType().Name}");
                LogMessage($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        private void BtnTestRuntime_Click(object sender, EventArgs e)
        {
            try
            {
                LogMessage("=== 开始运行时测试 ===");

                // 创建测试数据
                DataTable testData = CreateTestData();
                LogMessage("✅ 测试数据创建成功");

                // 创建矿物列表
                List<string> brittleMinerals = new List<string> { "石英", "长石", "方解石" };
                List<string> ductileMinerals = new List<string> { "黏土", "伊利石" };
                LogMessage("✅ 矿物列表创建成功");

                // 尝试创建VisualizationForm实例
                LogMessage("正在创建VisualizationForm实例...");
                VisualizationForm visualForm = new VisualizationForm(testData, brittleMinerals, ductileMinerals);
                LogMessage("✅ VisualizationForm实例创建成功");

                // 检查关键控件是否已初始化
                var trackBarField = typeof(VisualizationForm).GetField("trackBarDepth", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (trackBarField != null)
                {
                    var trackBar = trackBarField.GetValue(visualForm);
                    if (trackBar != null)
                    {
                        LogMessage("✅ trackBarDepth已正确初始化");
                    }
                    else
                    {
                        LogMessage("❌ trackBarDepth为null");
                    }
                }

                var chartField = typeof(VisualizationForm).GetField("chartPie", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (chartField != null)
                {
                    var chart = chartField.GetValue(visualForm);
                    if (chart != null)
                    {
                        LogMessage("✅ chartPie已正确初始化");
                    }
                    else
                    {
                        LogMessage("❌ chartPie为null");
                    }
                }

                // 显示窗体
                LogMessage("正在显示VisualizationForm...");
                visualForm.Show();
                LogMessage("✅ VisualizationForm显示成功");

                lblRuntimeStatus.Text = "运行时状态: ✅ 通过";
                lblRuntimeStatus.ForeColor = Color.Green;
                LogMessage("=== 运行时测试通过 ===");
            }
            catch (Exception ex)
            {
                lblRuntimeStatus.Text = "运行时状态: ❌ 失败";
                lblRuntimeStatus.ForeColor = Color.Red;
                LogMessage($"❌ 运行时测试失败: {ex.Message}");
                LogMessage($"异常类型: {ex.GetType().Name}");
                LogMessage($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.ScrollToCaret();
            Application.DoEvents();
        }

        private DataTable CreateTestData()
        {
            DataTable data = new DataTable();

            // 添加列
            data.Columns.Add("GeoID", typeof(string));
            data.Columns.Add("顶深/m", typeof(double));
            data.Columns.Add("底深/m", typeof(double));
            data.Columns.Add("石英", typeof(double));
            data.Columns.Add("长石", typeof(double));
            data.Columns.Add("方解石", typeof(double));
            data.Columns.Add("黏土", typeof(double));
            data.Columns.Add("伊利石", typeof(double));
            data.Columns.Add("脆性指数", typeof(double));
            data.Columns.Add("脆性矿物总量", typeof(double));
            data.Columns.Add("塑性矿物总量", typeof(double));

            // 添加测试数据
            Random random = new Random();
            for (int i = 0; i < 5; i++)
            {
                double depth = 4700 + i * 50;
                DataRow row = data.NewRow();
                row["GeoID"] = $"Test_{i + 1:D3}";
                row["顶深/m"] = depth;
                row["底深/m"] = depth + 50;

                double quartz = random.NextDouble() * 30 + 20;
                double feldspar = random.NextDouble() * 20 + 10;
                double calcite = random.NextDouble() * 15 + 5;
                double clay = random.NextDouble() * 20 + 10;
                double illite = random.NextDouble() * 15 + 5;

                row["石英"] = Math.Round(quartz, 2);
                row["长石"] = Math.Round(feldspar, 2);
                row["方解石"] = Math.Round(calcite, 2);
                row["黏土"] = Math.Round(clay, 2);
                row["伊利石"] = Math.Round(illite, 2);

                double brittleTotal = quartz + feldspar + calcite;
                double ductileTotal = clay + illite;
                double brittlenessIndex = brittleTotal / (brittleTotal + ductileTotal) * 100;

                row["脆性矿物总量"] = Math.Round(brittleTotal, 2);
                row["塑性矿物总量"] = Math.Round(ductileTotal, 2);
                row["脆性指数"] = Math.Round(brittlenessIndex, 2);

                data.Rows.Add(row);
            }

            return data;
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestVisualizationFormCompilation());
        }
    }
}
