using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using Newtonsoft.Json;

namespace BritSystem
{
    /// <summary>
    /// 对比图显示窗体
    /// </summary>
    public partial class ComparisonChartForm : Form
    {
        private Chart chartComparison;
        private Button btnClose;
        private Button btnSaveImage;
        private Button btnSeparate;
        private Button btnRestore;
        private Label lblTitle;
        private Panel pnlControls;
        private bool isSeparated = false; // 标记是否处于分隔状态

        public ComparisonChartForm()
        {
            InitializeComponent();
            LoadComparisonData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 设置窗体属性
            this.Text = "脆性指数对比图";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(33, 33, 33);

            // 创建标题标签
            lblTitle = new Label();
            lblTitle.Text = "脆性指数计算方法对比图";
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.White;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 60;
            lblTitle.BackColor = Color.FromArgb(45, 45, 45);

            // 创建控制面板
            pnlControls = new Panel();
            pnlControls.Height = 60;
            pnlControls.Dock = DockStyle.Bottom;
            pnlControls.BackColor = Color.FromArgb(45, 45, 45);

            // 创建关闭按钮
            btnClose = new Button();
            btnClose.Text = "关闭";
            btnClose.Size = new Size(100, 35);
            btnClose.Location = new Point(20, 12);
            btnClose.BackColor = Color.FromArgb(50, 50, 50);
            btnClose.ForeColor = Color.White;
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.FlatAppearance.BorderColor = Color.Cyan;
            btnClose.Click += BtnClose_Click;

            // 创建保存图像按钮
            btnSaveImage = new Button();
            btnSaveImage.Text = "保存图像";
            btnSaveImage.Size = new Size(100, 35);
            btnSaveImage.Location = new Point(140, 12);
            btnSaveImage.BackColor = Color.FromArgb(50, 50, 50);
            btnSaveImage.ForeColor = Color.White;
            btnSaveImage.FlatStyle = FlatStyle.Flat;
            btnSaveImage.FlatAppearance.BorderColor = Color.Cyan;
            btnSaveImage.Click += BtnSaveImage_Click;

            // 创建分隔显示按钮
            btnSeparate = new Button();
            btnSeparate.Text = "分隔显示";
            btnSeparate.Size = new Size(100, 35);
            btnSeparate.Location = new Point(260, 12);
            btnSeparate.BackColor = Color.FromArgb(50, 50, 50);
            btnSeparate.ForeColor = Color.White;
            btnSeparate.FlatStyle = FlatStyle.Flat;
            btnSeparate.FlatAppearance.BorderColor = Color.Cyan;
            btnSeparate.Click += BtnSeparate_Click;

            // 创建还原显示按钮
            btnRestore = new Button();
            btnRestore.Text = "还原显示";
            btnRestore.Size = new Size(100, 35);
            btnRestore.Location = new Point(380, 12);
            btnRestore.BackColor = Color.FromArgb(50, 50, 50);
            btnRestore.ForeColor = Color.White;
            btnRestore.FlatStyle = FlatStyle.Flat;
            btnRestore.FlatAppearance.BorderColor = Color.Cyan;
            btnRestore.Click += BtnRestore_Click;
            btnRestore.Enabled = false; // 初始状态下禁用

            // 创建图表控件
            chartComparison = new Chart();
            chartComparison.Dock = DockStyle.Fill;
            chartComparison.BackColor = Color.FromArgb(60, 60, 60);

            // 创建图表区域
            ChartArea chartArea = new ChartArea("ComparisonArea");
            chartArea.BackColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisY.IsReversed = true; // Y轴反转，深度从上到下
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.Gray;
            chartArea.AxisY.MajorGrid.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dot;
            chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dot;

            // 设置坐标轴范围以便更好地对比数据
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 10;

            // 启用缩放和滚动
            chartArea.CursorX.IsUserEnabled = true;
            chartArea.CursorX.IsUserSelectionEnabled = true;
            chartArea.CursorY.IsUserEnabled = true;
            chartArea.CursorY.IsUserSelectionEnabled = true;
            chartArea.AxisX.ScaleView.Zoomable = true;
            chartArea.AxisY.ScaleView.Zoomable = true;
            chartArea.AxisX.ScrollBar.IsPositionedInside = true;
            chartArea.AxisY.ScrollBar.IsPositionedInside = true;

            chartComparison.ChartAreas.Add(chartArea);

            // 创建图例
            Legend legend = new Legend("ComparisonLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.Docking = Docking.Right;
            legend.Alignment = StringAlignment.Center;
            chartComparison.Legends.Add(legend);

            // 为图例添加点击事件以支持调色功能
            chartComparison.MouseClick += ChartComparison_MouseClick;

            // 添加控件到面板
            pnlControls.Controls.Add(btnClose);
            pnlControls.Controls.Add(btnSaveImage);
            pnlControls.Controls.Add(btnSeparate);
            pnlControls.Controls.Add(btnRestore);

            // 添加控件到窗体
            this.Controls.Add(chartComparison);
            this.Controls.Add(pnlControls);
            this.Controls.Add(lblTitle);

            this.ResumeLayout(false);
        }

        /// <summary>
        /// 加载对比数据
        /// </summary>
        private void LoadComparisonData()
        {
            try
            {
                // 检查临时文件是否存在
                string mineralDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_MineralogicalData.json");
                string staticDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");

                bool hasMineralData = File.Exists(mineralDataPath);
                bool hasStaticData = File.Exists(staticDataPath);

                if (!hasMineralData && !hasStaticData)
                {
                    MessageBox.Show("没有找到对比数据！请先在各个系统中保存图表数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 加载矿物组分法数据
                if (hasMineralData)
                {
                    LoadDataFromFile(mineralDataPath, Color.Blue, "矿物组分法");
                }

                // 加载静态岩石力学参数法数据
                if (hasStaticData)
                {
                    // 注意：静态岩石力学参数法的颜色将在LoadDataFromFile中设置为Cyan
                    LoadDataFromFile(staticDataPath, Color.Red, "静态岩石力学参数法");
                }

                // 显示加载结果
                string loadMessage = $"已加载 {chartComparison.Series.Count} 个系统的数据";
                if (chartComparison.Series.Count > 0)
                {
                    int totalPoints = chartComparison.Series.Sum(s => s.Points.Count);
                    loadMessage += $"，共 {totalPoints} 个数据点";
                }
                lblTitle.Text = $"脆性指数计算方法对比图 - {loadMessage}";

                // 如果没有数据，显示提示
                if (chartComparison.Series.Count == 0)
                {
                    lblTitle.Text = "脆性指数计算方法对比图 - 暂无数据";
                }

                // 如果有数据，调整图表显示
                if (chartComparison.Series.Count > 0)
                {
                    // 计算所有数据的深度范围
                    double minDepth = double.MaxValue;
                    double maxDepth = double.MinValue;

                    foreach (var series in chartComparison.Series)
                    {
                        foreach (var point in series.Points)
                        {
                            double depth = point.YValues[0]; // Y轴是深度
                            minDepth = Math.Min(minDepth, depth);
                            maxDepth = Math.Max(maxDepth, depth);
                        }
                    }

                    // 设置合适的深度范围，添加一些边距
                    if (minDepth != double.MaxValue && maxDepth != double.MinValue)
                    {
                        double depthRange = maxDepth - minDepth;
                        double margin = depthRange * 0.1; // 10%边距

                        chartComparison.ChartAreas[0].AxisY.Minimum = Math.Max(0, minDepth - margin);
                        chartComparison.ChartAreas[0].AxisY.Maximum = maxDepth + margin;

                        // 设置合适的间隔
                        double interval = depthRange / 10;
                        if (interval > 0)
                        {
                            chartComparison.ChartAreas[0].AxisY.Interval = Math.Max(1, Math.Round(interval));
                        }
                    }

                    chartComparison.ChartAreas[0].RecalculateAxesScale();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载对比数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 从文件加载数据
        /// </summary>
        private void LoadDataFromFile(string filePath, Color seriesColor, string seriesName)
        {
            try
            {
                string jsonContent = File.ReadAllText(filePath);
                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                if (data?.DataPoints != null)
                {
                    // 创建系列，根据系统类型设置不同样式
                    Series series = new Series(seriesName);

                    if (seriesName.Contains("静态岩石力学参数法"))
                    {
                        // 静态岩石力学参数法：使用平滑曲线，不显示数据点（与StaticRockMechanicsForm一致）
                        series.ChartType = SeriesChartType.Spline;
                        series.Color = Color.Cyan; // 使用青色，与原系统一致
                        series.BorderWidth = 2;
                        series.MarkerStyle = MarkerStyle.None; // 不显示数据点
                        series.MarkerSize = 0;
                    }
                    else
                    {
                        // 矿物组分法：使用带数据点的线条
                        series.ChartType = SeriesChartType.Line;
                        series.Color = seriesColor;
                        series.BorderWidth = 3;
                        series.MarkerStyle = MarkerStyle.Circle;
                        series.MarkerSize = 8;
                        series.MarkerColor = seriesColor;
                        series.MarkerBorderColor = Color.White;
                        series.MarkerBorderWidth = 1;
                    }

                    int validPointCount = 0;
                    double minDepth = double.MaxValue, maxDepth = double.MinValue;
                    double minBrittleness = double.MaxValue, maxBrittleness = double.MinValue;

                    // 添加数据点 - 确保坐标轴正确：X轴=脆性指数，Y轴=深度
                    foreach (var point in data.DataPoints)
                    {
                        double depth = Convert.ToDouble(point.TopDepth);
                        double brittleIndex = Convert.ToDouble(point.BrittleIndex);

                        // 确保脆性指数在合理范围内（0-100%）
                        if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                        {
                            series.Points.AddXY(brittleIndex, depth); // X=脆性指数, Y=深度
                            validPointCount++;

                            // 记录数据范围
                            minDepth = Math.Min(minDepth, depth);
                            maxDepth = Math.Max(maxDepth, depth);
                            minBrittleness = Math.Min(minBrittleness, brittleIndex);
                            maxBrittleness = Math.Max(maxBrittleness, brittleIndex);
                        }
                    }

                    if (validPointCount > 0)
                    {
                        chartComparison.Series.Add(series);
                        Console.WriteLine($"已加载 {seriesName}: {validPointCount} 个数据点");
                        Console.WriteLine($"  深度范围: {minDepth:F2} - {maxDepth:F2} m");
                        Console.WriteLine($"  脆性指数范围: {minBrittleness:F2} - {maxBrittleness:F2} %");
                    }
                    else
                    {
                        Console.WriteLine($"警告: {seriesName} 没有有效的数据点");
                    }
                }
                else
                {
                    Console.WriteLine($"警告: {filePath} 中没有找到 DataPoints");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件 {filePath} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Console.WriteLine($"加载文件错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 保存图像按钮点击事件
        /// </summary>
        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "PNG图像 (*.png)|*.png|JPEG图像 (*.jpg)|*.jpg";
                saveFileDialog.Title = "保存对比图";
                saveFileDialog.FileName = $"脆性指数对比图_{DateTime.Now:yyyyMMdd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    chartComparison.SaveImage(saveFileDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show("对比图已保存成功！", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图像时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 分隔显示按钮点击事件
        /// </summary>
        private void BtnSeparate_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartComparison.Series.Count < 2)
                {
                    MessageBox.Show("需要至少两个系统的数据才能进行分隔显示！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                SeparateChartAreas();
                isSeparated = true;
                btnSeparate.Enabled = false;
                btnRestore.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分隔显示时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 还原显示按钮点击事件
        /// </summary>
        private void BtnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                RestoreChartAreas();
                isSeparated = false;
                btnSeparate.Enabled = true;
                btnRestore.Enabled = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"还原显示时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// 分隔图表区域
        /// </summary>
        private void SeparateChartAreas()
        {
            // 清除现有的图表区域和标题
            chartComparison.ChartAreas.Clear();
            chartComparison.Titles.Clear();

            // 计算统一的深度范围
            var depthRange = CalculateUnifiedDepthRange();

            // 创建左侧图表区域（矿物组分法）
            ChartArea leftArea = new ChartArea("LeftArea");
            ConfigureChartAreaForSeparate(leftArea, "矿物组分法", depthRange);
            leftArea.Position = new ElementPosition(5, 10, 45, 80); // X, Y, Width, Height (百分比)
            chartComparison.ChartAreas.Add(leftArea);

            // 创建右侧图表区域（静态岩石力学参数法）
            ChartArea rightArea = new ChartArea("RightArea");
            ConfigureChartAreaForSeparate(rightArea, "静态岩石力学参数法", depthRange);
            rightArea.Position = new ElementPosition(50, 10, 45, 80);
            chartComparison.ChartAreas.Add(rightArea);

            // 重新分配系列到对应的图表区域
            foreach (var series in chartComparison.Series)
            {
                if (series.Name.Contains("矿物组分法"))
                {
                    series.ChartArea = "LeftArea";
                }
                else if (series.Name.Contains("静态岩石力学参数法"))
                {
                    series.ChartArea = "RightArea";
                }
            }

            // 调整图例位置
            if (chartComparison.Legends.Count > 0)
            {
                chartComparison.Legends[0].Position = new ElementPosition(96, 10, 4, 80);
            }
        }

        /// <summary>
        /// 还原图表区域
        /// </summary>
        private void RestoreChartAreas()
        {
            // 清除现有的图表区域和标题
            chartComparison.ChartAreas.Clear();
            chartComparison.Titles.Clear();

            // 创建单一图表区域
            ChartArea chartArea = new ChartArea("ComparisonArea");
            ConfigureChartArea(chartArea, "");
            chartComparison.ChartAreas.Add(chartArea);

            // 将所有系列分配到同一个图表区域
            foreach (var series in chartComparison.Series)
            {
                series.ChartArea = "ComparisonArea";
            }

            // 恢复图例位置
            if (chartComparison.Legends.Count > 0)
            {
                chartComparison.Legends[0].Position = new ElementPosition();
                chartComparison.Legends[0].Docking = Docking.Right;
            }

            // 重新计算坐标轴范围
            RecalculateAxisRanges();
        }
        /// <summary>
        /// 配置图表区域
        /// </summary>
        private void ConfigureChartArea(ChartArea chartArea, string title)
        {
            chartArea.BackColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisY.IsReversed = true; // Y轴反转，深度从上到下
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.Gray;
            chartArea.AxisY.MajorGrid.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dot;
            chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dot;

            // 设置坐标轴范围
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 10;

            // 启用缩放和滚动
            chartArea.CursorX.IsUserEnabled = true;
            chartArea.CursorX.IsUserSelectionEnabled = true;
            chartArea.CursorY.IsUserEnabled = true;
            chartArea.CursorY.IsUserSelectionEnabled = true;
            chartArea.AxisX.ScaleView.Zoomable = true;
            chartArea.AxisY.ScaleView.Zoomable = true;
            chartArea.AxisX.ScrollBar.IsPositionedInside = true;
            chartArea.AxisY.ScrollBar.IsPositionedInside = true;

            // 添加标题
            if (!string.IsNullOrEmpty(title))
            {
                Title chartTitle = new Title(title);
                chartTitle.ForeColor = Color.White;
                chartTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
                chartTitle.DockedToChartArea = chartArea.Name;
                chartTitle.IsDockedInsideChartArea = false;
                chartTitle.Docking = Docking.Top;
                chartComparison.Titles.Add(chartTitle);
            }
        }

        /// <summary>
        /// 配置分隔模式的图表区域
        /// </summary>
        private void ConfigureChartAreaForSeparate(ChartArea chartArea, string title, (double min, double max) depthRange)
        {
            chartArea.BackColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisY.IsReversed = true; // Y轴反转，深度从上到下
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.Gray;
            chartArea.AxisY.MajorGrid.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dot;
            chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dot;

            // 设置X轴范围
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 10;

            // 设置统一的Y轴范围（基于实际数据范围）
            chartArea.AxisY.Minimum = depthRange.min;
            chartArea.AxisY.Maximum = depthRange.max;

            // 设置合适的Y轴间隔
            double depthSpan = depthRange.max - depthRange.min;
            double interval = Math.Max(1, Math.Round(depthSpan / 10));
            chartArea.AxisY.Interval = interval;

            // 启用缩放和滚动
            chartArea.CursorX.IsUserEnabled = true;
            chartArea.CursorX.IsUserSelectionEnabled = true;
            chartArea.CursorY.IsUserEnabled = true;
            chartArea.CursorY.IsUserSelectionEnabled = true;
            chartArea.AxisX.ScaleView.Zoomable = true;
            chartArea.AxisY.ScaleView.Zoomable = true;
            chartArea.AxisX.ScrollBar.IsPositionedInside = true;
            chartArea.AxisY.ScrollBar.IsPositionedInside = true;

            // 添加标题
            if (!string.IsNullOrEmpty(title))
            {
                Title chartTitle = new Title(title);
                chartTitle.ForeColor = Color.White;
                chartTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
                chartTitle.DockedToChartArea = chartArea.Name;
                chartTitle.IsDockedInsideChartArea = false;
                chartTitle.Docking = Docking.Top;
                chartComparison.Titles.Add(chartTitle);
            }
        }

        /// <summary>
        /// 计算统一的深度范围（以数据范围更大的系统为准）
        /// </summary>
        private (double min, double max) CalculateUnifiedDepthRange()
        {
            double globalMinDepth = double.MaxValue;
            double globalMaxDepth = double.MinValue;

            // 分别计算每个系统的深度范围
            var mineralRange = CalculateSeriesDepthRange("矿物组分法");
            var staticRange = CalculateSeriesDepthRange("静态岩石力学参数法");

            // 选择范围更大的系统作为基准
            double mineralSpan = mineralRange.max - mineralRange.min;
            double staticSpan = staticRange.max - staticRange.min;

            (double min, double max) baseRange;
            if (mineralSpan >= staticSpan)
            {
                baseRange = mineralRange;
                Console.WriteLine($"选择矿物组分法的深度范围作为基准: {mineralRange.min:F2} - {mineralRange.max:F2} m (范围: {mineralSpan:F2} m)");
            }
            else
            {
                baseRange = staticRange;
                Console.WriteLine($"选择静态岩石力学参数法的深度范围作为基准: {staticRange.min:F2} - {staticRange.max:F2} m (范围: {staticSpan:F2} m)");
            }

            // 如果某个系统没有数据，使用另一个系统的范围
            if (baseRange.min == double.MaxValue || baseRange.max == double.MinValue)
            {
                if (mineralRange.min != double.MaxValue && mineralRange.max != double.MinValue)
                {
                    baseRange = mineralRange;
                }
                else if (staticRange.min != double.MaxValue && staticRange.max != double.MinValue)
                {
                    baseRange = staticRange;
                }
                else
                {
                    // 如果都没有数据，使用默认范围
                    baseRange = (0, 1000);
                }
            }

            // 添加10%的边距
            double depthSpan = baseRange.max - baseRange.min;
            double margin = depthSpan * 0.1;

            return (Math.Max(0, baseRange.min - margin), baseRange.max + margin);
        }

        /// <summary>
        /// 计算指定系列的深度范围
        /// </summary>
        private (double min, double max) CalculateSeriesDepthRange(string seriesNamePattern)
        {
            double minDepth = double.MaxValue;
            double maxDepth = double.MinValue;

            foreach (var series in chartComparison.Series)
            {
                if (series.Name.Contains(seriesNamePattern))
                {
                    foreach (var point in series.Points)
                    {
                        double depth = point.YValues[0]; // Y轴是深度
                        minDepth = Math.Min(minDepth, depth);
                        maxDepth = Math.Max(maxDepth, depth);
                    }
                }
            }

            return (minDepth, maxDepth);
        }

        /// <summary>
        /// 重新计算坐标轴范围
        /// </summary>
        private void RecalculateAxisRanges()
        {
            if (chartComparison.Series.Count > 0)
            {
                // 计算所有数据的深度范围
                double minDepth = double.MaxValue;
                double maxDepth = double.MinValue;

                foreach (var series in chartComparison.Series)
                {
                    foreach (var point in series.Points)
                    {
                        double depth = point.YValues[0]; // Y轴是深度
                        minDepth = Math.Min(minDepth, depth);
                        maxDepth = Math.Max(maxDepth, depth);
                    }
                }

                // 设置合适的深度范围，添加一些边距
                if (minDepth != double.MaxValue && maxDepth != double.MinValue)
                {
                    double depthRange = maxDepth - minDepth;
                    double margin = depthRange * 0.1; // 10%边距

                    foreach (ChartArea area in chartComparison.ChartAreas)
                    {
                        area.AxisY.Minimum = Math.Max(0, minDepth - margin);
                        area.AxisY.Maximum = maxDepth + margin;

                        // 设置合适的间隔
                        double interval = depthRange / 10;
                        if (interval > 0)
                        {
                            area.AxisY.Interval = Math.Max(1, Math.Round(interval));
                        }
                        area.RecalculateAxesScale();
                    }
                }
            }
        }
        /// <summary>
        /// 图表鼠标点击事件 - 用于图例调色功能
        /// </summary>
        private void ChartComparison_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                // 检查是否点击在图例区域
                HitTestResult result = chartComparison.HitTest(e.X, e.Y);

                if (result.ChartElementType == ChartElementType.LegendItem)
                {
                    LegendItem legendItem = result.Object as LegendItem;
                    if (legendItem != null)
                    {
                        // 找到对应的系列
                        Series targetSeries = null;
                        foreach (var series in chartComparison.Series)
                        {
                            if (series.Name == legendItem.SeriesName)
                            {
                                targetSeries = series;
                                break;
                            }
                        }

                        if (targetSeries != null)
                        {
                            // 显示颜色选择对话框
                            ColorDialog colorDialog = new ColorDialog();
                            colorDialog.Color = targetSeries.Color;
                            colorDialog.FullOpen = true;

                            if (colorDialog.ShowDialog() == DialogResult.OK)
                            {
                                // 更新系列颜色
                                targetSeries.Color = colorDialog.Color;

                                // 如果是矿物组分法，还需要更新标记颜色
                                if (targetSeries.Name.Contains("矿物组分法"))
                                {
                                    targetSeries.MarkerColor = colorDialog.Color;
                                }

                                // 刷新图表
                                chartComparison.Invalidate();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"调色功能出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
