#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能搜索助手
为用户提供优化的搜索建议和自动化搜索
"""

import os
import time
import webbrowser
import tkinter as tk
from tkinter import messagebox
from typing import List, Dict
from loguru import logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
except ImportError as e:
    logger.error(f"Selenium导入失败: {e}")


class SmartSearchHelper:
    """智能搜索助手"""
    
    def __init__(self, custom_browser_path: str = None):
        self.custom_browser_path = custom_browser_path
        self.driver = None
    
    def create_browser(self):
        """创建浏览器实例"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 如果用户指定了自定义浏览器路径，使用它
            if self.custom_browser_path and os.path.exists(self.custom_browser_path):
                chrome_options.binary_location = self.custom_browser_path
                logger.info(f"使用自定义浏览器: {self.custom_browser_path}")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 执行脚本隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("浏览器创建成功")
            return True
            
        except Exception as e:
            logger.error(f"创建浏览器失败: {e}")
            return False
    
    def open_wos_with_search_ready(self, citations: List[Dict[str, str]]) -> bool:
        """打开WOS并准备搜索"""
        if not self.create_browser():
            return False
        
        try:
            # 打开Web of Science
            self.driver.get("https://webofscience.clarivate.cn")
            time.sleep(3)
            
            # 显示搜索建议窗口
            self.show_search_suggestions(citations)
            
            return True
            
        except Exception as e:
            logger.error(f"打开WOS失败: {e}")
            return False
    
    def show_search_suggestions(self, citations: List[Dict[str, str]]):
        """显示搜索建议窗口"""
        # 创建搜索建议窗口
        suggestion_window = tk.Toplevel()
        suggestion_window.title("搜索建议")
        suggestion_window.geometry("800x600")
        suggestion_window.grab_set()  # 模态窗口
        
        # 创建文本框显示搜索建议
        import tkinter.scrolledtext as scrolledtext
        text_widget = scrolledtext.ScrolledText(suggestion_window, wrap=tk.WORD, width=80, height=30)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 生成搜索建议内容
        suggestions = self.generate_search_suggestions(citations)
        text_widget.insert(tk.END, suggestions)
        text_widget.config(state=tk.DISABLED)
        
        # 添加按钮
        button_frame = tk.Frame(suggestion_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(button_frame, text="开始手动搜索", 
                 command=lambda: self.start_manual_search(suggestion_window, citations)).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="关闭", 
                 command=suggestion_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def generate_search_suggestions(self, citations: List[Dict[str, str]]) -> str:
        """生成搜索建议"""
        suggestions = "🔍 Web of Science 搜索建议\n"
        suggestions += "=" * 50 + "\n\n"
        suggestions += "以下是为您准备的搜索关键词，请在Web of Science中逐个搜索：\n\n"
        
        for i, citation in enumerate(citations, 1):
            ref_num = citation.get('reference_number', str(i))
            title = citation.get('title', '').strip()
            authors = citation.get('authors', '')
            year = citation.get('year', '')
            
            suggestions += f"📄 文献 [{ref_num}]\n"
            suggestions += "-" * 20 + "\n"
            
            # 标题搜索建议
            if title and title != '未识别' and len(title) > 10:
                # 提取关键词
                title_words = title.replace('"', '').replace("'", "").split()
                key_words = [word for word in title_words if len(word) > 3][:5]
                
                suggestions += f"🎯 推荐搜索方式1 - 标题关键词：\n"
                suggestions += f"   {' '.join(key_words)}\n\n"
                
                suggestions += f"🎯 推荐搜索方式2 - 完整标题：\n"
                suggestions += f"   \"{title}\"\n\n"
            
            # 作者搜索建议
            if authors and authors != '未识别':
                if isinstance(authors, list):
                    first_author = authors[0] if authors else ""
                else:
                    first_author = authors.split(',')[0].strip()
                
                suggestions += f"👤 作者搜索：\n"
                suggestions += f"   {first_author}\n\n"
            
            # 组合搜索建议
            if title and authors and year:
                suggestions += f"🔗 组合搜索：\n"
                if isinstance(authors, list):
                    first_author = authors[0] if authors else ""
                else:
                    first_author = authors.split(',')[0].strip()
                
                key_words = title.split()[:3] if title != '未识别' else []
                if key_words:
                    suggestions += f"   {' '.join(key_words)} {first_author} {year}\n\n"
            
            suggestions += f"📋 原始信息：\n"
            suggestions += f"   标题: {title}\n"
            suggestions += f"   作者: {authors}\n"
            suggestions += f"   年份: {year}\n"
            suggestions += "\n" + "="*50 + "\n\n"
        
        suggestions += "💡 搜索技巧：\n"
        suggestions += "1. 先尝试标题关键词搜索，结果更准确\n"
        suggestions += "2. 如果结果太多，加上作者名字缩小范围\n"
        suggestions += "3. 如果结果太少，减少关键词数量\n"
        suggestions += "4. 找到正确文献后，点击进入详情页\n"
        suggestions += "5. 在详情页点击 '导出' → 'EndNote Desktop' 下载引用\n\n"
        
        return suggestions
    
    def start_manual_search(self, window, citations):
        """开始手动搜索"""
        window.destroy()
        
        # 显示提示
        messagebox.showinfo("开始搜索", 
                           "搜索建议已准备完毕！\n\n"
                           "请在打开的Web of Science页面中：\n"
                           "1. 使用建议的关键词进行搜索\n"
                           "2. 找到正确的文献\n"
                           "3. 进入详情页下载EndNote引用\n\n"
                           "祝您搜索顺利！")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
