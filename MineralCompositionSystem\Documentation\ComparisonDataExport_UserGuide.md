# 对比图数据导出功能使用说明

## 功能概述

增强版的"存为对比图"功能提供了多种数据导出选项，支持将脆性指数数据导出到不同的设备和位置，方便与其他系统进行数据对接。

## 主要特性

### 1. 多种导出格式
- **JSON格式** (推荐): 结构化数据，易于程序读取
- **Excel格式** (.xlsx): 适合数据分析和报表制作
- **CSV格式**: 通用的表格数据格式
- **XML格式**: 标准化的数据交换格式

### 2. 灵活的输出位置
- **本地文件**: 保存到本地硬盘
- **可移动设备**: 保存到U盘、移动硬盘等
- **网络位置**: 保存到网络共享文件夹
- **自定义路径**: 指定任意有效路径

### 3. 导出选项
- **包含元数据**: 添加系统信息、导出时间等
- **压缩输出**: 将导出文件压缩为ZIP格式
- **导出后打开**: 自动打开导出文件或文件夹
- **显示数据预览**: 导出前预览数据内容

## 使用方法

### 基本使用

1. **生成脆性指数图表**
   - 导入矿物数据
   - 选择脆性和塑性矿物
   - 点击"计算脆性指数"
   - 点击"生成曲线"

2. **导出对比数据**
   - 点击"存为对比图"按钮
   - 在弹出的对话框中选择导出选项
   - 点击"开始导出"

### 高级使用

#### 右键菜单快速操作
- **右键点击**"存为对比图"按钮，显示快速操作菜单：
  - **增强导出 (推荐)**: 打开完整的导出对话框
  - **快速导出JSON**: 直接导出JSON格式到桌面
  - **快速导出Excel**: 直接导出Excel格式到桌面
  - **传统导出 (兼容模式)**: 使用原有的导出方式

#### 批量导出
1. 在导出对话框中勾选"同时导出多种格式"
2. 系统将自动导出JSON、Excel、CSV、XML四种格式
3. 所有文件保存在同一个文件夹中

#### 数据预览
1. 在导出对话框中勾选"显示数据预览"
2. 点击"预览数据"按钮
3. 查看表格视图和JSON格式预览
4. 确认数据无误后再进行导出

## 导出文件说明

### JSON格式文件结构
```json
{
  "Metadata": {
    "SystemName": "矿物组分法",
    "ExportTime": "2024-01-01 12:00:00",
    "DataCount": 50,
    "ExportVersion": "1.0",
    "Description": "脆性指数对比图数据"
  },
  "DataPoints": [
    {
      "TopDepth": 100.0,
      "BottomDepth": 105.0,
      "BrittleIndex": 65.5,
      "SystemName": "矿物组分法",
      "ExportTime": "2024-01-01 12:00:00"
    }
  ]
}
```

### Excel文件结构
- **对比数据工作表**: 包含序号、顶深、底深、脆性指数、数据来源等列
- **元数据工作表**: 包含导出信息和系统描述

### CSV文件结构
- 第一行为列标题
- 如果包含元数据，文件开头会有注释行（以#开头）
- 数据行包含：序号,顶深(m),底深(m),脆性指数(%),数据来源

## 设备输出配置

### 可移动设备检测
系统会自动检测已连接的可移动存储设备（U盘、移动硬盘等），并显示可用空间信息。

### 网络位置配置
支持以下网络路径格式：
- Windows共享: `\\server\share\folder`
- 映射网络驱动器: `Z:\folder`
- UNC路径: `\\*************\shared`

### 自定义路径
可以指定任何有效的文件系统路径，系统会验证路径的可访问性。

## 故障排除

### 常见问题

1. **导出失败**
   - 检查目标路径是否存在且有写入权限
   - 确保磁盘空间充足
   - 检查文件名是否包含非法字符

2. **网络位置无法访问**
   - 验证网络连接
   - 检查共享权限设置
   - 确认用户有访问权限

3. **可移动设备未检测到**
   - 重新插拔设备
   - 检查设备是否正常工作
   - 确认设备已正确格式化

### 错误代码说明

- **路径不存在**: 指定的输出路径无效
- **权限不足**: 没有写入目标位置的权限
- **磁盘空间不足**: 目标位置剩余空间不够
- **文件被占用**: 目标文件正在被其他程序使用

## 兼容性说明

### 向后兼容
- 保留原有的临时文件导出功能
- 支持传统的JSON格式
- 其他系统可以继续读取原有格式的数据

### 数据格式版本
- **版本1.0**: 基础数据结构
- **版本2.0**: 增强版，包含更多元数据信息

## 技术支持

如果在使用过程中遇到问题，请：
1. 查看系统日志文件
2. 检查导出文件的完整性
3. 验证目标系统的数据读取能力
4. 联系技术支持团队

---

**注意**: 导出的数据文件包含重要的地质分析信息，请妥善保管并确保数据安全。
