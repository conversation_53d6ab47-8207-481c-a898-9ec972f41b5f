using System;
using System.Windows.Forms;
using System.Drawing;

namespace StaticRockMechanicsSystem.Forms
{
    public class DashboardForm : Form
    {
        private Label lblTitle;
        private Button btnStaticRockMechanics;
        private Button btnExit;
        private string username;

        public DashboardForm(string username)
        {
            this.username = username;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            lblTitle = new Label();
            btnStaticRockMechanics = new Button();
            btnExit = new Button();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Padding = new Padding(0, 10, 0, 0);
            lblTitle.Size = new Size(578, 73);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "静态岩石力学参数法脆性指数系统V1.0";
            lblTitle.TextAlign = ContentAlignment.TopCenter;
            // 
            // btnStaticRockMechanics
            // 
            btnStaticRockMechanics.BackColor = Color.FromArgb(50, 50, 50);
            btnStaticRockMechanics.FlatAppearance.BorderColor = Color.Cyan;
            btnStaticRockMechanics.FlatStyle = FlatStyle.Flat;
            btnStaticRockMechanics.Font = new Font("微软雅黑", 12F);
            btnStaticRockMechanics.ForeColor = Color.LightSkyBlue;
            btnStaticRockMechanics.Location = new Point(199, 100);
            btnStaticRockMechanics.Name = "btnStaticRockMechanics";
            btnStaticRockMechanics.Size = new Size(180, 120);
            btnStaticRockMechanics.TabIndex = 1;
            btnStaticRockMechanics.Text = "开始分析";
            btnStaticRockMechanics.UseVisualStyleBackColor = false;
            btnStaticRockMechanics.Click += BtnStaticRockMechanics_Click;
            // 
            // btnExit
            // 
            btnExit.BackColor = Color.FromArgb(60, 60, 60);
            btnExit.FlatAppearance.BorderColor = Color.Cyan;
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.Font = new Font("微软雅黑", 10F);
            btnExit.ForeColor = Color.White;
            btnExit.Location = new Point(250, 250);
            btnExit.Name = "btnExit";
            btnExit.Size = new Size(114, 40);
            btnExit.TabIndex = 3;
            btnExit.Text = "退出系统";
            btnExit.UseVisualStyleBackColor = false;
            btnExit.Click += BtnExit_Click;
            // 
            // DashboardForm
            // 
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(578, 344);
            Controls.Add(lblTitle);
            Controls.Add(btnStaticRockMechanics);
            Controls.Add(btnExit);
            Name = "DashboardForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "静态岩石力学参数法脆性指数系统V1.0";
            ResumeLayout(false);
        }

        private void LoadData()
        {
            // 简化版本，不需要加载数据
        }

        private void BtnStaticRockMechanics_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开静态岩石力学参数法分析窗体
                StaticRockMechanicsForm staticForm = new StaticRockMechanicsForm(username);
                staticForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开静态岩石力学参数法分析窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            // 退出系统
            Application.Exit();
        }
    }
}
