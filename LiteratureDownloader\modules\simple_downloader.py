#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的文献下载器
直接操作用户已打开的浏览器页面
"""

import os
import time
import webbrowser
from typing import List, Dict
from loguru import logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.keys import Keys
except ImportError as e:
    logger.error(f"Selenium导入失败: {e}")


class SimpleDownloader:
    """简单文献下载器"""

    def __init__(self, download_dir: str, custom_browser_path: str = None):
        self.download_dir = download_dir
        self.custom_browser_path = custom_browser_path
        self.driver = None
    
    def connect_to_existing_browser(self):
        """连接到已存在的Chrome浏览器"""
        try:
            # 连接到已打开的Chrome浏览器（需要用调试端口启动）
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            logger.info("成功连接到现有浏览器")
            return True
            
        except Exception as e:
            logger.warning(f"无法连接到现有浏览器: {e}")
            return False
    
    def create_new_browser(self):
        """创建新的浏览器实例"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--remote-debugging-port=9222")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-extensions")

            # 设置下载目录
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 如果用户指定了自定义浏览器路径，使用它
            if self.custom_browser_path and os.path.exists(self.custom_browser_path):
                chrome_options.binary_location = self.custom_browser_path
                logger.info(f"使用自定义浏览器: {self.custom_browser_path}")

            self.driver = webdriver.Chrome(options=chrome_options)
            logger.info("创建新浏览器成功")
            return True

        except Exception as e:
            logger.error(f"创建浏览器失败: {e}")
            return False
    
    def open_wos_and_search(self, citation: Dict[str, str]) -> bool:
        """打开WOS并搜索文献"""
        try:
            ref_num = citation.get('reference_number', '?')
            title = citation.get('title', '').strip()
            authors = citation.get('authors', '')
            
            # 检查信息是否足够
            if (not title or title == '未识别') and (not authors or authors == '未识别'):
                logger.warning(f"跳过文献 [{ref_num}]: 信息不足")
                return False
            
            # 打开Web of Science
            self.driver.get("https://webofscience.clarivate.cn/wos/alldb/smart-search")
            time.sleep(3)
            
            # 构建搜索查询
            if title and title != '未识别' and len(title) > 10:
                search_query = f'TI="{title}"'
                search_type = "标题"
            elif authors and authors != '未识别':
                if isinstance(authors, list):
                    authors = ', '.join(authors[:2])
                search_query = f'AU="{authors}"'
                search_type = "作者"
            else:
                return False
            
            logger.info(f"搜索文献 [{ref_num}] - {search_type}: {search_query}")
            
            # 查找搜索框并输入
            try:
                search_box = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        'input[data-ta="search-input"], textarea[data-ta="search-input"]'))
                )
                
                search_box.clear()
                search_box.send_keys(search_query)
                search_box.send_keys(Keys.RETURN)
                
                time.sleep(5)
                logger.info(f"文献 [{ref_num}] 搜索完成")
                return True
                
            except Exception as e:
                logger.error(f"搜索失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"打开WOS失败: {e}")
            return False
    
    def download_endnote_from_current_page(self, ref_num: str) -> bool:
        """从当前页面下载EndNote引用"""
        try:
            logger.info(f"开始下载文献 [{ref_num}] 的EndNote引用")
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找并点击第一个搜索结果
            try:
                first_result = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 
                        'a[data-ta="result-title-link"], .search-results-item a, h3 a'))
                )
                first_result.click()
                time.sleep(5)
                logger.info(f"文献 [{ref_num}] 进入详情页")
                
            except Exception as e:
                logger.warning(f"无法点击搜索结果: {e}")
                return False
            
            # 查找导出按钮
            export_selectors = [
                'button[data-ta="export-button"]',
                'button[title*="导出"]',
                'button[title*="Export"]',
                '.export-button'
            ]
            
            export_button = None
            for selector in export_selectors:
                try:
                    export_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if not export_button:
                # 尝试通过文本查找
                try:
                    export_button = self.driver.find_element(By.XPATH, 
                        "//button[contains(text(), '导出') or contains(text(), 'Export')]")
                except:
                    logger.warning(f"文献 [{ref_num}] 找不到导出按钮")
                    return False
            
            # 点击导出按钮
            self.driver.execute_script("arguments[0].click();", export_button)
            time.sleep(2)
            logger.info(f"文献 [{ref_num}] 点击导出按钮")
            
            # 查找EndNote选项
            endnote_selectors = [
                'a[title*="EndNote Desktop"]',
                'button[title*="EndNote Desktop"]',
                'a[data-value="endnote"]',
                '.endnote-desktop'
            ]
            
            endnote_option = None
            for selector in endnote_selectors:
                try:
                    endnote_option = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if not endnote_option:
                # 尝试通过文本查找
                try:
                    endnote_option = self.driver.find_element(By.XPATH, 
                        "//a[contains(text(), 'EndNote') or contains(@title, 'EndNote')]")
                except:
                    logger.warning(f"文献 [{ref_num}] 找不到EndNote选项")
                    return False
            
            # 点击EndNote选项
            self.driver.execute_script("arguments[0].click();", endnote_option)
            time.sleep(3)
            logger.info(f"文献 [{ref_num}] EndNote引用下载完成")
            
            return True
            
        except Exception as e:
            logger.error(f"下载EndNote引用失败: {e}")
            return False
    
    def batch_download_simple(self, citations: List[Dict[str, str]]) -> Dict[str, int]:
        """简单批量下载"""
        results = {
            'total': len(citations),
            'processed': 0,
            'downloaded': 0,
            'skipped': 0,
            'failed': 0
        }
        
        # 尝试连接现有浏览器，如果失败则创建新的
        if not self.connect_to_existing_browser():
            if not self.create_new_browser():
                logger.error("无法创建或连接浏览器")
                return results
        
        try:
            for citation in citations:
                ref_num = citation.get('reference_number', '?')
                
                # 检查信息是否足够
                title = citation.get('title', '').strip()
                authors = citation.get('authors', '')
                
                if (not title or title == '未识别') and (not authors or authors == '未识别'):
                    results['skipped'] += 1
                    logger.info(f"跳过文献 [{ref_num}]: 信息不足")
                    continue
                
                results['processed'] += 1
                
                # 搜索文献
                if self.open_wos_and_search(citation):
                    # 下载EndNote引用
                    if self.download_endnote_from_current_page(ref_num):
                        results['downloaded'] += 1
                        logger.info(f"文献 [{ref_num}] 下载成功")
                    else:
                        results['failed'] += 1
                        logger.warning(f"文献 [{ref_num}] 下载失败")
                else:
                    results['failed'] += 1
                    logger.warning(f"文献 [{ref_num}] 搜索失败")
                
                # 每个文献之间稍作停顿
                time.sleep(2)
        
        except Exception as e:
            logger.error(f"批量下载过程出错: {e}")
        
        return results
    
    def open_urls_for_manual_download(self, citations: List[Dict[str, str]]) -> List[str]:
        """为手动下载打开URL"""
        urls = []
        
        for citation in citations:
            ref_num = citation.get('reference_number', '?')
            title = citation.get('title', '').strip()
            authors = citation.get('authors', '')
            
            # 检查信息是否足够
            if (not title or title == '未识别') and (not authors or authors == '未识别'):
                logger.info(f"跳过文献 [{ref_num}]: 信息不足")
                continue
            
            # 构建搜索URL
            if title and title != '未识别' and len(title) > 10:
                search_query = f'TI="{title}"'
            elif authors and authors != '未识别':
                if isinstance(authors, list):
                    authors = ', '.join(authors[:2])
                search_query = f'AU="{authors}"'
            else:
                continue
            
            # 编码搜索查询
            import urllib.parse
            encoded_query = urllib.parse.quote(search_query)
            url = f"https://webofscience.clarivate.cn/wos/alldb/smart-search?query={encoded_query}"
            
            urls.append(url)
            
            # 打开URL
            webbrowser.open(url)
            logger.info(f"已打开文献 [{ref_num}] 的搜索页面")
            
            # 避免同时打开太多页面
            time.sleep(1)
        
        return urls
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
