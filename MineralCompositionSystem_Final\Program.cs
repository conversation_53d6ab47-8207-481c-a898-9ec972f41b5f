using System;
using System.Windows.Forms;
using MineralCompositionSystem.Core;
using MineralCompositionSystem.Services;
using MineralCompositionSystem.Forms;

namespace MineralCompositionSystem
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 使用现代的应用程序配置初始化方法，自动处理高DPI设置
            ApplicationConfiguration.Initialize();

            // 设置默认字体以确保在高DPI下显示清晰
            Application.SetDefaultFont(new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point));

            try
            {
                // 初始化日志服务
                LoggingService.Instance.Info("矿物组分法脆性指数分析系统启动");

                // 创建并运行登录窗体
                bool loginSuccess;
                string username;
                DialogResult loginResult = ShowLoginForm(out loginSuccess, out username);

                if (loginSuccess)
                {
                    // 登录成功，显示仪表盘
                    Application.Run(new DashboardForm(username));
                }
            }
            catch (Exception ex)
            {
                // 记录未处理的异常
                LoggingService.Instance.Exception(ex, "应用程序发生未处理的异常");

                // 向用户显示友好的错误消息
                MessageBox.Show(
                    "应用程序发生了未知错误，即将关闭。请联系技术支持。\n\n错误详情已保存到日志文件中。",
                    "应用程序错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                // 记录应用程序关闭
                LoggingService.Instance.Info("矿物组分法脆性指数分析系统关闭");
            }
        }

        /// <summary>
        /// 显示登录窗体
        /// </summary>
        /// <param name="loginSuccess">登录是否成功</param>
        /// <param name="username">用户名</param>
        /// <returns>对话框结果</returns>
        private static DialogResult ShowLoginForm(out bool loginSuccess, out string username)
        {
            loginSuccess = false;
            username = string.Empty;

            using (var loginForm = new LoginForm())
            {
                DialogResult result = loginForm.ShowDialog();
                if (result == DialogResult.OK)
                {
                    loginSuccess = true;
                    username = "admin"; // 简化的用户名
                }
                return result;
            }
        }
    }
}
