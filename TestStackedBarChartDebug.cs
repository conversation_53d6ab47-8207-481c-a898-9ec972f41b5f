using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using BritSystem.Controls;

namespace BritSystem
{
    /// <summary>
    /// 测试堆叠柱状图的调试版本 - 输出详细日志到VS输出窗口
    /// </summary>
    public partial class TestStackedBarChartDebug : Form
    {
        private MineralStackedBarChartControl stackedChart;
        private Button btnLoadData;
        private Label lblStatus;
        private TextBox txtDebugInfo;

        public TestStackedBarChartDebug()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 创建加载数据按钮
            this.btnLoadData = new Button();
            this.btnLoadData.Text = "加载测试数据并查看VS输出";
            this.btnLoadData.Size = new Size(200, 40);
            this.btnLoadData.Location = new Point(20, 20);
            this.btnLoadData.Click += BtnLoadData_Click;

            // 创建状态标签
            this.lblStatus = new Label();
            this.lblStatus.Text = "点击按钮加载数据，然后查看VS的输出窗口...";
            this.lblStatus.Size = new Size(500, 25);
            this.lblStatus.Location = new Point(240, 30);
            this.lblStatus.ForeColor = Color.Blue;

            // 创建调试信息文本框
            this.txtDebugInfo = new TextBox();
            this.txtDebugInfo.Multiline = true;
            this.txtDebugInfo.ScrollBars = ScrollBars.Vertical;
            this.txtDebugInfo.Location = new Point(20, 80);
            this.txtDebugInfo.Size = new Size(760, 150);
            this.txtDebugInfo.ReadOnly = true;
            this.txtDebugInfo.Font = new Font("Consolas", 9);
            this.txtDebugInfo.Text = "调试信息:\n" +
                                   "1. 点击按钮加载测试数据\n" +
                                   "2. 查看VS的输出窗口 (视图 -> 输出)\n" +
                                   "3. 在输出窗口的'显示输出来源'中选择'调试'或'常规'\n" +
                                   "4. 观察详细的日志信息，特别关注:\n" +
                                   "   - 🔧 X轴和Y轴的设置\n" +
                                   "   - 🔍 数据点的坐标信息\n" +
                                   "   - 📊 图表类型信息\n" +
                                   "   - 数据加载和处理过程\n\n" +
                                   "预期的正确设置:\n" +
                                   "- X轴: 矿物含量 (0-100%)\n" +
                                   "- Y轴: 深度分类 (4700m, 4750m, 4800m, 4850m, 4900m)\n" +
                                   "- 图表类型: StackedBar (水平堆叠条形图)";

            // 创建堆叠柱状图控件
            this.stackedChart = new MineralStackedBarChartControl();
            this.stackedChart.Location = new Point(20, 250);
            this.stackedChart.Size = new Size(760, 350);
            this.stackedChart.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;

            // 窗体设置
            this.Text = "堆叠柱状图调试测试 - 查看VS输出窗口";
            this.Size = new Size(800, 650);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Controls.Add(this.btnLoadData);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.txtDebugInfo);
            this.Controls.Add(this.stackedChart);

            this.ResumeLayout(false);
        }

        private void BtnLoadData_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "正在加载数据，请查看VS输出窗口...";
                lblStatus.ForeColor = Color.Blue;
                Application.DoEvents();

                // 输出开始标记到VS输出窗口
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));
                System.Diagnostics.Debug.WriteLine("开始测试堆叠柱状图 - " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));

                // 创建测试数据
                DataTable testData = CreateTestData();
                System.Diagnostics.Debug.WriteLine($"测试数据创建完成，行数: {testData.Rows.Count}");

                // 定义矿物列表
                List<string> brittleMinerals = new List<string> { "石英", "长石", "方解石" };
                List<string> ductileMinerals = new List<string> { "黏土", "伊利石" };

                System.Diagnostics.Debug.WriteLine($"脆性矿物: {string.Join(", ", brittleMinerals)}");
                System.Diagnostics.Debug.WriteLine($"塑性矿物: {string.Join(", ", ductileMinerals)}");

                // 设置图表数据（这里会触发大量日志）
                System.Diagnostics.Debug.WriteLine("开始设置图表数据...");
                stackedChart.ResultData = testData;
                stackedChart.BrittleMinerals = brittleMinerals;
                stackedChart.DuctileMinerals = ductileMinerals;

                lblStatus.Text = "✅ 数据加载完成！请查看VS输出窗口的详细日志";
                lblStatus.ForeColor = Color.Green;

                // 输出结束标记
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));
                System.Diagnostics.Debug.WriteLine("测试完成 - " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));

                // 显示提示信息
                string info = "数据加载完成！\n\n";
                info += "请查看VS的输出窗口获取详细日志:\n";
                info += "1. 菜单: 视图 -> 输出\n";
                info += "2. 在输出窗口的'显示输出来源'下拉框中选择'调试'或'常规'\n";
                info += "3. 查找带有🔧、🔍、📊等图标的日志信息\n\n";
                info += "关键检查点:\n";
                info += "- X轴设置是否为0-100%矿物含量\n";
                info += "- Y轴设置是否为深度分类\n";
                info += "- 数据点坐标是否正确 (X=含量, Y=深度索引)\n";
                info += "- 图表类型是否为StackedBar";

                MessageBox.Show(info, "调试信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"❌ 加载失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
                
                System.Diagnostics.Debug.WriteLine($"错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                
                MessageBox.Show($"加载测试数据时出错:\n\n{ex.Message}\n\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable CreateTestData()
        {
            DataTable data = new DataTable();

            // 添加列
            data.Columns.Add("GeoID", typeof(string));
            data.Columns.Add("顶深/m", typeof(double));
            data.Columns.Add("底深/m", typeof(double));
            data.Columns.Add("石英", typeof(double));
            data.Columns.Add("长石", typeof(double));
            data.Columns.Add("方解石", typeof(double));
            data.Columns.Add("黏土", typeof(double));
            data.Columns.Add("伊利石", typeof(double));
            data.Columns.Add("脆性指数", typeof(double));
            data.Columns.Add("脆性矿物总量", typeof(double));
            data.Columns.Add("塑性矿物总量", typeof(double));

            // 添加测试数据 - 5个深度层，每个矿物含量都不同
            double[] depths = { 4700, 4750, 4800, 4850, 4900 };
            
            for (int i = 0; i < depths.Length; i++)
            {
                double depth = depths[i];
                DataRow row = data.NewRow();
                row["GeoID"] = $"Test_{i + 1:D3}";
                row["顶深/m"] = depth;
                row["底深/m"] = depth + 50;

                // 生成有明显变化的矿物含量数据，便于观察
                double quartz = 25 + i * 5;      // 25%, 30%, 35%, 40%, 45%
                double feldspar = 20 - i * 2;    // 20%, 18%, 16%, 14%, 12%
                double calcite = 10 + i * 2;     // 10%, 12%, 14%, 16%, 18%
                double clay = 30 - i * 3;        // 30%, 27%, 24%, 21%, 18%
                double illite = 15 - i * 2;      // 15%, 13%, 11%, 9%, 7%

                row["石英"] = Math.Round(quartz, 2);
                row["长石"] = Math.Round(feldspar, 2);
                row["方解石"] = Math.Round(calcite, 2);
                row["黏土"] = Math.Round(clay, 2);
                row["伊利石"] = Math.Round(illite, 2);

                // 计算脆性指数和总量
                double brittleTotal = quartz + feldspar + calcite;
                double ductileTotal = clay + illite;
                double brittlenessIndex = brittleTotal / (brittleTotal + ductileTotal) * 100;

                row["脆性矿物总量"] = Math.Round(brittleTotal, 2);
                row["塑性矿物总量"] = Math.Round(ductileTotal, 2);
                row["脆性指数"] = Math.Round(brittlenessIndex, 2);

                data.Rows.Add(row);
                
                // 输出每行数据到调试窗口
                System.Diagnostics.Debug.WriteLine($"测试数据[{i}]: 深度={depth}m, 石英={quartz:F1}%, 长石={feldspar:F1}%, 方解石={calcite:F1}%, 黏土={clay:F1}%, 伊利石={illite:F1}%");
            }

            return data;
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestStackedBarChartDebug());
        }
    }
}
