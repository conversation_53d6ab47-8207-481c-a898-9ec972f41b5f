#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网站搜索模块
功能：
1. 在指定学术网站搜索文献
2. 解析搜索结果
3. 提取下载链接
4. 支持多个学术数据库
"""

import re
import time
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse, quote
from loguru import logger

try:
    import requests
    from bs4 import BeautifulSoup
    import selenium
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
except ImportError as e:
    logger.error(f"网站搜索模块导入失败: {e}")
    raise

from config.settings import NETWORK_CONFIG, ACADEMIC_SITES


class WebSearcher:
    """网站搜索器"""
    
    def __init__(self):
        self.network_config = NETWORK_CONFIG
        self.academic_sites = ACADEMIC_SITES
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.network_config['user_agent']
        })
        
        # Selenium WebDriver配置
        self.driver = None
        self.setup_webdriver()
    
    def setup_webdriver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument(f'--user-agent={self.network_config["user_agent"]}')
            
            # 尝试创建WebDriver（可选）
            # self.driver = webdriver.Chrome(options=chrome_options)
            logger.info("WebDriver配置完成（未启动）")
            
        except Exception as e:
            logger.warning(f"WebDriver设置失败: {e}")
            self.driver = None
    
    def search_google_scholar(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """在Google Scholar中搜索"""
        try:
            search_url = self.academic_sites['google_scholar'] + quote(query)
            logger.info(f"搜索Google Scholar: {query}")
            
            response = self.session.get(
                search_url,
                timeout=self.network_config['timeout']
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # 解析搜索结果
            for item in soup.find_all('div', class_='gs_r gs_or gs_scl')[:max_results]:
                try:
                    result = self.parse_google_scholar_item(item)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.warning(f"解析Google Scholar结果项失败: {e}")
                    continue
            
            logger.info(f"Google Scholar搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"Google Scholar搜索失败: {e}")
            return []
    
    def parse_google_scholar_item(self, item) -> Optional[Dict[str, str]]:
        """解析Google Scholar搜索结果项"""
        try:
            result = {
                'source': 'google_scholar',
                'title': '',
                'authors': '',
                'year': '',
                'journal': '',
                'url': '',
                'pdf_url': '',
                'citations': 0
            }
            
            # 提取标题和链接
            title_elem = item.find('h3', class_='gs_rt')
            if title_elem:
                title_link = title_elem.find('a')
                if title_link:
                    result['title'] = title_link.get_text().strip()
                    result['url'] = title_link.get('href', '')
            
            # 提取作者和期刊信息
            info_elem = item.find('div', class_='gs_a')
            if info_elem:
                info_text = info_elem.get_text()
                result['authors'], result['journal'], result['year'] = self.parse_scholar_info(info_text)
            
            # 查找PDF链接
            pdf_links = item.find_all('a', href=True)
            for link in pdf_links:
                href = link.get('href', '')
                if href.endswith('.pdf') or 'pdf' in href.lower():
                    result['pdf_url'] = href
                    break
            
            # 提取引用次数
            cite_elem = item.find('a', string=re.compile(r'Cited by \d+'))
            if cite_elem:
                cite_match = re.search(r'Cited by (\d+)', cite_elem.get_text())
                if cite_match:
                    result['citations'] = int(cite_match.group(1))
            
            return result if result['title'] else None
            
        except Exception as e:
            logger.warning(f"解析Google Scholar项失败: {e}")
            return None
    
    def parse_scholar_info(self, info_text: str) -> Tuple[str, str, str]:
        """解析Google Scholar的作者、期刊、年份信息"""
        authors = ""
        journal = ""
        year = ""
        
        try:
            # 分割信息
            parts = info_text.split(' - ')
            
            if len(parts) >= 1:
                authors = parts[0].strip()
            
            if len(parts) >= 2:
                # 从第二部分提取期刊和年份
                second_part = parts[1]
                year_match = re.search(r'\b(19|20)\d{2}\b', second_part)
                if year_match:
                    year = year_match.group(0)
                    journal = second_part.replace(year, '').strip(' ,')
                else:
                    journal = second_part.strip()
            
        except Exception as e:
            logger.warning(f"解析Scholar信息失败: {e}")
        
        return authors, journal, year
    
    def search_arxiv(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """在arXiv中搜索"""
        try:
            # arXiv API搜索
            api_url = f"http://export.arxiv.org/api/query?search_query=all:{quote(query)}&start=0&max_results={max_results}"
            logger.info(f"搜索arXiv: {query}")
            
            response = self.session.get(api_url, timeout=self.network_config['timeout'])
            response.raise_for_status()
            
            # 解析XML响应
            soup = BeautifulSoup(response.content, 'xml')
            results = []
            
            for entry in soup.find_all('entry'):
                try:
                    result = self.parse_arxiv_entry(entry)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.warning(f"解析arXiv结果项失败: {e}")
                    continue
            
            logger.info(f"arXiv搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"arXiv搜索失败: {e}")
            return []
    
    def parse_arxiv_entry(self, entry) -> Optional[Dict[str, str]]:
        """解析arXiv搜索结果项"""
        try:
            result = {
                'source': 'arxiv',
                'title': '',
                'authors': '',
                'year': '',
                'journal': 'arXiv',
                'url': '',
                'pdf_url': '',
                'abstract': ''
            }
            
            # 提取标题
            title_elem = entry.find('title')
            if title_elem:
                result['title'] = title_elem.get_text().strip()
            
            # 提取作者
            authors = []
            for author in entry.find_all('author'):
                name_elem = author.find('name')
                if name_elem:
                    authors.append(name_elem.get_text().strip())
            result['authors'] = ', '.join(authors)
            
            # 提取年份
            published_elem = entry.find('published')
            if published_elem:
                published_date = published_elem.get_text()
                year_match = re.search(r'\b(19|20)\d{2}\b', published_date)
                if year_match:
                    result['year'] = year_match.group(0)
            
            # 提取链接
            id_elem = entry.find('id')
            if id_elem:
                result['url'] = id_elem.get_text().strip()
                # 构造PDF链接
                arxiv_id = result['url'].split('/')[-1]
                result['pdf_url'] = f"https://arxiv.org/pdf/{arxiv_id}.pdf"
            
            # 提取摘要
            summary_elem = entry.find('summary')
            if summary_elem:
                result['abstract'] = summary_elem.get_text().strip()
            
            return result if result['title'] else None
            
        except Exception as e:
            logger.warning(f"解析arXiv项失败: {e}")
            return None
    
    def search_custom_site(self, base_url: str, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """在自定义网站中搜索"""
        try:
            # 构造搜索URL
            if '?' in base_url:
                search_url = f"{base_url}&q={quote(query)}"
            else:
                search_url = f"{base_url}?q={quote(query)}"
            
            logger.info(f"搜索自定义网站: {base_url}")
            
            response = self.session.get(search_url, timeout=self.network_config['timeout'])
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # 通用搜索结果解析
            result_selectors = [
                'div.search-result',
                'div.result',
                'article',
                'div.paper',
                'div.publication'
            ]
            
            for selector in result_selectors:
                items = soup.select(selector)[:max_results]
                if items:
                    for item in items:
                        try:
                            result = self.parse_generic_result(item, base_url)
                            if result:
                                results.append(result)
                        except Exception as e:
                            logger.warning(f"解析搜索结果项失败: {e}")
                            continue
                    break
            
            logger.info(f"自定义网站搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"自定义网站搜索失败: {e}")
            return []
    
    def parse_generic_result(self, item, base_url: str) -> Optional[Dict[str, str]]:
        """解析通用搜索结果项"""
        try:
            result = {
                'source': urlparse(base_url).netloc,
                'title': '',
                'authors': '',
                'year': '',
                'journal': '',
                'url': '',
                'pdf_url': ''
            }
            
            # 查找标题
            title_selectors = ['h1', 'h2', 'h3', '.title', '.paper-title']
            for selector in title_selectors:
                title_elem = item.select_one(selector)
                if title_elem:
                    result['title'] = title_elem.get_text().strip()
                    break
            
            # 查找链接
            link_elem = item.find('a', href=True)
            if link_elem:
                href = link_elem.get('href')
                result['url'] = urljoin(base_url, href)
            
            # 查找PDF链接
            pdf_links = item.find_all('a', href=True)
            for link in pdf_links:
                href = link.get('href', '')
                if href.endswith('.pdf') or 'pdf' in href.lower():
                    result['pdf_url'] = urljoin(base_url, href)
                    break
            
            # 提取年份
            text = item.get_text()
            year_match = re.search(r'\b(19|20)\d{2}\b', text)
            if year_match:
                result['year'] = year_match.group(0)
            
            return result if result['title'] else None
            
        except Exception as e:
            logger.warning(f"解析通用结果项失败: {e}")
            return None
    
    def search_multiple_sites(self, query: str, sites: List[str] = None, max_results_per_site: int = 5) -> List[Dict[str, str]]:
        """在多个网站中搜索"""
        if not sites:
            sites = ['google_scholar', 'arxiv']
        
        all_results = []
        
        for site in sites:
            try:
                time.sleep(self.network_config['delay_between_requests'])
                
                if site == 'google_scholar':
                    results = self.search_google_scholar(query, max_results_per_site)
                elif site == 'arxiv':
                    results = self.search_arxiv(query, max_results_per_site)
                elif site in self.academic_sites:
                    results = self.search_custom_site(self.academic_sites[site], query, max_results_per_site)
                else:
                    # 假设是自定义URL
                    results = self.search_custom_site(site, query, max_results_per_site)
                
                all_results.extend(results)
                
            except Exception as e:
                logger.error(f"在网站 {site} 搜索失败: {e}")
                continue
        
        # 去重
        unique_results = self.deduplicate_results(all_results)
        
        logger.info(f"多网站搜索完成，总共找到 {len(unique_results)} 个唯一结果")
        return unique_results
    
    def deduplicate_results(self, results: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """去除重复的搜索结果"""
        unique_results = []
        seen_titles = set()
        
        for result in results:
            title = result.get('title', '').lower().strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_results.append(result)
        
        return unique_results
    
    def close(self):
        """关闭搜索器"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                logger.warning(f"关闭WebDriver失败: {e}")
        
        self.session.close()
