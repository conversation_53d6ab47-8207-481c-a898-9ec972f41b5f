/* 引入需要的字体 */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', 'Noto Sans SC', sans-serif;
}

:root {
    --primary-color: #45f3ff;
    --dark-bg: #23242a;
    --darker-bg: #1c1c1c;
    --panel-bg: #28292d;
    --text-light: #ffffff;
    --text-gray: #8f8f8f;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
}

body {
    background-color: var(--dark-bg);
    color: var(--text-light);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(69, 243, 255, 0.2);
}

header h1 {
    color: var(--primary-color);
    font-size: 2em;
    text-shadow: 0 0 10px rgba(69, 243, 255, 0.3);
}

.user-info {
    display: flex;
    align-items: center;
}

.username {
    margin-right: 15px;
    font-weight: 500;
}

.logout-btn {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: var(--primary-color);
    color: var(--darker-bg);
}

/* 导航样式 */
nav {
    margin: 20px 0;
}

nav ul {
    display: flex;
    list-style: none;
    background-color: var(--darker-bg);
    border-radius: 8px;
    overflow: hidden;
}

nav ul li {
    flex: 1;
}

nav ul li a {
    display: block;
    padding: 15px 0;
    text-align: center;
    text-decoration: none;
    color: var(--text-light);
    transition: all 0.3s ease;
}

nav ul li a:hover {
    background-color: rgba(69, 243, 255, 0.1);
}

nav ul li a.active {
    background-color: var(--primary-color);
    color: var(--darker-bg);
    font-weight: 500;
}

/* 矿物组分法按钮特殊样式 */
#mineralogical-btn {
    background-color: rgba(69, 243, 255, 0.2);
    border-left: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

#mineralogical-btn:hover {
    background-color: rgba(69, 243, 255, 0.3);
    transform: translateX(5px);
}

/* 主内容区域 */
main {
    background-color: var(--panel-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h2 {
    font-size: 1.8em;
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* 统计卡片 */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--darker-bg);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.stat-card h3 {
    font-size: 1em;
    margin-bottom: 15px;
    color: var(--text-gray);
}

.stat-number {
    font-size: 2em;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-desc {
    font-size: 0.8em;
    color: var(--text-gray);
}

/* 最近活动表格 */
.recent-activity {
    background-color: var(--darker-bg);
    border-radius: 8px;
    padding: 20px;
}

.recent-activity h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background-color: rgba(69, 243, 255, 0.1);
}

th,
td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    font-weight: 500;
    color: var(--primary-color);
}

tbody tr:hover {
    background-color: rgba(69, 243, 255, 0.05);
}

.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8em;
}

.status.success {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--success-color);
}

.status.warning {
    background-color: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
}

.status.danger {
    background-color: rgba(244, 67, 54, 0.2);
    color: var(--danger-color);
}

/* 页脚 */
footer {
    text-align: center;
    padding: 20px 0;
    color: var(--text-gray);
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    table {
        display: block;
        overflow-x: auto;
    }
}