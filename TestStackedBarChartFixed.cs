using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using BritSystem.Controls;

namespace BritSystem
{
    /// <summary>
    /// 测试修复后的堆叠柱状图
    /// </summary>
    public partial class TestStackedBarChartFixed : Form
    {
        private MineralStackedBarChartControl stackedChart;
        private Button btnLoadData;
        private Label lblStatus;

        public TestStackedBarChartFixed()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 创建加载数据按钮
            this.btnLoadData = new Button();
            this.btnLoadData.Text = "加载测试数据";
            this.btnLoadData.Size = new Size(150, 40);
            this.btnLoadData.Location = new Point(20, 20);
            this.btnLoadData.Click += BtnLoadData_Click;

            // 创建状态标签
            this.lblStatus = new Label();
            this.lblStatus.Text = "点击按钮加载测试数据...";
            this.lblStatus.Size = new Size(400, 25);
            this.lblStatus.Location = new Point(200, 30);
            this.lblStatus.ForeColor = Color.Blue;

            // 创建堆叠柱状图控件
            this.stackedChart = new MineralStackedBarChartControl();
            this.stackedChart.Location = new Point(20, 80);
            this.stackedChart.Size = new Size(760, 500);
            this.stackedChart.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;

            // 窗体设置
            this.Text = "堆叠柱状图修复测试 - 水平条形图";
            this.Size = new Size(800, 650);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Controls.Add(this.btnLoadData);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.stackedChart);

            this.ResumeLayout(false);
        }

        private void BtnLoadData_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "正在创建测试数据...";
                lblStatus.ForeColor = Color.Blue;
                Application.DoEvents();

                // 创建测试数据
                DataTable testData = CreateTestData();

                // 定义矿物列表
                List<string> brittleMinerals = new List<string> { "石英", "长石", "方解石" };
                List<string> ductileMinerals = new List<string> { "黏土", "伊利石" };

                lblStatus.Text = "正在设置图表数据...";
                Application.DoEvents();

                // 设置图表数据
                stackedChart.ResultData = testData;
                stackedChart.BrittleMinerals = brittleMinerals;
                stackedChart.DuctileMinerals = ductileMinerals;

                lblStatus.Text = "✅ 数据加载完成！应该显示堆叠条形图：X轴=矿物含量(0-100%)，Y轴=深度";
                lblStatus.ForeColor = Color.Green;

                // 显示数据信息
                string info = $"测试数据信息:\n";
                info += $"- 数据行数: {testData.Rows.Count}\n";
                info += $"- 脆性矿物: {string.Join(", ", brittleMinerals)}\n";
                info += $"- 塑性矿物: {string.Join(", ", ductileMinerals)}\n";
                info += $"- 深度范围: {testData.Rows[0]["顶深/m"]}m - {testData.Rows[testData.Rows.Count - 1]["顶深/m"]}m\n\n";
                info += $"正确的预期效果（按用户要求）:\n";
                info += $"- X轴: 矿物含量 (0%, 20%, 40%, 60%, 80%, 100%) - 固定刻度\n";
                info += $"- Y轴: 深度 (4700m, 4750m, 4800m, 4850m, 4900m) - 分类轴\n";
                info += $"- 每个深度显示为一个水平条，不同颜色代表不同矿物";

                MessageBox.Show(info, "测试数据信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"❌ 加载失败: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"加载测试数据时出错:\n\n{ex.Message}\n\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable CreateTestData()
        {
            DataTable data = new DataTable();

            // 添加列
            data.Columns.Add("GeoID", typeof(string));
            data.Columns.Add("顶深/m", typeof(double));
            data.Columns.Add("底深/m", typeof(double));
            data.Columns.Add("石英", typeof(double));
            data.Columns.Add("长石", typeof(double));
            data.Columns.Add("方解石", typeof(double));
            data.Columns.Add("黏土", typeof(double));
            data.Columns.Add("伊利石", typeof(double));
            data.Columns.Add("脆性指数", typeof(double));
            data.Columns.Add("脆性矿物总量", typeof(double));
            data.Columns.Add("塑性矿物总量", typeof(double));

            // 添加测试数据 - 5个深度层
            double[] depths = { 4700, 4750, 4800, 4850, 4900 };

            for (int i = 0; i < depths.Length; i++)
            {
                double depth = depths[i];
                DataRow row = data.NewRow();
                row["GeoID"] = $"Test_{i + 1:D3}";
                row["顶深/m"] = depth;
                row["底深/m"] = depth + 50;

                // 生成有变化的矿物含量数据
                double quartz = 30 + i * 5;      // 30%, 35%, 40%, 45%, 50%
                double feldspar = 20 - i * 2;    // 20%, 18%, 16%, 14%, 12%
                double calcite = 15 + i * 1;     // 15%, 16%, 17%, 18%, 19%
                double clay = 25 - i * 3;        // 25%, 22%, 19%, 16%, 13%
                double illite = 10 - i * 1;      // 10%, 9%, 8%, 7%, 6%

                row["石英"] = Math.Round(quartz, 2);
                row["长石"] = Math.Round(feldspar, 2);
                row["方解石"] = Math.Round(calcite, 2);
                row["黏土"] = Math.Round(clay, 2);
                row["伊利石"] = Math.Round(illite, 2);

                // 计算脆性指数和总量
                double brittleTotal = quartz + feldspar + calcite;
                double ductileTotal = clay + illite;
                double brittlenessIndex = brittleTotal / (brittleTotal + ductileTotal) * 100;

                row["脆性矿物总量"] = Math.Round(brittleTotal, 2);
                row["塑性矿物总量"] = Math.Round(ductileTotal, 2);
                row["脆性指数"] = Math.Round(brittlenessIndex, 2);

                data.Rows.Add(row);
            }

            return data;
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestStackedBarChartFixed());
        }
    }
}
