{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AntDesign/1.3.2": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.0", "OneOf": "3.0.271"}, "runtime": {"lib/net8.0/AntDesign.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AntDesign.Charts/0.6.1": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "OneOf": "3.0.271"}, "runtime": {"lib/net8.0/AntDesign.Charts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AntDesign.ProLayout/1.3.1": {"dependencies": {"AntDesign": "1.3.2"}, "runtime": {"lib/net8.0/AntDesign.ProLayout.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Azure.Core/1.6.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.0", "System.Diagnostics.DiagnosticSource": "4.7.0", "System.Memory": "4.5.3", "System.Numerics.Vectors": "4.5.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.0/Azure.Core.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.600.20.52802"}}}, "Azure.Identity/1.3.0": {"dependencies": {"Azure.Core": "1.6.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.Identity.Client.Extensions.Msal": "2.16.5", "System.Memory": "4.5.3", "System.Security.Cryptography.ProtectedData": "9.0.4", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "1.300.20.56202"}}}, "DotNetCore.NPOI/1.2.3": {"dependencies": {"DotNetCore.NPOI.Core": "1.2.3", "DotNetCore.NPOI.OpenXml4Net": "1.2.3", "DotNetCore.NPOI.OpenXmlFormats": "1.2.3"}, "runtime": {"lib/netstandard2.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI.Core/1.2.3": {"dependencies": {"SharpZipLib": "1.2.0", "System.Drawing.Common": "4.5.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.0/NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI.OpenXml4Net/1.2.3": {"dependencies": {"DotNetCore.NPOI.Core": "1.2.3"}, "runtime": {"lib/netstandard2.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.NPOI.OpenXmlFormats/1.2.3": {"dependencies": {"DotNetCore.NPOI.OpenXml4Net": "1.2.3"}, "runtime": {"lib/netstandard2.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EPPlus/8.0.1": {"dependencies": {"EPPlus.Interfaces": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.ComponentModel.Annotations": "5.0.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/EPPlus.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.0"}}}, "EPPlus.Interfaces/8.0.0": {"runtime": {"lib/net8.0/EPPlus.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HIC.System.Windows.Forms.DataVisualization/1.0.1": {"dependencies": {"Microsoft.Data.SqlClient": "3.0.0"}, "runtime": {"lib/net5.0-windows7.0/System.Windows.Forms.DataVisualization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authorization/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Components/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Microsoft.AspNetCore.Components.Analyzers": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.0": {}, "Microsoft.AspNetCore.Components.Forms/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Components.Web/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.0", "Microsoft.AspNetCore.Components.Forms": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.0", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Metadata/8.0.0": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.46214"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/3.0.0": {"dependencies": {"Azure.Identity": "1.3.0", "Microsoft.Data.SqlClient.SNI.runtime": "3.0.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Diagnostics.DiagnosticSource": "4.7.0", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "8.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Identity.Client/4.22.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.22.0.0", "fileVersion": "4.22.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"dependencies": {"Microsoft.Identity.Client": "4.22.0", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.16.5.0", "fileVersion": "2.16.5.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.8.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.JSInterop/8.0.0": {"runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Office.Interop.Excel/15.0.4795.1001": {"runtime": {"lib/netstandard2.0/Microsoft.Office.Interop.Excel.dll": {"assemblyVersion": "1*******", "fileVersion": "15.0.4795.1000"}}}, "Microsoft.Web.WebView2/1.0.1774.30": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.1774.30", "fileVersion": "1.0.1774.30"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.1774.30", "fileVersion": "1.0.1774.30"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.1774.30", "fileVersion": "1.0.1774.30"}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.1774.30"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.1774.30"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.1774.30"}}}, "Microsoft.Web.WebView2.DevToolsProtocolExtension/1.0.824": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.Web.WebView2": "1.0.1774.30", "System.Text.Json": "7.0.0"}, "runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.DevToolsProtocolExtension.dll": {"assemblyVersion": "1.0.824.0", "fileVersion": "1.0.824.0"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Win32.SystemEvents/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "OneOf/3.0.271": {"runtime": {"lib/netstandard2.0/OneOf.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpZipLib/1.2.0": {"runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Buffers/4.5.0": {}, "System.ComponentModel.Annotations/5.0.0": {"runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Configuration.ConfigurationManager/9.0.4": {"dependencies": {"System.Diagnostics.EventLog": "9.0.4", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Data.OleDb/9.0.4": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.4", "System.Diagnostics.PerformanceCounter": "9.0.4"}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Diagnostics.DiagnosticSource/4.7.0": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "System.Diagnostics.EventLog/9.0.4": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Diagnostics.PerformanceCounter/9.0.4": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.4"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Drawing.Common/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}, "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "System.IO.Pipelines/8.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Memory/4.5.3": {}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.4"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.Cng/4.5.0": {"runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Security.Cryptography.Xml/8.0.2": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Principal.Windows/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Text.Encoding.CodePages/8.0.0": {"runtime": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encodings.Web/7.0.0": {"runtime": {"lib/net7.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net7.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Threading.Tasks.Extensions/4.5.2": {}}}, "libraries": {"AntDesign/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-93cAUh7R3Xa9P/zMCbrG7dRSC/afCPyZ8exrY63joHwlVJRFCQPxyxjqNurcji6K0Cek+g67gjNgSNBJ3awdLg==", "path": "antdesign/1.3.2", "hashPath": "antdesign.1.3.2.nupkg.sha512"}, "AntDesign.Charts/0.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-Aqo7yivvPJzcPRL/gLAcy/62um3Iz9Z9Ffpsm1SEoeY5N1eM5tZ+a4nEew3i6EJKPDV+sIztzHBW/VX96mWpaA==", "path": "antdesign.charts/0.6.1", "hashPath": "antdesign.charts.0.6.1.nupkg.sha512"}, "AntDesign.ProLayout/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-PWW+6bpV3oHoLVlEfFxYl5h8gJJU1odBGwHfgYQ31l/cBpx4OdqrrJuTR+wNoBbTkpZ6NF/A7y7MoxUWeCXCSQ==", "path": "antdesign.prolayout/1.3.1", "hashPath": "antdesign.prolayout.1.3.1.nupkg.sha512"}, "Azure.Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-kI4m2NsODPOrxo0OoKjk6B3ADbdovhDQIEmI4039upjjZKRaewVLx/Uz4DfRa/NtnIRZQPUALe1yvdHWAoRt4w==", "path": "azure.core/1.6.0", "hashPath": "azure.core.1.6.0.nupkg.sha512"}, "Azure.Identity/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-l1SYfZKOFBuUFG7C2SWHmJcrQQaiXgBdVCycx4vcZQkC6efDVt7mzZ5pfJAFEJDBUq7mjRQ0RPq9ZDGdSswqMg==", "path": "azure.identity/1.3.0", "hashPath": "azure.identity.1.3.0.nupkg.sha512"}, "DotNetCore.NPOI/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-agXbYDkOvovMYBNqTcRAr6jrTtUXGYzVNdS9Yxy4PDZa8tBQiIeAD+54/Jdsc+3/Mln1L1Yj+SciO1TYwljcdQ==", "path": "dotnetcore.npoi/1.2.3", "hashPath": "dotnetcore.npoi.1.2.3.nupkg.sha512"}, "DotNetCore.NPOI.Core/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-2OgRNy4OpVOIwnkU8KPJs6itTE2xFpIFkFmci/iAnea/ZPTzQJrmuNPVKlfdn1csOI8ecFWES8Tk2ZmRSJjl5w==", "path": "dotnetcore.npoi.core/1.2.3", "hashPath": "dotnetcore.npoi.core.1.2.3.nupkg.sha512"}, "DotNetCore.NPOI.OpenXml4Net/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-gELhZln7TuLn2he3r0mx1qnuPLjEgTW5z8RTFnEexSQ+r1smDqSSryXNEhwUvj1beBUxTjKq2RrwkrWwLgtScA==", "path": "dotnetcore.npoi.openxml4net/1.2.3", "hashPath": "dotnetcore.npoi.openxml4net.1.2.3.nupkg.sha512"}, "DotNetCore.NPOI.OpenXmlFormats/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-3v0cJA+4rclcoW+hVuU4oYu1wNYv/jEIqQghLDMHqlviyOVxnuruAUDvsgENjiZM0iWFhkK0N/ZsXvH+qGvObw==", "path": "dotnetcore.npoi.openxmlformats/1.2.3", "hashPath": "dotnetcore.npoi.openxmlformats.1.2.3.nupkg.sha512"}, "EPPlus/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Lk46SFgp9P58+SB8ZHhsAmXlw3skC3+Ky2Nd3xmB+R01z7ZK8AUxivkOg20PZ/O94xMLqQxmORRH5tRr2JmKew==", "path": "epplus/8.0.1", "hashPath": "epplus.8.0.1.nupkg.sha512"}, "EPPlus.Interfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-EFr/vUbDYK55sxjfUfLUiv7oiz1f6ZLRYMKILHyfnWS019cYX5zJaQ1U3OojRuED8tgEeXX9QeG7Kj/b0XE7hQ==", "path": "epplus.interfaces/8.0.0", "hashPath": "epplus.interfaces.8.0.0.nupkg.sha512"}, "HIC.System.Windows.Forms.DataVisualization/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2i<PERSON>r<PERSON>Jov37VIGLATCcI8tAaFemn8KJW/b4686foj+CslKTSGa2NcQgvVCYF+kR4+6d9cg09X1xrcoIgnVlznsA==", "path": "hic.system.windows.forms.datavisualization/1.0.1", "hashPath": "hic.system.windows.forms.datavisualization.1.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "path": "microsoft.aspnetcore.authorization/8.0.0", "hashPath": "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kqspqWo3lT+rrSd39kvrV7SZYl0znYZQbQ8SJaHjDA8ffMPV6BkfVe0i6LvxRPwq/agwSWdIDq2j4x+78Frypg==", "path": "microsoft.aspnetcore.components/8.0.0", "hashPath": "microsoft.aspnetcore.components.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJMa9kQDw3vkqcMMbuicIpyax7QH6imQFbLRzVqJzrGs5LN954IPaJVkDzRCEXFVAN24Cml6g4mEF3b0D7Oa+Q==", "path": "microsoft.aspnetcore.components.analyzers/8.0.0", "hashPath": "microsoft.aspnetcore.components.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iiYB/7Sl/vTURO4EiTUCmfIXujlJOl+Gh7nknCFhvFQ+kKMFFXYcrszYwLN9aQSolpswc/A9a78KL59/UIezig==", "path": "microsoft.aspnetcore.components.forms/8.0.0", "hashPath": "microsoft.aspnetcore.components.forms.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aokUKvFoNqxR6bf0+iKrDfQ79OLHWYn5UGYp5MU65/il1vuRK7MAF18oGj7QgiZJUu3cMAZjCFkHbsWLhQxCsA==", "path": "microsoft.aspnetcore.components.web/8.0.0", "hashPath": "microsoft.aspnetcore.components.web.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "path": "microsoft.aspnetcore.metadata/8.0.0", "hashPath": "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MUauWfCLsZQQMUR/wZhec5MH6+NTPmPp9i/OsjIMmIu2ICYUGOVm1x7RTqKxq19UWxXMSG03/O0FyXQJrpDs9A==", "path": "microsoft.data.sqlclient/3.0.0", "hashPath": "microsoft.data.sqlclient.3.0.0.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-n1sNyjJgu2pYWKgw3ZPikw3NiRvG4kt7Ya5MK8u77Rgj/1bTFqO/eDF4k5W9H5GXplMZCpKkNbp5kNBICgSB0w==", "path": "microsoft.data.sqlclient.sni.runtime/3.0.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "path": "microsoft.extensions.configuration.json/8.0.1", "hashPath": "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-GlamU9rs8cSVIx9WSGv5QKpt66KkE+ImxNa/wNZZUJ3knt3PM98T9sOY8B7NcEfhw7NoxU2/0TSOcmnRSJQgqw==", "path": "microsoft.identity.client/4.22.0", "hashPath": "microsoft.identity.client.4.22.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"type": "package", "serviceable": true, "sha512": "sha512-VlGUZEpF8KP/GCfFI59sdE0WA0o9quqwM1YQY0dSp6jpGy5EOBkureaybLfpwCuYUUjQbLkN2p7neUIcQCfbzA==", "path": "microsoft.identity.client.extensions.msal/2.16.5", "hashPath": "microsoft.identity.client.extensions.msal.2.16.5.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.JSInterop/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qQqASbHxWIddssdEBKUQ/49j21SEstiho6VAepPQa9eISLCBCE6wq0m3YaB6cpdF5U+AWX5F3FvDfmssql3xtw==", "path": "microsoft.jsinterop/8.0.0", "hashPath": "microsoft.jsinterop.8.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Office.Interop.Excel/15.0.4795.1001": {"type": "package", "serviceable": true, "sha512": "sha512-cuvqi/U5MYSM0gvR2l90q0m/urRgmg69EiwP5VWp1RcaJ0YT5G26Va5LaOZ3KJFc22FNihS5CUjeePUp2YpGQA==", "path": "microsoft.office.interop.excel/15.0.4795.1001", "hashPath": "microsoft.office.interop.excel.15.0.4795.1001.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.1774.30": {"type": "package", "serviceable": true, "sha512": "sha512-yP+GB9V/obn7HUzvwu9w+erJqrtlswlBSfkfGuhanoWkEbIDlZMSI36PZeQq1sQsDNzQzaDxV8cPPCUQ7J7U4w==", "path": "microsoft.web.webview2/1.0.1774.30", "hashPath": "microsoft.web.webview2.1.0.1774.30.nupkg.sha512"}, "Microsoft.Web.WebView2.DevToolsProtocolExtension/1.0.824": {"type": "package", "serviceable": true, "sha512": "sha512-UHg2IvwJpMs7kgtkGoBAKXooalQHH5lfe/2H49MpzzlsHFB8vYDNeLvrisPZLdfrI8kQI1mGAX4AhcA8FNJyag==", "path": "microsoft.web.webview2.devtoolsprotocolextension/1.0.824", "hashPath": "microsoft.web.webview2.devtoolsprotocolextension.1.0.824.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-LuI1oG+24TUj1ZRQQjM5Ew73BKnZE5NZ/7eAdh1o8ST5dPhUnJvIkiIn2re3MwnkRy6ELRnvEbBxHP8uALKhJw==", "path": "microsoft.win32.systemevents/4.5.0", "hashPath": "microsoft.win32.systemevents.4.5.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OneOf/3.0.271": {"type": "package", "serviceable": true, "sha512": "sha512-pqpqeK8xQGggExhr4tesVgJkjdn+9HQAO0QgrYV2hFjE3y90okzk1kQMntMiUOGfV7FrCUfKPaVvPBD4IANqKg==", "path": "oneof/3.0.271", "hashPath": "oneof.3.0.271.nupkg.sha512"}, "SharpZipLib/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-zvWa/L02JHNatdtjya6Swpudb2YEHaOLHL1eRrqpjm71iGRNUNONO5adUF/9CHbSJbzhELW1UoH4NGy7n7+3bQ==", "path": "sharpziplib/1.2.0", "hashPath": "sharpziplib.1.2.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "path": "system.configuration.configurationmanager/9.0.4", "hashPath": "system.configuration.configurationmanager.9.0.4.nupkg.sha512"}, "System.Data.OleDb/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-b4n3L1M67SpXLcbAknhv4sOMoki3u21MFyv93nI8TiuiWFk2HAiXSh3kNQtQ3BPPk5Xm9rRcNTSYTgXd5T5yFA==", "path": "system.data.oledb/9.0.4", "hashPath": "system.data.oledb.9.0.4.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-oJjw3uFuVDJiJNbCD8HB4a2p3NYLdt1fiT5OGsPLw+WTOuG0KpP4OXelMmmVKpClueMsit6xOlzy4wNKQFiBLg==", "path": "system.diagnostics.diagnosticsource/4.7.0", "hashPath": "system.diagnostics.diagnosticsource.4.7.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "path": "system.diagnostics.eventlog/9.0.4", "hashPath": "system.diagnostics.eventlog.9.0.4.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dPtvbm/WuXQHFHzi/lC0bO+AJAEx5m/Gdk9A0JSXjAD/AQtXKCEOInP9VhADovaf595pjCGV0jqRFzkqDUzrqw==", "path": "system.diagnostics.performancecounter/9.0.4", "hashPath": "system.diagnostics.performancecounter.9.0.4.nupkg.sha512"}, "System.Drawing.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-AiJFxxVPdeITstiRS5aAu8+8Dpf5NawTMoapZ53Gfirml24p7HIfhjmCRxdXnmmf3IUA3AX3CcW7G73CjWxW/Q==", "path": "system.drawing.common/4.5.0", "hashPath": "system.drawing.common.4.5.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "path": "system.security.cryptography.protecteddata/9.0.4", "hashPath": "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "path": "system.security.cryptography.xml/8.0.2", "hashPath": "system.security.cryptography.xml.8.0.2.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}}}