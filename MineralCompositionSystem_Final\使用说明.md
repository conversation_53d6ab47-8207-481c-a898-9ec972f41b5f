# 矿物组分法脆性指数分析系统 V1.0

## 📋 系统简介

矿物组分法脆性指数分析系统是一款专业的地质分析软件，专门用于基于矿物成分计算岩石样本的脆性指数。系统通过分析岩石中的脆性矿物和塑性矿物含量，计算出脆性指数，帮助地质工作者评估岩石的脆性特性。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

1. 双击 `启动系统.bat` 文件
2. 系统会自动检查环境、编译并启动程序

### 方法二：命令行启动

1. 打开命令提示符或PowerShell
2. 进入系统目录
3. 执行命令：`dotnet run`

## 📋 系统要求

- **操作系统**: Windows 10/11 (64位)
- **运行环境**: .NET 8.0 Desktop Runtime
- **内存**: 建议 4GB 以上
- **硬盘空间**: 500MB 可用空间

## 🔧 环境配置

### 安装 .NET 8.0 Runtime

如果系统提示缺少 .NET 8.0，请：

1. 访问：<https://dotnet.microsoft.com/download/dotnet/8.0>
2. 下载并安装 ".NET 8.0 Desktop Runtime"
3. 重启系统后再次运行

## 👤 登录信息

- **用户名**: admin
- **密码**: 123

## 🎯 主要功能

### 1. 数据导入

- 支持 Excel 文件导入 (.xlsx, .xls)
- 自动识别矿物成分列
- 智能检测顶深和底深列

### 2. 矿物分析

- **脆性矿物**: 石英、长石、碳酸盐等
- **塑性矿物**: 黏土矿物、有机质等
- 支持手动映射和自动识别

### 3. 脆性指数计算

使用公式：

```
脆性指数 = 脆性矿物总量 / (脆性矿物总量 + 塑性矿物总量)
```

### 4. 数据可视化

- 深度-脆性指数关系曲线
- 矿物成分分布图
- 交互式图表操作

### 5. 结果导出

- Excel 格式导出
- 图表保存功能
- 数据备份

## 📖 操作指南

### 基本操作流程

1. **启动系统** → 使用启动脚本或命令行
2. **用户登录** → 输入用户名和密码
3. **选择功能** → 点击"开始分析"
4. **导入数据** → 加载 Excel 数据文件
5. **选择列** → 指定顶深、底深和矿物列
6. **计算分析** → 执行脆性指数计算
7. **查看结果** → 查看计算结果和图表
8. **导出数据** → 保存分析结果

### 数据格式要求

Excel 文件应包含以下列：

- **深度信息**: 顶深列、底深列
- **矿物成分**: 各种矿物含量（百分比）
- **数据格式**: 数值型数据

### 常见问题解决

**Q: 系统无法启动？**
A: 检查是否安装了 .NET 8.0 Runtime

**Q: 无法识别矿物列？**
A: 使用手动映射功能指定列对应关系

**Q: 计算结果异常？**
A: 检查输入数据的完整性和格式

**Q: 图表显示不正常？**
A: 确保数据中包含有效的深度和脆性指数值

## 📞 技术支持

如遇到技术问题，请：

1. 查看系统日志文件（SampleData/日志.txt）
2. 检查数据格式是否符合要求
3. 确认系统环境配置正确

## 📝 版本信息

- **版本**: V1.0
- **发布日期**: 2025年6月
- **适用范围**: 地质勘探、岩石力学分析
- **技术框架**: .NET 8.0 + Windows Forms

---

**注意**: 本系统专门用于矿物组分法脆性指数分析，如需进行岩石力学参数法分析，请使用对应的静态岩石力学参数法系统。
