﻿using System;
using System.Collections.Generic;

namespace MineralCompositionSystem.Models
{
    /// <summary>
    /// 脆性指数数据点模型
    /// </summary>
    public class BrittlenessDataPoint
    {
        /// <summary>
        /// 地质点唯一标识
        /// </summary>
        public string GeoID { get; set; }

        /// <summary>
        /// 顶深
        /// </summary>
        public double TopDepth { get; set; }

        /// <summary>
        /// 底深
        /// </summary>
        public double BottomDepth { get; set; }

        /// <summary>
        /// 脆性指数
        /// </summary>
        public double BrittleIndex { get; set; }

        /// <summary>
        /// 脆性矿物列表
        /// </summary>
        public List<string> BrittleMinerals { get; set; } = new List<string>();

        /// <summary>
        /// 塑性矿物列表
        /// </summary>
        public List<string> DuctileMinerals { get; set; } = new List<string>();

        /// <summary>
        /// 原始数据行索引
        /// </summary>
        public int RowIndex { get; set; } = -1;

        /// <summary>
        /// 列名映射字典
        /// </summary>
        public Dictionary<string, string> ColumnMappings { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 生成唯一的GeoID
        /// </summary>
        public void GenerateGeoID()
        {
            // 使用深度和脆性指数生成唯一的GeoID
            GeoID = $"GEO_{TopDepth:F2}_{BottomDepth:F2}_{BrittleIndex:F4}";
            // 添加哈希校验保证唯一性
            GeoID += $"_{Math.Abs(GeoID.GetHashCode()):X8}";
        }

        /// <summary>
        /// 生成包含映射信息的GeoID
        /// </summary>
        public void GenerateGeoIDWithMappings(Dictionary<string, string> brittleMappings, Dictionary<string, string> ductileMappings)
        {
            // 基本GeoID生成
            GenerateGeoID();

            // 保存列名映射
            ColumnMappings.Clear();

            // 添加脆性矿物映射
            foreach (var mapping in brittleMappings)
            {
                ColumnMappings[mapping.Key] = mapping.Value;
            }

            // 添加塑性矿物映射
            foreach (var mapping in ductileMappings)
            {
                ColumnMappings[mapping.Key] = mapping.Value;
            }
        }
    }
}
