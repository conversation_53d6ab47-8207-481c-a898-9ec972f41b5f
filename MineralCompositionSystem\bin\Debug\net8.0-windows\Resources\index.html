<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脆性指数系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 420px;
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            backdrop-filter: blur(10px);
        }

        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-header h1 {
            color: #2a5298;
            font-size: 2.2rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .login-header p {
            color: #666;
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .form-group label {
            display: block;
            color: #333;
            margin-bottom: 0.6rem;
            font-size: 1rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-group input:focus {
            outline: none;
            border-color: #2a5298;
            box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.2);
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(42, 82, 152, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .error-message {
            color: #dc3545;
            font-size: 0.95rem;
            margin-top: 1.2rem;
            text-align: center;
            display: none;
            padding: 0.8rem;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 6px;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .shake {
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .login-header h1 {
                font-size: 1.8rem;
            }

            .form-group input {
                font-size: 1rem;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@4.24.13/dist/antd.min.css">
    <script src="https://cdn.jsdelivr.net/npm/antd@4.24.13/dist/antd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.21/dist/vue.global.prod.js"></script>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <div class="login-header">
                <h1>脆性指数系统</h1>
                <p>请登录以继续</p>
            </div>
            <a-form id="login-form" @submit.prevent="handleLogin">
                <a-form-item label="用户名">
                    <a-input v-model:value="username" autocomplete="username" placeholder="请输入用户名" />
                </a-form-item>
                <a-form-item label="密码">
                    <a-input-password v-model:value="password" autocomplete="current-password" placeholder="请输入密码" />
                </a-form-item>
                <a-form-item>
                    <a-button type="primary" html-type="submit" :loading="loading" block>登录</a-button>
                </a-form-item>
                <a-alert v-if="errorMessage" :message="errorMessage" type="error" show-icon style="margin-top: 12px;" />
            </a-form>
        </div>
    </div>

    <script>
    const { createApp, ref } = Vue;
    createApp({
        setup() {
            const username = ref("");
            const password = ref("");
            const loading = ref(false);
            const errorMessage = ref("");
            const isRunningInWebView = () => window.chrome && window.chrome.webview;
            const handleLogin = () => {
                loading.value = true;
                errorMessage.value = "";
                if (isRunningInWebView()) {
                    window.chrome.webview.postMessage({
                        action: 'login',
                        username: username.value,
                        password: password.value
                    });
                } else {
                    setTimeout(() => {
                        loading.value = false;
                        errorMessage.value = "仅支持在桌面应用中登录";
                    }, 1000);
                }
            };
            if (isRunningInWebView()) {
                window.chrome.webview.addEventListener('message', event => {
                    const data = event.data;
                    if (data.action === 'loginResult') {
                        loading.value = false;
                        if (data.success) {
                            errorMessage.value = "";
                        } else {
                            errorMessage.value = data.message || "用户名或密码错误";
                        }
                    }
                });
            }
            return { username, password, loading, errorMessage, handleLogin };
        }
    }).use(antd).mount('#app');
    </script>
</body>
</html>