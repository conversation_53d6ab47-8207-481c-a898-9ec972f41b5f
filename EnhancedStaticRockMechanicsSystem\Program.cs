using System;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem
{
    /// <summary>
    /// 增强版静态岩石力学参数法系统主程序
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // 初始化日志服务
                LoggingService.Instance.Info("增强版静态岩石力学参数法系统启动");
                
                // 启动主窗体
                Application.Run(new Forms.StaticRockMechanicsForm());
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"系统启动失败: {ex.Message}");
                MessageBox.Show($"系统启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
