#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试
测试各个模块的基本功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import unittest
from unittest.mock import Mock, patch

try:
    from modules.citation_parser import CitationParser
    from modules.matcher import LiteratureMatcher
    from modules.file_manager import FileManager
    from utils.helpers import *
    from config.settings import get_config
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class TestCitationParser(unittest.TestCase):
    """测试文献引用解析器"""
    
    def setUp(self):
        self.parser = CitationParser()
    
    def test_parse_apa_style(self):
        """测试APA格式解析"""
        citation_text = "Smith, J. (2020). A study of machine learning. Journal of AI Research."
        result = self.parser.parse_apa_style(citation_text)
        
        self.assertEqual(result['pattern_type'], 'apa_style')
        self.assertIn('authors', result)
        self.assertIn('year', result)
        self.assertIn('title', result)
    
    def test_parse_authors(self):
        """测试作者解析"""
        author_text = "Smith, J. and Brown, A. & Wilson, C."
        authors = self.parser.parse_authors(author_text)
        
        self.assertIsInstance(authors, list)
        self.assertGreater(len(authors), 0)
    
    def test_clean_title(self):
        """测试标题清理"""
        title = '"A Study of Machine Learning Algorithms"'
        cleaned = self.parser.clean_title(title)
        
        self.assertNotIn('"', cleaned)
        self.assertGreater(len(cleaned), 0)


class TestLiteratureMatcher(unittest.TestCase):
    """测试文献匹配器"""
    
    def setUp(self):
        self.matcher = LiteratureMatcher()
    
    def test_normalize_authors(self):
        """测试作者标准化"""
        authors = "Smith, J. and Brown, A."
        normalized = self.matcher.normalize_authors(authors)
        
        self.assertIsInstance(normalized, list)
        self.assertGreater(len(normalized), 0)
    
    def test_calculate_similarity(self):
        """测试相似度计算"""
        target = {
            'title': 'Machine Learning in Healthcare',
            'authors': ['Smith, J.'],
            'year': '2020'
        }
        
        candidate = {
            'title': 'Machine Learning Applications in Healthcare',
            'authors': ['Smith, John'],
            'year': '2020'
        }
        
        similarity = self.matcher.calculate_similarity(target, candidate)
        
        self.assertIsInstance(similarity, dict)
        self.assertIn('overall_score', similarity)
        self.assertGreaterEqual(similarity['overall_score'], 0)
        self.assertLessEqual(similarity['overall_score'], 1)


class TestFileManager(unittest.TestCase):
    """测试文件管理器"""
    
    def setUp(self):
        self.file_manager = FileManager()
    
    def test_clean_text_for_filename(self):
        """测试文件名文本清理"""
        text = 'A Study of "Machine Learning" & AI: Applications?'
        cleaned = self.file_manager.clean_text_for_filename(text)
        
        # 检查是否移除了非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            self.assertNotIn(char, cleaned)
    
    def test_generate_filename(self):
        """测试文件名生成"""
        literature_info = {
            'title': 'Machine Learning in Healthcare',
            'authors': ['Smith, J.', 'Brown, A.'],
            'year': '2020'
        }
        
        filename = self.file_manager.generate_filename(literature_info)
        
        self.assertIsInstance(filename, str)
        self.assertGreater(len(filename), 0)
        self.assertTrue(filename.endswith('.pdf'))


class TestHelpers(unittest.TestCase):
    """测试辅助函数"""
    
    def test_clean_text(self):
        """测试文本清理"""
        text = "  This   is  a   test  text  \n\t  "
        cleaned = clean_text(text)
        
        self.assertEqual(cleaned, "This is a test text")
    
    def test_extract_doi_from_text(self):
        """测试DOI提取"""
        text = "This paper has DOI: 10.1234/example.doi.123"
        doi = extract_doi_from_text(text)
        
        self.assertEqual(doi, "10.1234/example.doi.123")
    
    def test_extract_year_from_text(self):
        """测试年份提取"""
        text = "This study was conducted in 2020."
        year = extract_year_from_text(text)
        
        self.assertEqual(year, "2020")
    
    def test_normalize_title(self):
        """测试标题标准化"""
        title = '"A Study of Machine Learning",'
        normalized = normalize_title(title)
        
        self.assertEqual(normalized, "A Study of Machine Learning")
    
    def test_parse_author_list(self):
        """测试作者列表解析"""
        author_text = "Smith, J. and Brown, A. & Wilson, C."
        authors = parse_author_list(author_text)
        
        self.assertIsInstance(authors, list)
        self.assertGreaterEqual(len(authors), 3)
    
    def test_is_valid_url(self):
        """测试URL验证"""
        valid_url = "https://www.example.com"
        invalid_url = "not_a_url"
        
        self.assertTrue(is_valid_url(valid_url))
        self.assertFalse(is_valid_url(invalid_url))
    
    def test_safe_filename(self):
        """测试安全文件名生成"""
        unsafe_name = 'file<name>with:illegal"chars'
        safe_name = safe_filename(unsafe_name)
        
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            self.assertNotIn(char, safe_name)
    
    def test_format_file_size(self):
        """测试文件大小格式化"""
        size = 1024 * 1024  # 1 MB
        formatted = format_file_size(size)
        
        self.assertIn("MB", formatted)
    
    def test_truncate_text(self):
        """测试文本截断"""
        long_text = "This is a very long text that should be truncated"
        truncated = truncate_text(long_text, max_length=20)
        
        self.assertLessEqual(len(truncated), 23)  # 20 + "..."
        self.assertTrue(truncated.endswith("..."))


class TestConfig(unittest.TestCase):
    """测试配置"""
    
    def test_get_config(self):
        """测试配置获取"""
        config = get_config()
        
        self.assertIsInstance(config, dict)
        self.assertIn('app', config)
        self.assertIn('paths', config)
    
    def test_get_specific_config(self):
        """测试特定配置获取"""
        ocr_config = get_config('ocr')
        
        self.assertIsInstance(ocr_config, dict)


def run_basic_tests():
    """运行基本测试"""
    print("开始运行基本功能测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestCitationParser,
        TestLiteratureMatcher,
        TestFileManager,
        TestHelpers,
        TestConfig
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n测试完成:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
