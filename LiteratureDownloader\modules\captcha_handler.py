#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人机验证处理模块
专门处理Web of Science等网站的人机验证
"""

import time
import random
from typing import Optional
from loguru import logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    import pyautogui
except ImportError as e:
    logger.error(f"依赖库导入失败: {e}")


class CaptchaHandler:
    """人机验证处理器"""
    
    def __init__(self, driver):
        self.driver = driver
        # 设置pyautogui安全设置
        try:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.1
        except:
            pass
    
    def detect_captcha(self) -> bool:
        """检测是否存在人机验证"""
        captcha_patterns = [
            # reCAPTCHA
            'iframe[src*="recaptcha"]',
            'div[class*="g-recaptcha"]',
            'div[id*="recaptcha"]',
            
            # hCaptcha
            'iframe[src*="hcaptcha"]',
            'div[class*="h-captcha"]',
            
            # Cloudflare
            'div[class*="cf-browser-verification"]',
            'div[class*="cf-challenge"]',
            'div[id*="cf-wrapper"]',
            
            # 通用验证码
            'div[class*="captcha"]',
            'div[id*="captcha"]',
            'iframe[title*="captcha"]',
            
            # 中文验证提示
            'div:contains("人机验证")',
            'div:contains("安全验证")',
            'div:contains("请完成验证")',
            
            # 英文验证提示
            'div:contains("Please verify")',
            'div:contains("Verification required")',
            'div:contains("Security check")',
            
            # 验证按钮
            'button:contains("验证")',
            'button:contains("Verify")',
            'input[value*="验证"]'
        ]
        
        for pattern in captcha_patterns:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, pattern)
                if elements and any(elem.is_displayed() for elem in elements):
                    logger.info(f"检测到验证码: {pattern}")
                    return True
            except:
                continue
        
        # 检查页面URL和标题
        try:
            url = self.driver.current_url.lower()
            title = self.driver.title.lower()
            
            captcha_keywords = ['captcha', 'verification', 'challenge', '验证', '人机']
            
            if any(keyword in url or keyword in title for keyword in captcha_keywords):
                logger.info(f"URL或标题包含验证关键词: {url}, {title}")
                return True
        except:
            pass
        
        return False
    
    def handle_captcha(self) -> bool:
        """处理人机验证"""
        logger.info("开始处理人机验证...")
        
        # 策略1: 尝试自动点击
        if self._try_auto_click():
            return True
        
        # 策略2: 使用鼠标控制
        if self._try_mouse_control():
            return True
        
        # 策略3: 等待用户手动处理
        return self._wait_user_manual()
    
    def _try_auto_click(self) -> bool:
        """尝试自动点击验证元素"""
        click_selectors = [
            # reCAPTCHA复选框
            'div[class*="recaptcha-checkbox-border"]',
            'span[class*="recaptcha-checkbox"]',
            
            # hCaptcha复选框
            'div[class*="hcaptcha-checkbox"]',
            
            # 通用验证按钮
            'button[class*="verify"]',
            'input[type="checkbox"][class*="captcha"]',
            'div[role="checkbox"]',
            
            # Cloudflare验证
            'input[type="checkbox"][name="cf_captcha_kind"]',
            'button[type="submit"][value="Verify"]'
        ]
        
        for selector in click_selectors:
            try:
                element = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                
                if element.is_displayed():
                    logger.info(f"尝试点击验证元素: {selector}")
                    self._human_click(element)
                    
                    # 等待验证结果
                    time.sleep(3)
                    
                    # 检查是否验证成功
                    if not self.detect_captcha():
                        logger.info("自动点击验证成功")
                        return True
                        
            except Exception as e:
                logger.debug(f"点击 {selector} 失败: {e}")
                continue
        
        return False
    
    def _human_click(self, element):
        """模拟人类点击"""
        try:
            # 滚动到元素
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(random.uniform(0.3, 0.8))
            
            # 获取元素位置
            location = element.location_once_scrolled_into_view
            size = element.size
            
            # 计算点击位置（元素中心附近的随机位置）
            x = location['x'] + size['width'] // 2 + random.randint(-5, 5)
            y = location['y'] + size['height'] // 2 + random.randint(-5, 5)
            
            # 使用ActionChains模拟鼠标移动
            actions = ActionChains(self.driver)
            actions.move_to_element_with_offset(element, 
                                              random.randint(-2, 2), 
                                              random.randint(-2, 2))
            actions.pause(random.uniform(0.1, 0.3))
            actions.click()
            actions.perform()
            
        except Exception as e:
            logger.debug(f"ActionChains点击失败: {e}")
            # 备用方案：JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", element)
            except Exception as e2:
                logger.debug(f"JavaScript点击也失败: {e2}")
    
    def _try_mouse_control(self) -> bool:
        """尝试使用系统鼠标控制"""
        try:
            # 查找验证码iframe
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            
            for iframe in iframes:
                src = iframe.get_attribute("src") or ""
                if any(keyword in src.lower() for keyword in ['captcha', 'recaptcha', 'hcaptcha']):
                    logger.info("找到验证码iframe，尝试鼠标控制")
                    
                    # 获取iframe位置
                    location = iframe.location_once_scrolled_into_view
                    size = iframe.size
                    
                    # 计算点击位置
                    click_x = location['x'] + 30  # 复选框通常在左上角
                    click_y = location['y'] + 30
                    
                    # 获取浏览器窗口位置
                    window_pos = self.driver.get_window_position()
                    
                    # 计算屏幕坐标
                    screen_x = window_pos['x'] + click_x
                    screen_y = window_pos['y'] + click_y + 100  # 加上浏览器标题栏高度
                    
                    # 使用pyautogui点击
                    pyautogui.click(screen_x, screen_y)
                    time.sleep(2)
                    
                    # 检查是否成功
                    if not self.detect_captcha():
                        logger.info("鼠标控制验证成功")
                        return True
                    
        except Exception as e:
            logger.debug(f"鼠标控制失败: {e}")
        
        return False
    
    def _wait_user_manual(self) -> bool:
        """等待用户手动处理"""
        import tkinter as tk
        from tkinter import messagebox
        
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)  # 置顶显示
            
            result = messagebox.askokcancel(
                "🤖 人机验证处理",
                "检测到人机验证页面！\n\n"
                "🔧 自动处理方案已尝试但未成功\n"
                "👆 请在浏览器中手动完成验证\n\n"
                "完成验证后请点击'确定'继续\n"
                "如果无法完成验证，点击'取消'跳过\n\n"
                "⚠️ 请保持浏览器窗口打开",
                icon='question'
            )
            
            root.destroy()
            
            if result:
                # 给用户一些时间完成验证
                time.sleep(2)
                
                # 再次检查验证状态
                max_wait = 30  # 最多等待30秒
                for i in range(max_wait):
                    if not self.detect_captcha():
                        logger.info("用户手动验证完成")
                        return True
                    time.sleep(1)
                
                logger.warning("验证状态未改变，但继续尝试")
                return True
            else:
                logger.info("用户选择跳过验证")
                return False
                
        except Exception as e:
            logger.error(f"用户交互失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """等待页面加载完成"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            return True
        except:
            return False
