using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace MineralCompositionSystem.Forms
{
    /// <summary>
    /// 数据预览窗体
    /// </summary>
    public partial class DataPreviewForm : Form
    {
        private List<object> _dataPoints;
        private string _systemName;
        
        private DataGridView dgvPreview;
        private TextBox txtJsonPreview;
        private TabControl tabControl;
        private Label lblSummary;
        private Button btnClose;
        
        public DataPreviewForm(List<object> dataPoints, string systemName)
        {
            _dataPoints = dataPoints ?? new List<object>();
            _systemName = systemName ?? "未知系统";
            
            InitializeComponent();
            LoadPreviewData();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 窗体设置
            this.Text = $"数据预览 - {_systemName}";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            
            // 摘要标签
            lblSummary = new Label();
            lblSummary.Location = new Point(12, 12);
            lblSummary.Size = new Size(760, 40);
            lblSummary.Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold);
            lblSummary.ForeColor = Color.DarkBlue;
            
            // 选项卡控件
            tabControl = new TabControl();
            tabControl.Location = new Point(12, 60);
            tabControl.Size = new Size(760, 480);
            
            // 表格预览选项卡
            TabPage tabTable = new TabPage("表格视图");
            dgvPreview = new DataGridView();
            dgvPreview.Dock = DockStyle.Fill;
            dgvPreview.ReadOnly = true;
            dgvPreview.AllowUserToAddRows = false;
            dgvPreview.AllowUserToDeleteRows = false;
            dgvPreview.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvPreview.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvPreview.AlternatingRowsDefaultCellStyle.BackColor = Color.LightGray;
            tabTable.Controls.Add(dgvPreview);
            
            // JSON预览选项卡
            TabPage tabJson = new TabPage("JSON格式");
            txtJsonPreview = new TextBox();
            txtJsonPreview.Dock = DockStyle.Fill;
            txtJsonPreview.Multiline = true;
            txtJsonPreview.ScrollBars = ScrollBars.Both;
            txtJsonPreview.ReadOnly = true;
            txtJsonPreview.Font = new Font("Consolas", 9F);
            txtJsonPreview.BackColor = Color.White;
            tabJson.Controls.Add(txtJsonPreview);
            
            tabControl.TabPages.Add(tabTable);
            tabControl.TabPages.Add(tabJson);
            
            // 关闭按钮
            btnClose = new Button();
            btnClose.Text = "关闭";
            btnClose.Location = new Point(697, 550);
            btnClose.Size = new Size(75, 30);
            btnClose.DialogResult = DialogResult.OK;
            
            // 添加控件到窗体
            this.Controls.AddRange(new Control[] { lblSummary, tabControl, btnClose });
            
            this.ResumeLayout(false);
        }
        
        private void LoadPreviewData()
        {
            try
            {
                // 更新摘要信息
                UpdateSummaryInfo();
                
                // 加载表格数据
                LoadTableData();
                
                // 加载JSON数据
                LoadJsonData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载预览数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void UpdateSummaryInfo()
        {
            if (_dataPoints.Count == 0)
            {
                lblSummary.Text = "没有数据可预览";
                return;
            }
            
            // 分析数据统计信息
            var stats = AnalyzeDataStatistics();
            
            lblSummary.Text = $"系统: {_systemName} | " +
                             $"数据点数量: {_dataPoints.Count} | " +
                             $"深度范围: {stats.MinDepth:F1}m - {stats.MaxDepth:F1}m | " +
                             $"脆性指数范围: {stats.MinBrittleIndex:F1}% - {stats.MaxBrittleIndex:F1}% | " +
                             $"平均脆性指数: {stats.AvgBrittleIndex:F1}%";
        }
        
        private DataStatistics AnalyzeDataStatistics()
        {
            var stats = new DataStatistics();
            
            if (_dataPoints.Count == 0) return stats;
            
            var depths = new List<double>();
            var brittleIndices = new List<double>();
            
            foreach (var item in _dataPoints)
            {
                var json = JsonConvert.SerializeObject(item);
                var data = JsonConvert.DeserializeObject<dynamic>(json);
                
                if (data.TopDepth != null)
                {
                    depths.Add((double)data.TopDepth);
                }
                
                if (data.BrittleIndex != null)
                {
                    brittleIndices.Add((double)data.BrittleIndex);
                }
            }
            
            if (depths.Count > 0)
            {
                stats.MinDepth = depths.Min();
                stats.MaxDepth = depths.Max();
            }
            
            if (brittleIndices.Count > 0)
            {
                stats.MinBrittleIndex = brittleIndices.Min();
                stats.MaxBrittleIndex = brittleIndices.Max();
                stats.AvgBrittleIndex = brittleIndices.Average();
            }
            
            return stats;
        }
        
        private void LoadTableData()
        {
            try
            {
                if (_dataPoints.Count == 0)
                {
                    dgvPreview.DataSource = null;
                    return;
                }
                
                // 创建DataTable
                DataTable dt = new DataTable();
                dt.Columns.Add("序号", typeof(int));
                dt.Columns.Add("顶深(m)", typeof(double));
                dt.Columns.Add("底深(m)", typeof(double));
                dt.Columns.Add("脆性指数(%)", typeof(double));
                dt.Columns.Add("数据来源", typeof(string));
                
                // 填充数据
                for (int i = 0; i < _dataPoints.Count; i++)
                {
                    var item = _dataPoints[i];
                    var json = JsonConvert.SerializeObject(item);
                    var data = JsonConvert.DeserializeObject<dynamic>(json);
                    
                    DataRow row = dt.NewRow();
                    row["序号"] = i + 1;
                    row["顶深(m)"] = data.TopDepth ?? 0;
                    row["底深(m)"] = data.BottomDepth ?? 0;
                    row["脆性指数(%)"] = data.BrittleIndex ?? 0;
                    row["数据来源"] = _systemName;
                    
                    dt.Rows.Add(row);
                }
                
                dgvPreview.DataSource = dt;
                
                // 设置列格式
                if (dgvPreview.Columns["顶深(m)"] != null)
                    dgvPreview.Columns["顶深(m)"].DefaultCellStyle.Format = "F2";
                if (dgvPreview.Columns["底深(m)"] != null)
                    dgvPreview.Columns["底深(m)"].DefaultCellStyle.Format = "F2";
                if (dgvPreview.Columns["脆性指数(%)"] != null)
                    dgvPreview.Columns["脆性指数(%)"].DefaultCellStyle.Format = "F2";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载表格数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void LoadJsonData()
        {
            try
            {
                var previewData = new
                {
                    SystemName = _systemName,
                    ExportTime = DateTime.Now,
                    DataCount = _dataPoints.Count,
                    DataPoints = _dataPoints.Take(10).ToList(), // 只显示前10条数据作为预览
                    Note = _dataPoints.Count > 10 ? $"仅显示前10条数据，实际共有{_dataPoints.Count}条数据" : "显示全部数据"
                };
                
                string jsonText = JsonConvert.SerializeObject(previewData, Formatting.Indented);
                txtJsonPreview.Text = jsonText;
            }
            catch (Exception ex)
            {
                txtJsonPreview.Text = $"JSON预览生成失败: {ex.Message}";
            }
        }
        
        private class DataStatistics
        {
            public double MinDepth { get; set; }
            public double MaxDepth { get; set; }
            public double MinBrittleIndex { get; set; }
            public double MaxBrittleIndex { get; set; }
            public double AvgBrittleIndex { get; set; }
        }
    }
}
