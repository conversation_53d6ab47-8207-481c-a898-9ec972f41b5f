#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复的引用识别功能
专门验证52个引用的准确识别
"""

import sys
import re
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.pdf_reader import PDFReader

def test_final_fix():
    """测试最终修复的引用识别"""
    pdf_file = "paper.pdf"
    
    if not Path(pdf_file).exists():
        print(f"错误: 找不到文件 {pdf_file}")
        return
    
    print("=" * 80)
    print("测试最终修复的引用识别功能")
    print("目标: 准确识别52个引用，并正确提取作者、标题、期刊信息")
    print("=" * 80)
    
    # 创建PDF读取器
    pdf_reader = PDFReader()
    
    # 提取文本
    print("1. 提取PDF文本...")
    text = pdf_reader.extract_text(pdf_file)
    
    if not text:
        print("错误: 无法提取PDF文本")
        return
    
    print(f"   文本总长度: {len(text)} 字符")
    
    # 查找References部分（只在最后30%查找）
    print("\n2. 在文档最后30%部分查找References...")
    references_text = pdf_reader.extract_references_section(text)
    
    if not references_text:
        print("   错误: 未找到References部分")
        return
    
    print(f"   References部分长度: {len(references_text)} 字符")
    
    # 显示References开头内容
    print(f"\n   References开头内容:")
    lines = references_text.split('\n')[:5]
    for i, line in enumerate(lines):
        if line.strip():
            print(f"   {line[:80]}...")
    
    # 查找所有编号引用
    print("\n3. 查找编号引用 [1], [2], [3] ...")
    numbered_pattern = r'\[(\d+)\]\s*(.*?)(?=\n\s*\[\d+\]|\n\s*$|$)'
    numbered_matches = list(re.finditer(numbered_pattern, references_text, re.DOTALL))
    
    print(f"   找到 {len(numbered_matches)} 个编号引用")
    
    # 验证引用编号的连续性
    ref_numbers = []
    for match in numbered_matches:
        ref_num = int(match.group(1))
        ref_numbers.append(ref_num)
    
    ref_numbers.sort()
    print(f"   引用编号范围: [{min(ref_numbers)} - {max(ref_numbers)}]")
    
    # 检查是否有缺失的编号
    expected_numbers = set(range(1, max(ref_numbers) + 1))
    actual_numbers = set(ref_numbers)
    missing_numbers = expected_numbers - actual_numbers
    
    if missing_numbers:
        print(f"   警告: 缺失的引用编号: {sorted(missing_numbers)}")
    else:
        print(f"   ✓ 引用编号连续完整")
    
    # 解析引用内容
    print("\n4. 解析引用内容...")
    citations = pdf_reader.parse_references_section(references_text)
    
    print(f"   成功解析: {len(citations)} 个引用")
    
    # 统计解析质量
    valid_authors = sum(1 for c in citations if c.get('authors'))
    valid_titles = sum(1 for c in citations if c.get('title'))
    valid_years = sum(1 for c in citations if c.get('year'))
    valid_journals = sum(1 for c in citations if c.get('journal'))
    valid_dois = sum(1 for c in citations if c.get('doi'))
    
    print(f"\n5. 解析质量统计:")
    print(f"   有作者信息: {valid_authors}/{len(citations)} ({valid_authors/len(citations)*100:.1f}%)")
    print(f"   有标题信息: {valid_titles}/{len(citations)} ({valid_titles/len(citations)*100:.1f}%)")
    print(f"   有年份信息: {valid_years}/{len(citations)} ({valid_years/len(citations)*100:.1f}%)")
    print(f"   有期刊信息: {valid_journals}/{len(citations)} ({valid_journals/len(citations)*100:.1f}%)")
    print(f"   有DOI信息: {valid_dois}/{len(citations)} ({valid_dois/len(citations)*100:.1f}%)")
    
    # 显示前5个解析结果
    print(f"\n6. 前5个引用的解析结果:")
    print("   " + "=" * 70)
    
    for i, citation in enumerate(citations[:5]):
        ref_num = citation.get('reference_number', '?')
        print(f"\n   [{ref_num}] 引用 {i+1}:")
        print(f"   作者: {citation.get('authors', '未识别')}")
        print(f"   标题: {citation.get('title', '未识别')}")
        print(f"   期刊: {citation.get('journal', '未识别')}")
        print(f"   年份: {citation.get('year', '未识别')}")
        if citation.get('doi'):
            print(f"   DOI: {citation.get('doi')}")
        print(f"   原文: {citation.get('full_match', '')[:100]}...")
    
    # 显示最后2个解析结果
    if len(citations) > 5:
        print(f"\n   最后2个引用的解析结果:")
        for i, citation in enumerate(citations[-2:]):
            ref_num = citation.get('reference_number', '?')
            actual_index = len(citations) - 2 + i
            print(f"\n   [{ref_num}] 引用 {actual_index + 1}:")
            print(f"   作者: {citation.get('authors', '未识别')}")
            print(f"   标题: {citation.get('title', '未识别')}")
            print(f"   期刊: {citation.get('journal', '未识别')}")
            print(f"   年份: {citation.get('year', '未识别')}")
    
    # 最终评估
    print(f"\n7. 最终评估:")
    print("   " + "=" * 50)
    
    if len(citations) == 52:
        print("   ✓ 引用数量正确: 52个")
    else:
        print(f"   ✗ 引用数量不正确: {len(citations)}个 (期望52个)")
    
    if valid_authors >= len(citations) * 0.8:
        print("   ✓ 作者识别率良好")
    else:
        print("   ✗ 作者识别率需要改进")
    
    if valid_titles >= len(citations) * 0.7:
        print("   ✓ 标题识别率良好")
    else:
        print("   ✗ 标题识别率需要改进")
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)

if __name__ == "__main__":
    test_final_fix()
