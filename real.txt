using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using System.IO;
using System.Diagnostics;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem
{
    /// <summary>
    /// 静态岩石力学参数法脆性指数计算窗体
    /// </summary>
    public partial class StaticRockMechanicsForm : Form
    {
        #region 字段和属性

        private string username;
        private DataTable mechanicsData;
        private DataTable originalMechanicsData;
        private Chart chartBrittleness;
        private string currentExcelFile;

        // 控件声明
        private Label lblTitle;
        private Label lblWelcome;
        private Button btnBack;
        private Button btnLogout;
        private Button btnEmergencyExit;

        // 数据面板控件
        private Panel pnlData;
        private Label lblDataTitle;
        private Button btnImport;
        private Button btnExport;
        private DataGridView dgvMechanicsData;

        // 图表面板控件
        private Panel pnlChart;
        private Label lblChartTitle;
        private Button btnGenerateCurve;
        private Button btnReset;
        private Button btnSaveCurve;

        // 计算参数面板控件
        private Panel pnlParameters;
        private Label lblParametersTitle;
        private Label lblDensity;
        private TextBox txtDensity;
        private Label lblVp;
        private TextBox txtVp;
        private Label lblVs;
        private TextBox txtVs;
        private Button btnCalculate;
        private Label lblCalculationResult;

        #endregion

        #region 构造函数

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public StaticRockMechanicsForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎使用静态岩石力学参数法, {username}";
            }
        }

        #endregion

        #region 初始化方法

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.Text = "静态岩石力学参数法 - 脆性指数计算";
            this.Size = new Size(1430, 950);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(33, 33, 33);
            this.ForeColor = Color.White;
            this.WindowState = FormWindowState.Maximized;

            // 创建控件
            CreateControls();

            this.ResumeLayout(false);
        }

        private void CreateControls()
        {
            // 标题
            lblTitle = new Label
            {
                Text = "静态岩石力学参数法脆性指数计算系统",
                Font = new Font("微软雅黑", 18, FontStyle.Bold),
                ForeColor = Color.Cyan,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(45, 45, 45)
            };
            this.Controls.Add(lblTitle);

            // 欢迎文本
            lblWelcome = new Label
            {
                Text = "欢迎使用静态岩石力学参数法",
                Font = new Font("微软雅黑", 12),
                ForeColor = Color.LightGray,
                Location = new Point(20, 70),
                Size = new Size(400, 30)
            };
            this.Controls.Add(lblWelcome);

            // 按钮
            CreateButtons();

            // 参数输入面板
            CreateParametersPanel();

            // 数据面板
            CreateDataPanel();

            // 图表面板
            CreateChartPanel();
        }

        private void CreateButtons()
        {
            // 返回按钮
            btnBack = new Button
            {
                Text = "返回主页",
                Font = new Font("微软雅黑", 10),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(20, 110),
                Size = new Size(120, 35)
            };
            btnBack.FlatAppearance.BorderColor = Color.Cyan;
            btnBack.Click += BtnBack_Click;
            this.Controls.Add(btnBack);

            // 退出按钮
            btnLogout = new Button
            {
                Text = "退出登录",
                Font = new Font("微软雅黑", 10),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(160, 110),
                Size = new Size(120, 35)
            };
            btnLogout.FlatAppearance.BorderColor = Color.Cyan;
            btnLogout.Click += BtnLogout_Click;
            this.Controls.Add(btnLogout);

            // 紧急退出按钮
            btnEmergencyExit = new Button
            {
                Text = "紧急退出",
                Font = new Font("微软雅黑", 9),
                BackColor = Color.FromArgb(80, 30, 30),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(this.Width - 130, 12),
                Size = new Size(115, 30)
            };
            btnEmergencyExit.FlatAppearance.BorderColor = Color.Red;
            btnEmergencyExit.Click += BtnEmergencyExit_Click;
            this.Controls.Add(btnEmergencyExit);
        }

        private void CreateParametersPanel()
        {
            pnlParameters = new Panel
            {
                BackColor = Color.FromArgb(45, 45, 45),
                BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle,
                Location = new Point(20, 160),
                Size = new Size(this.Width - 40, 120)
            };
            this.Controls.Add(pnlParameters);

            // 参数面板标题
            lblParametersTitle = new Label
            {
                Text = "岩石力学参数输入",
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(200, 25)
            };
            pnlParameters.Controls.Add(lblParametersTitle);

            // 密度输入
            lblDensity = new Label
            {
                Text = "岩石密度 ρ (g/cm³):",
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.White,
                Location = new Point(20, 50),
                Size = new Size(150, 25)
            };
            pnlParameters.Controls.Add(lblDensity);

            txtDensity = new TextBox
            {
                Font = new Font("微软雅黑", 10),
                Location = new Point(180, 50),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
            };
            pnlParameters.Controls.Add(txtDensity);

            // 纵波速度输入
            lblVp = new Label
            {
                Text = "纵波速度 Vp (m/s):",
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.White,
                Location = new Point(300, 50),
                Size = new Size(150, 25)
            };
            pnlParameters.Controls.Add(lblVp);

            txtVp = new TextBox
            {
                Font = new Font("微软雅黑", 10),
                Location = new Point(460, 50),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
            };
            pnlParameters.Controls.Add(txtVp);

            // 横波速度输入
            lblVs = new Label
            {
                Text = "横波速度 Vs (m/s):",
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.White,
                Location = new Point(580, 50),
                Size = new Size(150, 25)
            };
            pnlParameters.Controls.Add(lblVs);

            txtVs = new TextBox
            {
                Font = new Font("微软雅黑", 10),
                Location = new Point(740, 50),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
            };
            pnlParameters.Controls.Add(txtVs);

            // 计算按钮
            btnCalculate = new Button
            {
                Text = "计算脆性指数",
                Font = new Font("微软雅黑", 10),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(20, 85),
                Size = new Size(120, 30)
            };
            btnCalculate.FlatAppearance.BorderColor = Color.Cyan;
            btnCalculate.Click += BtnCalculate_Click;
            pnlParameters.Controls.Add(btnCalculate);

            // 计算结果显示
            lblCalculationResult = new Label
            {
                Text = "计算结果将在此显示",
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.Yellow,
                Location = new Point(160, 85),
                Size = new Size(400, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };
            pnlParameters.Controls.Add(lblCalculationResult);
        }

        private void CreateDataPanel()
        {
            pnlData = new Panel
            {
                BackColor = Color.FromArgb(45, 45, 45),
                BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle,
                Location = new Point(20, 300),
                Size = new Size(600, 600)
            };
            this.Controls.Add(pnlData);

            // 数据面板标题
            lblDataTitle = new Label
            {
                Text = "岩石力学参数数据",
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(200, 25)
            };
            pnlData.Controls.Add(lblDataTitle);

            // 导入按钮
            btnImport = new Button
            {
                Text = "导入数据",
                Font = new Font("微软雅黑", 9),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(pnlData.Width - 240, 10),
                Size = new Size(100, 30)
            };
            btnImport.FlatAppearance.BorderColor = Color.Cyan;
            btnImport.Click += BtnImport_Click;
            pnlData.Controls.Add(btnImport);

            // 导出按钮
            btnExport = new Button
            {
                Text = "导出数据",
                Font = new Font("微软雅黑", 9),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(pnlData.Width - 130, 10),
                Size = new Size(100, 30)
            };
            btnExport.FlatAppearance.BorderColor = Color.Cyan;
            btnExport.Click += BtnExport_Click;
            pnlData.Controls.Add(btnExport);

            // 数据网格
            dgvMechanicsData = new DataGridView
            {
                Location = new Point(10, 50),
                Size = new Size(pnlData.Width - 20, pnlData.Height - 60),
                BackgroundColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                GridColor = Color.FromArgb(80, 80, 80),
                BorderStyle = System.Windows.Forms.BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // 设置数据网格样式
            dgvMechanicsData.DefaultCellStyle.BackColor = Color.FromArgb(60, 60, 60);
            dgvMechanicsData.DefaultCellStyle.ForeColor = Color.White;
            dgvMechanicsData.DefaultCellStyle.SelectionBackColor = Color.FromArgb(100, 100, 100);
            dgvMechanicsData.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvMechanicsData.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(80, 80, 80);
            dgvMechanicsData.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvMechanicsData.EnableHeadersVisualStyles = false;

            pnlData.Controls.Add(dgvMechanicsData);
        }

        private void CreateChartPanel()
        {
            pnlChart = new Panel
            {
                BackColor = Color.FromArgb(45, 45, 45),
                BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle,
                Location = new Point(640, 300),
                Size = new Size(this.Width - 680, 600)
            };
            this.Controls.Add(pnlChart);

            // 图表面板标题
            lblChartTitle = new Label
            {
                Text = "脆性指数曲线图",
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(200, 25)
            };
            pnlChart.Controls.Add(lblChartTitle);

            // 生成曲线按钮
            btnGenerateCurve = new Button
            {
                Text = "生成曲线",
                Font = new Font("微软雅黑", 9),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(pnlChart.Width - 350, 10),
                Size = new Size(100, 30)
            };
            btnGenerateCurve.FlatAppearance.BorderColor = Color.Cyan;
            btnGenerateCurve.Click += BtnGenerateCurve_Click;
            pnlChart.Controls.Add(btnGenerateCurve);

            // 重置按钮
            btnReset = new Button
            {
                Text = "重置",
                Font = new Font("微软雅黑", 9),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(pnlChart.Width - 240, 10),
                Size = new Size(100, 30)
            };
            btnReset.FlatAppearance.BorderColor = Color.Cyan;
            btnReset.Click += BtnReset_Click;
            pnlChart.Controls.Add(btnReset);

            // 保存曲线按钮
            btnSaveCurve = new Button
            {
                Text = "保存曲线",
                Font = new Font("微软雅黑", 9),
                BackColor = Color.FromArgb(50, 50, 50),
                ForeColor = Color.LightSkyBlue,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(pnlChart.Width - 130, 10),
                Size = new Size(100, 30)
            };
            btnSaveCurve.FlatAppearance.BorderColor = Color.Cyan;
            btnSaveCurve.Click += BtnSaveCurve_Click;
            pnlChart.Controls.Add(btnSaveCurve);

            // 创建图表
            CreateChart();
        }

        private void CreateChart()
        {
            chartBrittleness = new Chart
            {
                Location = new Point(10, 50),
                Size = new Size(pnlChart.Width - 20, pnlChart.Height - 60),
                BackColor = Color.FromArgb(45, 45, 45),
                ForeColor = Color.White,
                BorderlineColor = Color.FromArgb(60, 60, 60),
                BorderlineDashStyle = ChartDashStyle.Solid,
                BorderlineWidth = 1
            };

            // 添加图表区域
            ChartArea chartArea = new ChartArea("MainArea")
            {
                BackColor = Color.FromArgb(45, 45, 45),
                BorderColor = Color.FromArgb(60, 60, 60)
            };

            // 设置X轴（脆性指数）
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisX.TitleFont = new Font("微软雅黑", 10);
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dash;

            // 设置Y轴（深度）
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisY.TitleFont = new Font("微软雅黑", 10);
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
            chartArea.AxisY.IsReversed = true; // 深度轴反向显示

            chartBrittleness.ChartAreas.Add(chartArea);

            // 添加图例
            Legend legend = new Legend
            {
                BackColor = Color.FromArgb(45, 45, 45),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 9),
                Docking = Docking.Bottom,
                Alignment = StringAlignment.Center,
                BorderColor = Color.FromArgb(60, 60, 60),
                BorderWidth = 1
            };
            chartBrittleness.Legends.Add(legend);

            pnlChart.Controls.Add(chartBrittleness);
        }

        private void InitializeForm()
        {
            // 初始化数据表
            InitializeDataTable();

            // 绑定事件
            this.Load += StaticRockMechanicsForm_Load;
            this.Resize += StaticRockMechanicsForm_Resize;
            this.FormClosing += StaticRockMechanicsForm_FormClosing;
        }

        private void InitializeDataTable()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("顶深/m", typeof(double));
            mechanicsData.Columns.Add("底深/m", typeof(double));
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            dgvMechanicsData.DataSource = mechanicsData;
        }

        #endregion

        #region 事件处理方法

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
        }

        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            // 窗体大小改变时调整控件位置
            if (pnlParameters != null)
            {
                pnlParameters.Size = new Size(this.Width - 40, 120);
            }

            if (pnlData != null)
            {
                pnlData.Size = new Size(600, this.Height - 350);
            }

            if (pnlChart != null)
            {
                pnlChart.Location = new Point(640, 300);
                pnlChart.Size = new Size(this.Width - 680, this.Height - 350);

                if (chartBrittleness != null)
                {
                    chartBrittleness.Size = new Size(pnlChart.Width - 20, pnlChart.Height - 60);
                }
            }

            if (btnEmergencyExit != null)
            {
                btnEmergencyExit.Location = new Point(this.Width - 130, 12);
            }
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnEmergencyExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取输入参数
                if (!double.TryParse(txtDensity.Text, out double density) ||
                    !double.TryParse(txtVp.Text, out double vp) ||
                    !double.TryParse(txtVs.Text, out double vs))
                {
                    MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 根据您提供的公式计算静态岩石力学参数
                var result = CalculateStaticRockMechanics(density, vp, vs);

                // 显示计算结果
                lblCalculationResult.Text = $"Ed={result.Ed:F3}GPa, μd={result.MuD:F4}, Es={result.Es:F3}GPa, μs={result.MuS:F4}, BRIT={result.BrittlenessIndex:F2}%";

                // 添加到数据表
                DataRow newRow = mechanicsData.NewRow();
                newRow["顶深/m"] = 0.0; // 默认值，用户可以在导入数据时修改
                newRow["底深/m"] = 0.0; // 默认值
                newRow["密度/(g/cm³)"] = density;
                newRow["纵波速度/(m/s)"] = vp;
                newRow["横波速度/(m/s)"] = vs;
                newRow["动态杨氏模量/GPa"] = result.Ed;
                newRow["动态泊松比"] = result.MuD;
                newRow["静态杨氏模量/GPa"] = result.Es;
                newRow["静态泊松比"] = result.MuS;
                newRow["脆性指数/%"] = result.BrittlenessIndex;

                mechanicsData.Rows.Add(newRow);

                MessageBox.Show($"计算成功！脆性指数为: {result.BrittlenessIndex:F2}%", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnImport_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mechanicsData = ds.Tables[0];
                            originalMechanicsData = mechanicsData.Copy();
                            dgvMechanicsData.DataSource = mechanicsData;

                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = "保存Excel文件";
                saveFileDialog.FileName = $"静态岩石力学参数数据_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportExcelFile(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 清除现有系列
                chartBrittleness.Series.Clear();

                // 创建脆性指数曲线系列
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Spline,
                    Color = Color.Cyan,
                    BorderWidth = 2,
                    MarkerStyle = MarkerStyle.Circle,
                    MarkerSize = 6,
                    MarkerColor = Color.Yellow
                };

                // 添加数据点
                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row["脆性指数/%"] != DBNull.Value && row["顶深/m"] != DBNull.Value)
                    {
                        double brittleness = Convert.ToDouble(row["脆性指数/%"]);
                        double depth = Convert.ToDouble(row["顶深/m"]);

                        series.Points.AddXY(brittleness, depth);
                    }
                }

                chartBrittleness.Series.Add(series);

                // 设置轴范围
                if (series.Points.Count > 0)
                {
                    var depths = series.Points.Select(p => p.YValues[0]).ToList();
                    var brittlenessValues = series.Points.Select(p => p.XValue).ToList();

                    chartBrittleness.ChartAreas[0].AxisY.Minimum = depths.Min();
                    chartBrittleness.ChartAreas[0].AxisY.Maximum = depths.Max();
                    chartBrittleness.ChartAreas[0].AxisX.Minimum = Math.Max(0, brittlenessValues.Min() - 5);
                    chartBrittleness.ChartAreas[0].AxisX.Maximum = brittlenessValues.Max() + 5;
                }

                MessageBox.Show("曲线生成成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输入框
                txtDensity.Text = "";
                txtVp.Text = "";
                txtVs.Text = "";
                lblCalculationResult.Text = "计算结果将在此显示";

                // 清空图表
                chartBrittleness.Series.Clear();

                // 重置数据表
                if (originalMechanicsData != null)
                {
                    mechanicsData = originalMechanicsData.Copy();
                    dgvMechanicsData.DataSource = mechanicsData;
                }
                else
                {
                    InitializeDataTable();
                }

                MessageBox.Show("已重置所有数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("没有曲线可以保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "PNG图片 (*.png)|*.png|JPEG图片 (*.jpg)|*.jpg";
                saveFileDialog.Title = "保存曲线图片";
                saveFileDialog.FileName = $"静态岩石力学参数脆性指数曲线_{DateTime.Now:yyyyMMdd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    chartBrittleness.SaveImage(saveFileDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show("曲线图片保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 计算方法

        /// <summary>
        /// 计算结果结构体
        /// </summary>
        public struct RockMechanicsResult
        {
            public double Ed;              // 动态杨氏模量 (GPa)
            public double MuD;             // 动态泊松比
            public double Es;              // 静态杨氏模量 (GPa)
            public double MuS;             // 静态泊松比
            public double BrittlenessIndex; // 脆性指数 (%)
        }

        /// <summary>
        /// 根据您提供的公式计算静态岩石力学参数
        /// </summary>
        /// <param name="density">岩石密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        private RockMechanicsResult CalculateStaticRockMechanics(double density, double vp, double vs)
        {
            var result = new RockMechanicsResult();

            // 转换密度单位：g/cm³ -> kg/m³
            double rho = density * 1000;

            // 计算动态杨氏模量 Ed (GPa)
            // Ed = 10^-3 * ρ * Vs^2 * (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)
            double vp2 = vp * vp;
            double vs2 = vs * vs;
            result.Ed = 1e-3 * rho * vs2 * (3 * vp2 - 4 * vs2) / (vp2 - vs2) / 1e9; // 转换为GPa

            // 计算动态泊松比 μd
            // μd = (Vp^2 - 2Vs^2) / (2(Vp^2 - Vs^2))
            result.MuD = (vp2 - 2 * vs2) / (2 * (vp2 - vs2));

            // 计算静态杨氏模量 Es (GPa)
            // Es = Ed × 0.5823 + 7.566
            result.Es = result.Ed * 0.5823 + 7.566;

            // 计算静态泊松比 μs
            // μs = μd × 0.6648 + 0.0514
            result.MuS = result.MuD * 0.6648 + 0.0514;

            // 计算脆性指数
            // 需要先计算归一化的杨氏模量和泊松比
            // 这里使用常见的岩石参数范围进行归一化
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
            double EBRIT = (result.Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内

            // 归一化泊松比脆性指数
            // μBRIT = (μmax - μs) / (μmax - μmin) × 100%
            double MuBRIT = (MuSMax - result.MuS) / (MuSMax - MuSMin) * 100;
            MuBRIT = Math.Max(0, Math.Min(100, MuBRIT)); // 限制在0-100%范围内

            // 综合脆性指数
            // BRITe = (EBRIT + μBRIT) / 2
            result.BrittlenessIndex = (EBRIT + MuBRIT) / 2;

            return result;
        }

        #endregion

        #region Excel读写方法

        /// <summary>
        /// 读取Excel文件
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j + 1}";
                                dataTable.Columns.Add(columnName, typeof(object));
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    if (cell != null)
                                    {
                                        switch (cell.CellType)
                                        {
                                            case CellType.Numeric:
                                                dataRow[cellIndex] = cell.NumericCellValue;
                                                break;
                                            case CellType.String:
                                                dataRow[cellIndex] = cell.StringCellValue;
                                                break;
                                            case CellType.Boolean:
                                                dataRow[cellIndex] = cell.BooleanCellValue;
                                                break;
                                            case CellType.Formula:
                                                try
                                                {
                                                    dataRow[cellIndex] = cell.NumericCellValue;
                                                }
                                                catch
                                                {
                                                    dataRow[cellIndex] = cell.StringCellValue;
                                                }
                                                break;
                                            default:
                                                dataRow[cellIndex] = cell.ToString();
                                                break;
                                        }
                                    }
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出Excel文件
        /// </summary>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    IWorkbook workbook = new XSSFWorkbook();
                    ISheet sheet = workbook.CreateSheet("静态岩石力学参数数据");

                    // 创建表头
                    IRow headerRow = sheet.CreateRow(0);
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        headerRow.CreateCell(i).SetCellValue(mechanicsData.Columns[i].ColumnName);
                    }

                    // 创建数据行
                    for (int i = 0; i < mechanicsData.Rows.Count; i++)
                    {
                        IRow dataRow = sheet.CreateRow(i + 1);
                        for (int j = 0; j < mechanicsData.Columns.Count; j++)
                        {
                            var cellValue = mechanicsData.Rows[i][j];
                            if (cellValue != DBNull.Value)
                            {
                                if (double.TryParse(cellValue.ToString(), out double numValue))
                                {
                                    dataRow.CreateCell(j).SetCellValue(numValue);
                                }
                                else
                                {
                                    dataRow.CreateCell(j).SetCellValue(cellValue.ToString());
                                }
                            }
                        }
                    }

                    // 自动调整列宽
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        sheet.AutoSizeColumn(i);
                    }

                    workbook.Write(stream);
                }

                MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"导出Excel文件失败: {ex.Message}");
            }
        }

        #endregion
    }
}
