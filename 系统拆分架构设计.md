# 脆性指数系统拆分架构设计

## 1. 系统概述

当前的BritSystem是一个集成的脆性指数分析系统，包含两个主要算法模块：
- **矿物组分法**：基于矿物成分计算脆性指数
- **静态岩石力学参数法**：基于岩石力学参数计算脆性指数

## 2. 拆分目标

将现有系统拆分为两个独立的项目：
1. **MineralCompositionSystem** - 矿物组分法系统
2. **StaticRockMechanicsSystem** - 静态岩石力学参数法系统

每个系统都能独立运行，具有完整的功能。

## 3. 系统架构分析

### 3.1 当前系统组件分类

#### 共享组件（需要在两个项目中复制或提取为共享库）
- **Services/**
  - `ImportService.cs` - Excel数据导入服务
  - `ExportService.cs` - 数据导出服务  
  - `LoggingService.cs` - 日志服务
- **Models/**
  - `BrittlenessDataPoint.cs` - 脆性指数数据点模型
  - `CalculationResult.cs` - 计算结果模型
- **Core/**
  - `DataManager.cs` - 数据管理器
- **通用窗体**
  - `LoginForm.cs` - 登录窗体
  - `ComparisonChartForm.cs` - 对比图窗体

#### 矿物组分法专用组件
- **Core/**
  - `AlgorithmFormulaCal.cs` - 矿物组分算法核心
  - `BrittlenessCalculator.cs` - 脆性指数计算器
  - `ColumnDetector.cs` - 列检测器
- **窗体**
  - `MineralogicalForm.cs` - 矿物学分析主窗体
  - `MineralogicalAnalysisForm.cs` - 矿物学分析窗体
  - `ManualDetectForm.cs` - 手动检测窗体
  - `ManualMappingForm.cs` - 手动映射窗体
  - `VisualizationForm.cs` - 可视化窗体
  - `MineralInputForm.cs` - 矿物输入窗体
- **控件**
  - `MineralStackedBarChartControl.cs` - 矿物堆叠柱状图控件
- **辅助类**
  - `ClayMineralColumnDetector.cs` - 黏土矿物列检测器
  - `DetectColumnsPositionNew.cs` - 列位置检测
- **模型**
  - `MineralData.cs` - 矿物数据模型

#### 静态岩石力学参数法专用组件
- **窗体**
  - `StaticRockMechanicsForm.cs` - 静态岩石力学参数主窗体
  - `BritIndexAnalysisForm.cs` - 脆性指数分析窗体

### 3.2 依赖关系分析

#### NuGet包依赖
```xml
<!-- 共享依赖 -->
<PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
<PackageReference Include="EPPlus" Version="8.0.1" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Microsoft.Web.WebView2" Version="1.0.1774.30" />
<PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />

<!-- 矿物组分法特有 -->
<PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
<PackageReference Include="System.Data.OleDb" Version="9.0.4" />

<!-- 静态岩石力学参数法特有 -->
<!-- 暂无特有依赖 -->
```

## 4. 拆分方案

### 4.1 项目结构设计

#### MineralCompositionSystem 项目结构
```
MineralCompositionSystem/
├── Core/
│   ├── AlgorithmFormulaCal.cs
│   ├── BrittlenessCalculator.cs
│   ├── ColumnDetector.cs
│   └── DataManager.cs
├── Models/
│   ├── BrittlenessDataPoint.cs
│   ├── CalculationResult.cs
│   └── MineralData.cs
├── Services/
│   ├── ImportService.cs
│   ├── ExportService.cs
│   └── LoggingService.cs
├── Forms/
│   ├── LoginForm.cs
│   ├── DashboardForm.cs
│   ├── MineralogicalForm.cs
│   ├── MineralogicalAnalysisForm.cs
│   ├── ManualDetectForm.cs
│   ├── ManualMappingForm.cs
│   ├── VisualizationForm.cs
│   ├── MineralInputForm.cs
│   └── ComparisonChartForm.cs
├── Controls/
│   └── MineralStackedBarChartControl.cs
├── Helpers/
│   ├── ClayMineralColumnDetector.cs
│   ├── DetectColumnsPositionNew.cs
│   └── VisualizationHelper.cs
└── Program.cs
```

#### StaticRockMechanicsSystem 项目结构
```
StaticRockMechanicsSystem/
├── Core/
│   └── DataManager.cs
├── Models/
│   ├── BrittlenessDataPoint.cs
│   ├── CalculationResult.cs
│   └── RockMechanicsDataPoint.cs (新增)
├── Services/
│   ├── ImportService.cs
│   ├── ExportService.cs
│   ├── LoggingService.cs
│   └── RockMechanicsCalculationService.cs (新增)
├── Forms/
│   ├── LoginForm.cs
│   ├── DashboardForm.cs
│   ├── StaticRockMechanicsForm.cs
│   ├── BritIndexAnalysisForm.cs
│   └── ComparisonChartForm.cs
└── Program.cs
```

### 4.2 共享组件处理策略

由于两个系统需要保持独立，我们采用**代码复制**的方式处理共享组件，而不是创建共享库。这样可以确保：
1. 每个系统完全独立
2. 可以独立部署和维护
3. 避免版本依赖问题

### 4.3 数据模型适配

#### 矿物组分法系统
- 保留现有的矿物相关数据模型
- 专注于矿物成分数据处理

#### 静态岩石力学参数法系统
- 新增 `RockMechanicsDataPoint` 模型
- 适配岩石力学参数数据结构

## 5. 实施步骤

1. **创建项目结构** - 创建两个新的独立项目
2. **复制共享组件** - 将共享代码复制到两个项目中
3. **移植专用组件** - 将各自专用的组件移植到对应项目
4. **调整依赖关系** - 更新命名空间和引用关系
5. **功能测试** - 确保两个系统功能完整
6. **优化和清理** - 移除不必要的代码和依赖

## 6. 预期效果

拆分完成后：
- 两个独立的可执行程序
- 各自专注于特定的计算方法
- 保持原有功能完整性
- 便于独立维护和部署
