#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PDF文本提取和引用识别
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.pdf_reader import PDFReader
from modules.citation_parser import CitationParser

def debug_pdf_extraction(pdf_path):
    """调试PDF提取过程"""
    print(f"正在分析PDF文件: {pdf_path}")
    print("=" * 50)
    
    # 创建PDF读取器
    pdf_reader = PDFReader()
    
    # 提取文本
    text = pdf_reader.extract_text(pdf_path)
    
    if not text:
        print("错误: 无法提取PDF文本")
        return
    
    print(f"提取的文本长度: {len(text)} 字符")
    print("\n前500个字符:")
    print("-" * 30)
    print(text[:500])
    print("-" * 30)
    
    # 查找可能的引用部分
    print("\n查找包含年份的行（可能的引用）:")
    print("-" * 30)
    
    lines = text.split('\n')
    potential_citations = []
    
    for i, line in enumerate(lines):
        # 查找包含4位年份的行
        import re
        if re.search(r'\b(19|20)\d{2}\b', line):
            potential_citations.append((i, line.strip()))
    
    # 显示前20个可能的引用
    for i, (line_num, line) in enumerate(potential_citations[:20]):
        print(f"{i+1:2d}. 行{line_num:3d}: {line[:100]}...")
    
    print(f"\n找到 {len(potential_citations)} 行包含年份")
    
    # 尝试识别引用
    print("\n尝试识别文献引用:")
    print("-" * 30)
    
    citations = pdf_reader.find_citations(text)
    print(f"识别到 {len(citations)} 个文献引用")
    
    for i, citation in enumerate(citations):
        print(f"{i+1}. {citation['pattern_type']}: {citation['full_match'][:100]}...")
    
    # 如果没有识别到引用，尝试查找References部分
    if len(citations) == 0:
        print("\n查找References/Bibliography部分:")
        print("-" * 30)
        
        ref_keywords = ['references', 'bibliography', 'works cited', 'literature cited']
        
        for keyword in ref_keywords:
            pattern = rf'\b{keyword}\b'
            matches = list(re.finditer(pattern, text, re.IGNORECASE))
            
            if matches:
                print(f"找到 '{keyword}' 在位置:")
                for match in matches:
                    start = max(0, match.start() - 50)
                    end = min(len(text), match.end() + 200)
                    context = text[start:end].replace('\n', ' ')
                    print(f"  位置 {match.start()}: ...{context}...")
                break

if __name__ == "__main__":
    pdf_file = "paper.pdf"
    
    if Path(pdf_file).exists():
        debug_pdf_extraction(pdf_file)
    else:
        print(f"错误: 找不到文件 {pdf_file}")
        print("请确保paper.pdf文件在当前目录中")
