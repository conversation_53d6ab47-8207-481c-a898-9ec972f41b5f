@echo off
chcp 65001 >nul
echo Starting Literature Download Automation Program...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check dependencies
echo Checking dependencies...
python -c "import tkinter, PIL, requests, PyPDF2, pdfplumber, pytesseract" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start program
echo Starting program...
python main.py

if errorlevel 1 (
    echo Program error occurred
    pause
)
