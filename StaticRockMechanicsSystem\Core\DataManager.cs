using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace StaticRockMechanicsSystem.Core
{
    /// <summary>
    /// 数据管理器类，负责数据的加载、保存和处理
    /// </summary>
    public class DataManager
    {
        #region 私有字段

        private DataTable _sourceData;
        private readonly Dictionary<string, string> _columnMappings;
        private string _lastLoadedFilePath;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public DataManager()
        {
            _sourceData = new DataTable();
            _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _lastLoadedFilePath = string.Empty;
        }

        /// <summary>
        /// 带数据的构造函数
        /// </summary>
        /// <param name="data">数据表</param>
        public DataManager(DataTable data)
        {
            _sourceData = data?.Copy() ?? new DataTable();
            _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _lastLoadedFilePath = string.Empty;
        }

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置源数据
        /// </summary>
        public DataTable SourceData => _sourceData;

        /// <summary>
        /// 获取列名映射
        /// </summary>
        public Dictionary<string, string> ColumnMappings => _columnMappings;

        /// <summary>
        /// 获取最后加载的文件路径
        /// </summary>
        public string LastLoadedFilePath => _lastLoadedFilePath;

        #endregion

        #region 公共方法

        /// <summary>
        /// 从Excel文件加载数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>成功返回true，失败返回false</returns>
        public bool LoadFromExcel(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            try
            {
                IWorkbook workbook = null;
                FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);

                // 根据文件扩展名确定Excel版本
                if (filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook(fs);
                }
                else if (filePath.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new HSSFWorkbook(fs);
                }
                else
                {
                    fs.Close();
                    return false;
                }

                // 获取第一个工作表
                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet == null)
                {
                    fs.Close();
                    workbook.Close();
                    return false;
                }

                // 创建数据表并设置列
                DataTable dataTable = new DataTable();
                IRow headerRow = sheet.GetRow(0);
                if (headerRow == null)
                {
                    fs.Close();
                    workbook.Close();
                    return false;
                }

                // 添加列
                int cellCount = headerRow.LastCellNum;
                for (int i = 0; i < cellCount; i++)
                {
                    ICell cell = headerRow.GetCell(i);
                    if (cell != null)
                    {
                        string columnName = cell.ToString();
                        if (string.IsNullOrEmpty(columnName))
                            columnName = $"Column{i}";

                        // 确保列名唯一
                        if (dataTable.Columns.Contains(columnName))
                            columnName = $"{columnName}_{i}";

                        dataTable.Columns.Add(columnName);
                    }
                    else
                    {
                        dataTable.Columns.Add($"Column{i}");
                    }
                }

                // 添加数据行
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                        continue;

                    DataRow dataRow = dataTable.NewRow();
                    for (int j = 0; j < cellCount; j++)
                    {
                        ICell cell = row.GetCell(j);
                        if (cell != null)
                        {
                            // 根据单元格类型获取值
                            switch (cell.CellType)
                            {
                                case CellType.Numeric:
                                    if (DateUtil.IsCellDateFormatted(cell))
                                    {
                                        dataRow[j] = cell.DateCellValue;
                                    }
                                    else
                                    {
                                        dataRow[j] = cell.NumericCellValue;
                                    }
                                    break;
                                case CellType.String:
                                    dataRow[j] = cell.StringCellValue;
                                    break;
                                case CellType.Boolean:
                                    dataRow[j] = cell.BooleanCellValue;
                                    break;
                                case CellType.Formula:
                                    // 对于公式单元格，获取计算结果
                                    switch (cell.CachedFormulaResultType)
                                    {
                                        case CellType.Numeric:
                                            dataRow[j] = cell.NumericCellValue;
                                            break;
                                        case CellType.String:
                                            dataRow[j] = cell.StringCellValue;
                                            break;
                                        case CellType.Boolean:
                                            dataRow[j] = cell.BooleanCellValue;
                                            break;
                                        default:
                                            dataRow[j] = cell.ToString();
                                            break;
                                    }
                                    break;
                                default:
                                    dataRow[j] = cell.ToString();
                                    break;
                            }
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }

                // 设置源数据并保存文件路径
                _sourceData = dataTable;
                _lastLoadedFilePath = filePath;

                // 关闭资源
                fs.Close();
                workbook.Close();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载Excel文件出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将数据表保存为Excel文件
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <param name="filePath">文件保存路径</param>
        /// <returns>成功返回true，失败返回false</returns>
        public bool SaveToExcel(DataTable dataTable, string filePath)
        {
            if (dataTable == null || string.IsNullOrEmpty(filePath))
                return false;

            try
            {
                IWorkbook workbook;
                if (filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook();
                }
                else if (filePath.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new HSSFWorkbook();
                }
                else
                {
                    // 默认使用.xlsx格式
                    filePath = Path.ChangeExtension(filePath, ".xlsx");
                    workbook = new XSSFWorkbook();
                }

                // 创建工作表
                ISheet sheet = workbook.CreateSheet("数据");

                // 创建标题行
                IRow headerRow = sheet.CreateRow(0);

                // 设置标题列样式（不使用Bold属性）
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.Boldweight = (short)NPOI.SS.UserModel.FontBoldWeight.Bold;
                headerStyle.SetFont(headerFont);

                // 创建标题行
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(dataTable.Columns[i].ColumnName);
                }

                // 创建数据行
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    IRow row = sheet.CreateRow(i + 1);
                    for (int j = 0; j < dataTable.Columns.Count; j++)
                    {
                        ICell cell = row.CreateCell(j);
                        object value = dataTable.Rows[i][j];
                        if (value != null && value != DBNull.Value)
                        {
                            if (value is double doubleValue)
                            {
                                cell.SetCellValue(doubleValue);
                            }
                            else if (value is DateTime dateTimeValue)
                            {
                                cell.SetCellValue(dateTimeValue);
                            }
                            else if (value is bool boolValue)
                            {
                                cell.SetCellValue(boolValue);
                            }
                            else
                            {
                                cell.SetCellValue(value.ToString());
                            }
                        }
                    }
                }

                // 自动调整列宽
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }

                workbook.Close();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存Excel文件出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将数据表保存为Excel文件 - 兼容旧版API（参数顺序颠倒）
        /// </summary>
        /// <param name="filePath">文件保存路径</param>
        /// <param name="dataTable">数据表</param>
        /// <returns>成功返回true，失败返回false</returns>
        public bool SaveToExcel(string filePath, DataTable dataTable)
        {
            // 调用新方法，但参数顺序调整
            return SaveToExcel(dataTable, filePath);
        }

        /// <summary>
        /// 添加列映射
        /// </summary>
        /// <param name="sourceColumn">源列名</param>
        /// <param name="targetColumn">目标列名</param>
        public void AddColumnMapping(string sourceColumn, string targetColumn)
        {
            if (!string.IsNullOrEmpty(sourceColumn) && !string.IsNullOrEmpty(targetColumn))
            {
                _columnMappings[sourceColumn] = targetColumn;
                Debug.WriteLine($"添加列映射: {sourceColumn} -> {targetColumn}");
            }
        }

        /// <summary>
        /// 移除列映射
        /// </summary>
        /// <param name="sourceColumn">源列名</param>
        /// <returns>移除成功返回true，否则返回false</returns>
        public bool RemoveColumnMapping(string sourceColumn)
        {
            if (_columnMappings.ContainsKey(sourceColumn))
            {
                _columnMappings.Remove(sourceColumn);
                Debug.WriteLine($"移除列映射: {sourceColumn}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清空所有列映射
        /// </summary>
        public void ClearColumnMappings()
        {
            _columnMappings.Clear();
            Debug.WriteLine("清空所有列映射");
        }

        /// <summary>
        /// 初始化标准列映射
        /// </summary>
        public void InitializeStandardMappings()
        {
            // 清空现有映射
            _columnMappings.Clear();

            // 添加标准映射
            _columnMappings.Add("顶深", "顶深");
            _columnMappings.Add("底深", "底深");
            _columnMappings.Add("石英", "石英");
            _columnMappings.Add("长石", "长石");
            _columnMappings.Add("碳酸盐", "碳酸盐");
            _columnMappings.Add("黏土", "黏土");
            _columnMappings.Add("TOC", "TOC");

            // 黏土矿物总量相关
            _columnMappings.Add("黏土矿物总量%", "黏土矿物总量");
            _columnMappings.Add("黏土矿物总量", "黏土矿物总量");
            _columnMappings.Add("粘土矿物总量%", "黏土矿物总量");
            _columnMappings.Add("粘土矿物总量", "黏土矿物总量");
            _columnMappings.Add("黏土矿物相对含量/%", "黏土矿物总量");
            _columnMappings.Add("黏土矿物相对含量%", "黏土矿物总量");
            _columnMappings.Add("黏土矿物相对含量", "黏土矿物总量");
            _columnMappings.Add("粘土矿物相对含量/%", "黏土矿物总量");
            _columnMappings.Add("粘土矿物相对含量%", "黏土矿物总量");
            _columnMappings.Add("粘土矿物相对含量", "黏土矿物总量");

            Debug.WriteLine("初始化标准列映射完成");
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 尝试将数据列转换为数值类型
        /// </summary>
        /// <param name="dataTable">数据表</param>
        private void ConvertColumnsToNumeric(DataTable dataTable)
        {
            foreach (DataColumn column in dataTable.Columns)
            {
                bool allNumeric = true;
                List<double> numericValues = new List<double>();

                // 检查是否所有非空值都是数值
                foreach (DataRow row in dataTable.Rows)
                {
                    if (row[column] == DBNull.Value || row[column] == null)
                    {
                        continue;
                    }

                    string value = row[column].ToString();
                    if (!double.TryParse(value, out double numericValue))
                    {
                        allNumeric = false;
                        break;
                    }

                    numericValues.Add(numericValue);
                }

                // 如果列中的所有非空值都是数值，则转换列类型
                if (allNumeric && numericValues.Count > 0)
                {
                    // 创建新列并复制数据
                    DataColumn newColumn = new DataColumn(column.ColumnName, typeof(double));
                    dataTable.Columns.Add(newColumn);

                    foreach (DataRow row in dataTable.Rows)
                    {
                        if (row[column] == DBNull.Value || row[column] == null)
                        {
                            row[newColumn] = DBNull.Value;
                        }
                        else
                        {
                            row[newColumn] = double.Parse(row[column].ToString());
                        }
                    }

                    // 移除原列并重命名新列
                    int oldIndex = dataTable.Columns.IndexOf(column);
                    int newIndex = dataTable.Columns.IndexOf(newColumn);
                    dataTable.Columns.Remove(column);
                    newColumn.ColumnName = column.ColumnName;
                }
            }
        }

        #endregion
    }
}