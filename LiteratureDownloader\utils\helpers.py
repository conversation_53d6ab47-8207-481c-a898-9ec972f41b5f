#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
辅助工具函数
提供各种实用的辅助功能
"""

import re
import os
import hashlib
from pathlib import Path
from typing import List, Dict, Optional, Any
from urllib.parse import urlparse
from loguru import logger


def clean_text(text: str) -> str:
    """清理文本，移除多余的空白字符"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除控制字符
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    return text


def extract_doi_from_text(text: str) -> Optional[str]:
    """从文本中提取DOI"""
    if not text:
        return None
    
    # DOI模式
    doi_patterns = [
        r'doi:\s*(10\.\d+\/[^\s]+)',
        r'DOI:\s*(10\.\d+\/[^\s]+)',
        r'https?://doi\.org/(10\.\d+\/[^\s]+)',
        r'https?://dx\.doi\.org/(10\.\d+\/[^\s]+)',
        r'\b(10\.\d+\/[^\s]+)\b'
    ]
    
    for pattern in doi_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            doi = match.group(1)
            # 验证DOI格式
            if re.match(r'^10\.\d+\/[^\s]+$', doi):
                return doi
    
    return None


def extract_year_from_text(text: str) -> Optional[str]:
    """从文本中提取年份"""
    if not text:
        return None
    
    # 查找4位数年份
    year_match = re.search(r'\b(19|20)\d{2}\b', text)
    if year_match:
        year = int(year_match.group(0))
        # 验证年份合理性
        if 1900 <= year <= 2030:
            return str(year)
    
    return None


def normalize_title(title: str) -> str:
    """标准化标题"""
    if not title:
        return ""
    
    # 移除引号
    title = re.sub(r'^["\']|["\']$', '', title.strip())
    
    # 标准化空白字符
    title = re.sub(r'\s+', ' ', title)
    
    # 移除末尾的标点符号
    title = re.sub(r'[.,;:]+$', '', title)
    
    return title.strip()


def parse_author_list(author_text: str) -> List[str]:
    """解析作者列表"""
    if not author_text:
        return []
    
    # 分割作者
    separators = [' and ', ' & ', ', ', ';', '\n']
    authors = [author_text]
    
    for sep in separators:
        new_authors = []
        for author in authors:
            new_authors.extend(author.split(sep))
        authors = new_authors
    
    # 清理作者名
    cleaned_authors = []
    for author in authors:
        author = author.strip()
        if author and len(author) > 1:
            # 移除多余的空格
            author = re.sub(r'\s+', ' ', author)
            # 移除特殊字符
            author = re.sub(r'[^\w\s\.\-\']', '', author)
            if author:
                cleaned_authors.append(author)
    
    return cleaned_authors


def calculate_text_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度（简单版本）"""
    if not text1 or not text2:
        return 0.0
    
    # 转换为小写并分词
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    # 计算Jaccard相似度
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    if union == 0:
        return 0.0
    
    return intersection / union


def is_valid_url(url: str) -> bool:
    """验证URL是否有效"""
    if not url:
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def get_file_hash(file_path: str, algorithm: str = 'md5') -> Optional[str]:
    """计算文件哈希值"""
    try:
        hash_func = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        
        return hash_func.hexdigest()
        
    except Exception as e:
        logger.error(f"计算文件哈希失败: {e}")
        return None


def safe_filename(filename: str, max_length: int = 200) -> str:
    """生成安全的文件名"""
    if not filename:
        return "unnamed_file"
    
    # 移除或替换非法字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # 移除多余的空格和点
    filename = re.sub(r'[\s\.]+', '_', filename)
    
    # 移除开头和结尾的下划线
    filename = filename.strip('_')
    
    # 限制长度
    if len(filename) > max_length:
        name, ext = os.path.splitext(filename)
        filename = name[:max_length - len(ext)] + ext
    
    # 确保不为空
    if not filename:
        filename = "unnamed_file"
    
    return filename


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def create_directory_if_not_exists(directory: str) -> bool:
    """创建目录（如果不存在）"""
    try:
        Path(directory).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False


def is_pdf_file(file_path: str) -> bool:
    """检查是否为PDF文件"""
    try:
        if not Path(file_path).exists():
            return False
        
        # 检查扩展名
        if not file_path.lower().endswith('.pdf'):
            return False
        
        # 检查文件头
        with open(file_path, 'rb') as f:
            header = f.read(4)
            return header == b'%PDF'
            
    except Exception:
        return False


def extract_numbers_from_text(text: str) -> List[int]:
    """从文本中提取所有数字"""
    if not text:
        return []
    
    numbers = re.findall(r'\d+', text)
    return [int(num) for num in numbers]


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截断文本"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def merge_dicts(*dicts) -> Dict[str, Any]:
    """合并多个字典"""
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def filter_dict_by_keys(data: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
    """根据键过滤字典"""
    return {k: v for k, v in data.items() if k in keys}


def validate_email(email: str) -> bool:
    """验证邮箱地址格式"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def extract_domain_from_url(url: str) -> Optional[str]:
    """从URL中提取域名"""
    try:
        parsed = urlparse(url)
        return parsed.netloc
    except Exception:
        return None


def count_words(text: str) -> int:
    """统计文本中的单词数"""
    if not text:
        return 0
    
    # 简单的单词计数
    words = re.findall(r'\b\w+\b', text)
    return len(words)


def remove_duplicates_preserve_order(items: List[Any]) -> List[Any]:
    """去除列表中的重复项，保持顺序"""
    seen = set()
    result = []
    
    for item in items:
        if item not in seen:
            seen.add(item)
            result.append(item)
    
    return result


def chunk_list(items: List[Any], chunk_size: int) -> List[List[Any]]:
    """将列表分块"""
    if chunk_size <= 0:
        return [items]
    
    return [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]


def retry_on_exception(func, max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    import time
    
    def wrapper(*args, **kwargs):
        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries:
                    raise e
                logger.warning(f"尝试 {attempt + 1} 失败: {e}, {delay}秒后重试")
                time.sleep(delay)
    
    return wrapper
