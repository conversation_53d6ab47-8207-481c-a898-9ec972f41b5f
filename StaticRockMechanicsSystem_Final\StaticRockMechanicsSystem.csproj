<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- 禁用默认项目项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <!-- 禁用自动生成资源名称 -->
    <GenerateResourceMSBuildArchitecture>CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime>CurrentRuntime</GenerateResourceMSBuildRuntime>
    <!-- 强制使用显式指定的ManifestResourceName -->
    <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
    <!-- 高DPI支持设置 -->
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <AssemblyTitle>静态岩石力学参数法脆性指数分析系统</AssemblyTitle>
    <Product>静态岩石力学参数法脆性指数分析系统</Product>
    <Description>基于岩石力学参数计算脆性指数的专业地质分析软件</Description>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <!-- 包引用 -->
  <ItemGroup>
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="EPPlus" Version="8.0.1" />
    <PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.1774.30" />
    <PackageReference Include="Microsoft.Web.WebView2.DevToolsProtocolExtension" Version="1.0.824" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <!-- 源代码文件 -->
  <ItemGroup>
    <!-- 核心类 -->
    <Compile Include="Core\DataManager.cs" />

    <!-- 模型类 -->
    <Compile Include="Models\BrittlenessDataPoint.cs" />
    <Compile Include="Models\CalculationResult.cs" />
    <Compile Include="Models\RockMechanicsDataPoint.cs" />

    <!-- 服务类 -->
    <Compile Include="Services\ImportService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\LoggingService.cs" />
    <Compile Include="Services\RockMechanicsCalculationService.cs" />

    <!-- 窗体类 -->
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StaticRockMechanicsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StaticRockMechanicsForm.Designer.cs">
      <DependentUpon>StaticRockMechanicsForm.cs</DependentUpon>
    </Compile>


    <!-- 主程序 -->
    <Compile Include="Program.cs" />
    <Compile Include="AppConfig.cs" />
  </ItemGroup>

  <!-- 资源文件 -->
  <ItemGroup>
    <EmbeddedResource Include="Forms\LoginForm.resx" />
    <EmbeddedResource Include="Forms\StaticRockMechanicsForm.resx" />
  </ItemGroup>



</Project>
