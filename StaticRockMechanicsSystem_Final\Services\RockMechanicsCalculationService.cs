using System;
using System.Data;
using StaticRockMechanicsSystem.Models;

namespace StaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 岩石力学参数计算服务
    /// </summary>
    public class RockMechanicsCalculationService
    {
        /// <summary>
        /// 计算动态岩石力学参数
        /// </summary>
        /// <param name="density">密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        public static (double Ed, double MuD) CalculateDynamicParameters(double density, double vp, double vs)
        {
            // 转换密度单位：g/cm³ -> kg/m³
            double densityKgM3 = density * 1000;

            // 计算动态杨氏模量 Ed (Pa)
            double Ed = densityKgM3 * vs * vs * (3 * vp * vp - 4 * vs * vs) / (vp * vp - vs * vs);

            // 计算动态泊松比 μd
            double MuD = (vp * vp - 2 * vs * vs) / (2 * (vp * vp - vs * vs));

            // 转换杨氏模量单位：Pa -> GPa
            Ed = Ed / 1e9;

            return (Ed, MuD);
        }

        /// <summary>
        /// 计算静态岩石力学参数
        /// </summary>
        /// <param name="Ed">动态杨氏模量 (GPa)</param>
        /// <param name="MuD">动态泊松比</param>
        /// <returns>计算结果</returns>
        public static (double Es, double MuS) CalculateStaticParameters(double Ed, double MuD)
        {
            // 使用经验公式计算静态杨氏模量
            double Es = 0.0289 * Ed * Ed + 0.2676 * Ed;

            // 使用经验公式计算静态泊松比
            double MuS = 1.0 / (1.0 + 0.8 * (1.0 / MuD - 1.0));

            return (Es, MuS);
        }

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        /// <param name="Es">静态杨氏模量 (GPa)</param>
        /// <param name="MuS">静态泊松比</param>
        /// <param name="EsMin">杨氏模量最小值</param>
        /// <param name="EsMax">杨氏模量最大值</param>
        /// <param name="MuSMin">泊松比最小值</param>
        /// <param name="MuSMax">泊松比最大值</param>
        /// <returns>脆性指数 (%)</returns>
        public static double CalculateBrittlenessIndex(double Es, double MuS, 
            double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量
            double normalizedEs = (Es - EsMin) / (EsMax - EsMin);
            normalizedEs = Math.Max(0, Math.Min(1, normalizedEs)); // 限制在[0,1]范围内

            // 归一化泊松比（注意：泊松比越小，脆性越大）
            double normalizedMuS = (MuSMax - MuS) / (MuSMax - MuSMin);
            normalizedMuS = Math.Max(0, Math.Min(1, normalizedMuS)); // 限制在[0,1]范围内

            // 计算脆性指数（百分比）
            double brittlenessIndex = (normalizedEs + normalizedMuS) / 2.0 * 100.0;

            return brittlenessIndex;
        }

        /// <summary>
        /// 完整的岩石力学参数计算
        /// </summary>
        /// <param name="density">密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <param name="EsMin">杨氏模量最小值</param>
        /// <param name="EsMax">杨氏模量最大值</param>
        /// <param name="MuSMin">泊松比最小值</param>
        /// <param name="MuSMax">泊松比最大值</param>
        /// <returns>完整的计算结果</returns>
        public static RockMechanicsDataPoint CalculateComplete(double density, double vp, double vs,
            double EsMin = 5.0, double EsMax = 80.0, double MuSMin = 0.1, double MuSMax = 0.4)
        {
            var result = new RockMechanicsDataPoint
            {
                Density = density,
                VelocityP = vp,
                VelocityS = vs
            };

            // 计算动态参数
            var (Ed, MuD) = CalculateDynamicParameters(density, vp, vs);
            result.DynamicYoungsModulus = Ed;
            result.DynamicPoissonsRatio = MuD;

            // 计算静态参数
            var (Es, MuS) = CalculateStaticParameters(Ed, MuD);
            result.StaticYoungsModulus = Es;
            result.StaticPoissonsRatio = MuS;

            // 计算脆性指数
            result.BrittlenessIndex = CalculateBrittlenessIndex(Es, MuS, EsMin, EsMax, MuSMin, MuSMax);

            // 生成GeoID
            result.GenerateGeoID();

            return result;
        }

        /// <summary>
        /// 验证输入参数的有效性
        /// </summary>
        /// <param name="density">密度</param>
        /// <param name="vp">纵波速度</param>
        /// <param name="vs">横波速度</param>
        /// <returns>验证结果和错误信息</returns>
        public static (bool IsValid, string ErrorMessage) ValidateInputParameters(double density, double vp, double vs)
        {
            if (density <= 0)
                return (false, "密度必须大于0");

            if (vp <= 0)
                return (false, "纵波速度必须大于0");

            if (vs <= 0)
                return (false, "横波速度必须大于0");

            if (vp <= vs)
                return (false, "纵波速度必须大于横波速度");

            if (vp <= Math.Sqrt(2) * vs)
                return (false, "输入数据可能异常（Vp ≤ √2·Vs），请检查数据准确性");

            return (true, string.Empty);
        }
    }
}
