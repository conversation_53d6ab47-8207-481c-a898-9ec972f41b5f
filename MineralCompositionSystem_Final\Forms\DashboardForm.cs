using System;
using System.Windows.Forms;
using System.Drawing;

namespace MineralCompositionSystem.Forms
{
    public class DashboardForm : Form
    {
        private Label lblTitle;
        private Button btnMineralogical;
        private Button btnExit;
        private string username;

        public DashboardForm(string username)
        {
            this.username = username;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            lblTitle = new Label();
            btnMineralogical = new Button();
            btnExit = new Button();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Padding = new Padding(0, 10, 0, 0);
            lblTitle.Size = new Size(578, 73);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "矿物组分法脆性指数系统V1.0";
            lblTitle.TextAlign = ContentAlignment.TopCenter;
            // 
            // btnMineralogical
            // 
            btnMineralogical.BackColor = Color.FromArgb(50, 50, 50);
            btnMineralogical.FlatAppearance.BorderColor = Color.Cyan;
            btnMineralogical.FlatStyle = FlatStyle.Flat;
            btnMineralogical.Font = new Font("微软雅黑", 12F);
            btnMineralogical.ForeColor = Color.LightSkyBlue;
            btnMineralogical.Location = new Point(100, 100);
            btnMineralogical.Name = "btnMineralogical";
            btnMineralogical.Size = new Size(180, 120);
            btnMineralogical.TabIndex = 1;
            btnMineralogical.Text = "开始分析";
            btnMineralogical.UseVisualStyleBackColor = false;
            btnMineralogical.Click += BtnMineralogical_Click;
            // 
            // btnExit
            // 
            btnExit.BackColor = Color.FromArgb(60, 60, 60);
            btnExit.FlatAppearance.BorderColor = Color.Cyan;
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.Font = new Font("微软雅黑", 10F);
            btnExit.ForeColor = Color.White;
            btnExit.Location = new Point(250, 250);
            btnExit.Name = "btnExit";
            btnExit.Size = new Size(114, 40);
            btnExit.TabIndex = 3;
            btnExit.Text = "退出系统";
            btnExit.UseVisualStyleBackColor = false;
            btnExit.Click += BtnExit_Click;
            // 
            // DashboardForm
            // 
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(578, 344);
            Controls.Add(lblTitle);
            Controls.Add(btnMineralogical);
            Controls.Add(btnExit);
            Name = "DashboardForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "矿物组分法脆性指数系统V1.0";
            ResumeLayout(false);
        }

        private void LoadData()
        {
            // 简化版本，不需要加载数据
        }

        private void BtnMineralogical_Click(object sender, EventArgs e)
        {
            // 打开矿物组分法分析功能
            MessageBox.Show("矿物组分法脆性指数分析功能\n\n" +
                          "此功能用于基于矿物成分计算脆性指数。\n" +
                          "请导入包含矿物成分数据的Excel文件进行分析。\n\n" +
                          "系统已成功启动！",
                          "矿物组分法系统",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Information);
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            // 退出系统
            Application.Exit();
        }


    }
}
