using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace BritSystem.Controls
{
    /// <summary>
    /// 手动坐标轴控件
    /// 支持自定义X轴和Y轴，Y轴绑定深度数据
    /// </summary>
    public partial class ManualAxisControl : UserControl
    {
        #region 私有字段

        private List<double> _depthData;
        private double _xMin = 0;
        private double _xMax = 100;
        private double _xInterval = 20;
        private string _xTitle = "X轴";
        private string _yTitle = "深度/m";
        private Font _axisFont = new Font("Microsoft YaHei", 9);
        private Font _titleFont = new Font("Microsoft YaHei", 10, FontStyle.Bold);
        private Color _axisColor = Color.Black;
        private Color _gridColor = Color.LightGray;
        private bool _showGrid = true;
        private int _marginLeft = 80;
        private int _marginRight = 20;
        private int _marginTop = 40;
        private int _marginBottom = 60;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取或设置深度数据
        /// </summary>
        [Browsable(false)]
        public List<double> DepthData
        {
            get { return _depthData; }
            set
            {
                _depthData = value ?? new List<double>();
                Invalidate();
            }
        }

        /// <summary>
        /// X轴最小值
        /// </summary>
        [Category("X轴设置")]
        [Description("X轴最小值")]
        public double XMinimum
        {
            get { return _xMin; }
            set { _xMin = value; Invalidate(); }
        }

        /// <summary>
        /// X轴最大值
        /// </summary>
        [Category("X轴设置")]
        [Description("X轴最大值")]
        public double XMaximum
        {
            get { return _xMax; }
            set { _xMax = value; Invalidate(); }
        }

        /// <summary>
        /// X轴间隔
        /// </summary>
        [Category("X轴设置")]
        [Description("X轴刻度间隔")]
        public double XInterval
        {
            get { return _xInterval; }
            set { _xInterval = value; Invalidate(); }
        }

        /// <summary>
        /// X轴标题
        /// </summary>
        [Category("X轴设置")]
        [Description("X轴标题")]
        public string XTitle
        {
            get { return _xTitle; }
            set { _xTitle = value ?? "X轴"; Invalidate(); }
        }

        /// <summary>
        /// Y轴标题
        /// </summary>
        [Category("Y轴设置")]
        [Description("Y轴标题")]
        public string YTitle
        {
            get { return _yTitle; }
            set { _yTitle = value ?? "Y轴"; Invalidate(); }
        }

        /// <summary>
        /// 是否显示网格
        /// </summary>
        [Category("外观")]
        [Description("是否显示网格线")]
        public bool ShowGrid
        {
            get { return _showGrid; }
            set { _showGrid = value; Invalidate(); }
        }

        /// <summary>
        /// 坐标轴颜色
        /// </summary>
        [Category("外观")]
        [Description("坐标轴颜色")]
        public Color AxisColor
        {
            get { return _axisColor; }
            set { _axisColor = value; Invalidate(); }
        }

        /// <summary>
        /// 网格颜色
        /// </summary>
        [Category("外观")]
        [Description("网格线颜色")]
        public Color GridColor
        {
            get { return _gridColor; }
            set { _gridColor = value; Invalidate(); }
        }

        #endregion

        #region 构造函数

        public ManualAxisControl()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            _depthData = new List<double>();
        }

        #endregion

        #region 初始化

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ManualAxisControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "ManualAxisControl";
            this.Size = new System.Drawing.Size(600, 400);
            this.Paint += new System.Windows.Forms.PaintEventHandler(this.ManualAxisControl_Paint);
            this.ResumeLayout(false);
        }

        #endregion

        #region 绘制方法

        private void ManualAxisControl_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.Clear(this.BackColor);

            // 计算绘图区域
            Rectangle plotArea = new Rectangle(
                _marginLeft,
                _marginTop,
                this.Width - _marginLeft - _marginRight,
                this.Height - _marginTop - _marginBottom
            );

            // 绘制坐标轴
            DrawAxes(g, plotArea);

            // 绘制网格
            if (_showGrid)
            {
                DrawGrid(g, plotArea);
            }

            // 绘制刻度和标签
            DrawXAxisTicks(g, plotArea);
            DrawYAxisTicks(g, plotArea);

            // 绘制标题
            DrawTitles(g, plotArea);
        }

        /// <summary>
        /// 绘制坐标轴
        /// </summary>
        private void DrawAxes(Graphics g, Rectangle plotArea)
        {
            using (Pen axisPen = new Pen(_axisColor, 2))
            {
                // 绘制X轴
                g.DrawLine(axisPen, plotArea.Left, plotArea.Bottom, plotArea.Right, plotArea.Bottom);
                
                // 绘制Y轴
                g.DrawLine(axisPen, plotArea.Left, plotArea.Top, plotArea.Left, plotArea.Bottom);
            }
        }

        /// <summary>
        /// 绘制网格
        /// </summary>
        private void DrawGrid(Graphics g, Rectangle plotArea)
        {
            using (Pen gridPen = new Pen(_gridColor, 1))
            {
                gridPen.DashStyle = DashStyle.Dash;

                // 绘制X轴网格线
                for (double x = _xMin; x <= _xMax; x += _xInterval)
                {
                    int pixelX = (int)(plotArea.Left + (x - _xMin) / (_xMax - _xMin) * plotArea.Width);
                    g.DrawLine(gridPen, pixelX, plotArea.Top, pixelX, plotArea.Bottom);
                }

                // 绘制Y轴网格线（基于深度数据）
                if (_depthData != null && _depthData.Count > 0)
                {
                    double yMin = _depthData.Min();
                    double yMax = _depthData.Max();
                    double yRange = yMax - yMin;
                    double yInterval = Math.Max(100, yRange / 10); // 至少100m间隔

                    for (double y = yMin; y <= yMax; y += yInterval)
                    {
                        int pixelY = (int)(plotArea.Bottom - (y - yMin) / yRange * plotArea.Height);
                        g.DrawLine(gridPen, plotArea.Left, pixelY, plotArea.Right, pixelY);
                    }
                }
            }
        }

        /// <summary>
        /// 绘制X轴刻度和标签
        /// </summary>
        private void DrawXAxisTicks(Graphics g, Rectangle plotArea)
        {
            using (Pen tickPen = new Pen(_axisColor, 1))
            using (Brush textBrush = new SolidBrush(_axisColor))
            {
                for (double x = _xMin; x <= _xMax; x += _xInterval)
                {
                    int pixelX = (int)(plotArea.Left + (x - _xMin) / (_xMax - _xMin) * plotArea.Width);
                    
                    // 绘制刻度线
                    g.DrawLine(tickPen, pixelX, plotArea.Bottom, pixelX, plotArea.Bottom + 5);
                    
                    // 绘制标签
                    string label = x.ToString("F0");
                    SizeF labelSize = g.MeasureString(label, _axisFont);
                    g.DrawString(label, _axisFont, textBrush, 
                        pixelX - labelSize.Width / 2, plotArea.Bottom + 8);
                }
            }
        }

        /// <summary>
        /// 绘制Y轴刻度和标签
        /// </summary>
        private void DrawYAxisTicks(Graphics g, Rectangle plotArea)
        {
            if (_depthData == null || _depthData.Count == 0)
                return;

            using (Pen tickPen = new Pen(_axisColor, 1))
            using (Brush textBrush = new SolidBrush(_axisColor))
            {
                double yMin = _depthData.Min();
                double yMax = _depthData.Max();
                double yRange = yMax - yMin;
                double yInterval = Math.Max(100, yRange / 10); // 至少100m间隔

                for (double y = yMin; y <= yMax; y += yInterval)
                {
                    int pixelY = (int)(plotArea.Bottom - (y - yMin) / yRange * plotArea.Height);
                    
                    // 绘制刻度线
                    g.DrawLine(tickPen, plotArea.Left - 5, pixelY, plotArea.Left, pixelY);
                    
                    // 绘制标签
                    string label = y.ToString("F0");
                    SizeF labelSize = g.MeasureString(label, _axisFont);
                    g.DrawString(label, _axisFont, textBrush, 
                        plotArea.Left - labelSize.Width - 8, pixelY - labelSize.Height / 2);
                }
            }
        }

        /// <summary>
        /// 绘制标题
        /// </summary>
        private void DrawTitles(Graphics g, Rectangle plotArea)
        {
            using (Brush titleBrush = new SolidBrush(_axisColor))
            {
                // 绘制X轴标题
                SizeF xTitleSize = g.MeasureString(_xTitle, _titleFont);
                g.DrawString(_xTitle, _titleFont, titleBrush,
                    plotArea.Left + (plotArea.Width - xTitleSize.Width) / 2,
                    plotArea.Bottom + 35);

                // 绘制Y轴标题（旋转90度）
                g.TranslateTransform(15, plotArea.Top + plotArea.Height / 2);
                g.RotateTransform(-90);
                SizeF yTitleSize = g.MeasureString(_yTitle, _titleFont);
                g.DrawString(_yTitle, _titleFont, titleBrush, -yTitleSize.Width / 2, 0);
                g.ResetTransform();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置X轴参数
        /// </summary>
        public void SetXAxis(double min, double max, double interval, string title = null)
        {
            _xMin = min;
            _xMax = max;
            _xInterval = interval;
            if (!string.IsNullOrEmpty(title))
                _xTitle = title;
            Invalidate();
        }

        /// <summary>
        /// 设置深度数据并更新Y轴
        /// </summary>
        public void SetDepthData(List<double> depths, string title = null)
        {
            _depthData = depths ?? new List<double>();
            if (!string.IsNullOrEmpty(title))
                _yTitle = title;
            Invalidate();
        }

        /// <summary>
        /// 将数据坐标转换为像素坐标
        /// </summary>
        public Point DataToPixel(double x, double y)
        {
            Rectangle plotArea = new Rectangle(
                _marginLeft,
                _marginTop,
                this.Width - _marginLeft - _marginRight,
                this.Height - _marginTop - _marginBottom
            );

            int pixelX = (int)(plotArea.Left + (x - _xMin) / (_xMax - _xMin) * plotArea.Width);
            
            int pixelY = plotArea.Bottom;
            if (_depthData != null && _depthData.Count > 0)
            {
                double yMin = _depthData.Min();
                double yMax = _depthData.Max();
                double yRange = yMax - yMin;
                if (yRange > 0)
                {
                    pixelY = (int)(plotArea.Bottom - (y - yMin) / yRange * plotArea.Height);
                }
            }

            return new Point(pixelX, pixelY);
        }

        /// <summary>
        /// 将像素坐标转换为数据坐标
        /// </summary>
        public PointF PixelToData(Point pixel)
        {
            Rectangle plotArea = new Rectangle(
                _marginLeft,
                _marginTop,
                this.Width - _marginLeft - _marginRight,
                this.Height - _marginTop - _marginBottom
            );

            double x = _xMin + (pixel.X - plotArea.Left) * (_xMax - _xMin) / plotArea.Width;
            
            double y = 0;
            if (_depthData != null && _depthData.Count > 0)
            {
                double yMin = _depthData.Min();
                double yMax = _depthData.Max();
                double yRange = yMax - yMin;
                if (yRange > 0)
                {
                    y = yMin + (plotArea.Bottom - pixel.Y) * yRange / plotArea.Height;
                }
            }

            return new PointF((float)x, (float)y);
        }

        #endregion
    }
}
