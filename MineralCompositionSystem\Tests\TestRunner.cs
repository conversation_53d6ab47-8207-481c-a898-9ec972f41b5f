using System;
using MineralCompositionSystem.Tests;

namespace MineralCompositionSystem.Tests
{
    /// <summary>
    /// 测试运行器
    /// </summary>
    public class TestRunner
    {
        /// <summary>
        /// 主测试入口
        /// </summary>
        public static void Main(string[] args)
        {
            Console.WriteLine("矿物组分系统测试开始...");
            Console.WriteLine("测试时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            Console.WriteLine();

            try
            {
                // 运行BrittlenessCalculator测试
                BrittlenessCalculatorTests.RunAllTests();
                
                Console.WriteLine();
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
