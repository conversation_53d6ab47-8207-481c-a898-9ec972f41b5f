<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WebView2测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #333;
            color: #fff;
            text-align: center;
            padding: 50px;
        }
        h1 {
            color: #0078d7;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #444;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        button {
            background-color: #0078d7;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background-color: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebView2测试页面</h1>
        <p>如果您能看到这个页面，说明WebView2已经正确加载。</p>
        <p>当前时间: <span id="current-time"></span></p>
        <button id="test-button">测试WebView2通信</button>
        <p id="result"></p>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 测试WebView2通信
        document.getElementById('test-button').addEventListener('click', function() {
            // 发送消息到C#
            window.chrome.webview.postMessage({
                action: 'test',
                message: '这是一条测试消息'
            });
            
            document.getElementById('result').textContent = '消息已发送，等待响应...';
        });

        // 接收来自C#的消息
        window.chrome.webview.addEventListener('message', function(event) {
            if (event.data.action === 'testResponse') {
                document.getElementById('result').textContent = '收到响应: ' + event.data.message;
            }
        });
    </script>
</body>
</html>
