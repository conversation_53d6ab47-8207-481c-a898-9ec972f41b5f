# 示例数据说明

## 文件夹内容

本文件夹包含用于测试和演示的示例数据文件。

### 数据文件

1. **静态岩石力学参数数据_示例.xlsx**
   - 包含标准格式的岩石力学参数数据
   - 用于测试基础计算功能

2. **对比数据_矿物组分法_20250701.json**
   - 矿物组分法系统导出的对比数据
   - 用于测试对比图功能

3. **对比数据_静态岩石力学_20250701.csv**
   - CSV格式的对比数据
   - 用于测试CSV导入功能

### 图片文件

1. **对比图_示例_20250701.png**
   - 示例对比图图片
   - 用于测试图片关联功能

## 使用方法

### 测试基础功能
1. 启动系统
2. 点击"导入数据"
3. 选择 `静态岩石力学参数数据_示例.xlsx`
4. 点击"计算脆性指数"
5. 查看计算结果

### 测试批量导入
1. 点击"批量导入数据"
2. 添加多个示例文件
3. 预览数据
4. 确认导入并查看对比图

### 测试对比功能
1. 先在矿物组分法系统中保存对比数据
2. 在本系统中点击"查看对比图"
3. 查看多系统对比结果

## 数据格式说明

### Excel 数据格式
```
顶深/m | 底深/m | 密度/(g/cm³) | 纵波速度/(m/s) | 横波速度/(m/s)
2500.0 | 2501.0 | 2.45         | 4200          | 2400
2501.0 | 2502.0 | 2.48         | 4250          | 2450
```

### JSON 对比数据格式
```json
{
  "SystemName": "矿物组分法",
  "DataPoints": [
    {
      "TopDepth": 2500.0,
      "BottomDepth": 2501.0,
      "BrittleIndex": 65.8
    }
  ],
  "SaveTime": "2025-07-01T18:16:02",
  "DataCount": 100
}
```

### CSV 对比数据格式
```csv
序号,顶深(m),底深(m),脆性指数(%),系统名称
1,2500.0,2501.0,65.8,静态岩石力学参数法
2,2501.0,2502.0,67.2,静态岩石力学参数法
```

## 注意事项

1. 示例数据仅用于测试，不代表真实地质数据
2. 使用前请确保系统已正确安装和配置
3. 如需添加自己的测试数据，请遵循相同的格式规范
