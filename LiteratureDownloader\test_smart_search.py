#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能搜索助手
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_smart_search():
    """测试智能搜索助手"""
    
    # 测试数据
    test_citations = [
        {
            'reference_number': '1',
            'title': 'The spatial distribution and influencing factors of urban cultural and entertainment facilities in beijing',
            'authors': ['D. Han', 'X<PERSON> Liang', 'N. Hu<PERSON>', '<PERSON><PERSON>'],
            'year': '2021',
            'journal': 'Sustainability'
        },
        {
            'reference_number': '2',
            'title': 'Social media as a medium to promote local perception',
            'authors': ['X<PERSON> Liang', 'N. Hua', '<PERSON><PERSON>'],
            'year': '2022',
            'journal': '未识别'
        },
        {
            'reference_number': '3',
            'title': 'Machine learning approaches for text classification',
            'authors': '<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>',
            'year': '2023',
            'journal': 'Nature Machine Intelligence'
        }
    ]
    
    print("🔍 智能搜索助手测试")
    print("=" * 40)
    
    try:
        from modules.smart_search_helper import SmartSearchHelper
        
        print("✅ 智能搜索助手模块导入成功")
        
        # 创建助手实例
        helper = SmartSearchHelper()
        
        # 测试搜索建议生成
        print("\n📋 生成搜索建议...")
        suggestions = helper.generate_search_suggestions(test_citations)
        
        print("搜索建议预览:")
        print("-" * 40)
        print(suggestions[:500] + "..." if len(suggestions) > 500 else suggestions)
        
        # 询问是否测试完整功能
        if input("\n是否测试完整的智能搜索助手？(y/n): ").lower() == 'y':
            print("正在启动智能搜索助手...")
            
            if helper.open_wos_with_search_ready(test_citations):
                print("✅ 智能搜索助手启动成功")
                input("请查看搜索建议窗口，完成后按回车键...")
            else:
                print("❌ 智能搜索助手启动失败")
            
            helper.close()
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_smart_search()
