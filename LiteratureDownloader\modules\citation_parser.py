#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文献信息解析模块
功能：
1. 解析不同格式的文献引用
2. 提取标题、作者、期刊、年份等关键信息
3. 标准化文献信息格式
4. 验证和清理文献数据
"""

import re
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from loguru import logger

try:
    import pandas as pd
    from fuzzywuzzy import fuzz, process
except ImportError as e:
    logger.error(f"文献解析模块导入失败: {e}")
    raise

from config.settings import CITATION_PATTERNS


class CitationParser:
    """文献引用解析器"""
    
    def __init__(self):
        self.citation_patterns = CITATION_PATTERNS
        self.author_patterns = [
            r'([A-Z][a-zA-Z\'\-]+(?:\s+[A-Z][a-zA-Z\'\-]*)*)',  # 标准作者名
            r'([A-Z]\.\s*[A-Z]\.\s*[A-Z][a-zA-Z\'\-]+)',        # 缩写名
            r'([A-Z][a-zA-Z\'\-]+,\s*[A-Z]\.(?:\s*[A-Z]\.)*)',  # 姓,名缩写
        ]
        
        # 常见期刊名称缩写
        self.journal_abbreviations = {
            'J.': 'Journal',
            'Proc.': 'Proceedings',
            'Trans.': 'Transactions',
            'IEEE': 'IEEE',
            'ACM': 'ACM',
            'Int.': 'International',
            'Conf.': 'Conference',
            'Symp.': 'Symposium',
        }
    
    def parse_citation(self, citation_text: str, pattern_type: str = None) -> Dict[str, str]:
        """解析单个文献引用"""
        try:
            parsed_info = {
                'original_text': citation_text,
                'title': '',
                'authors': [],
                'journal': '',
                'year': '',
                'volume': '',
                'issue': '',
                'pages': '',
                'doi': '',
                'isbn': '',
                'publisher': '',
                'confidence': 0.0
            }
            
            # 如果指定了模式类型，使用特定解析方法
            if pattern_type:
                parsed_info = self.parse_by_pattern(citation_text, pattern_type)
            else:
                # 尝试所有模式
                parsed_info = self.parse_auto(citation_text)
            
            # 后处理和验证
            parsed_info = self.post_process_citation(parsed_info)
            
            return parsed_info
            
        except Exception as e:
            logger.error(f"解析文献引用失败: {e}")
            return {'original_text': citation_text, 'error': str(e)}
    
    def parse_by_pattern(self, citation_text: str, pattern_type: str) -> Dict[str, str]:
        """根据指定模式解析文献引用"""
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': pattern_type,
            'title': '',
            'authors': [],
            'journal': '',
            'year': '',
            'volume': '',
            'issue': '',
            'pages': '',
            'doi': '',
            'isbn': '',
            'publisher': '',
            'confidence': 0.5
        }
        
        if pattern_type == 'apa_style':
            parsed_info = self.parse_apa_style(citation_text)
        elif pattern_type == 'mla_style':
            parsed_info = self.parse_mla_style(citation_text)
        elif pattern_type == 'chicago_style':
            parsed_info = self.parse_chicago_style(citation_text)
        elif pattern_type == 'ieee_style':
            parsed_info = self.parse_ieee_style(citation_text)
        elif pattern_type == 'doi_pattern':
            parsed_info = self.parse_doi(citation_text)
        elif pattern_type == 'isbn_pattern':
            parsed_info = self.parse_isbn(citation_text)
        
        return parsed_info
    
    def parse_apa_style(self, citation_text: str) -> Dict[str, str]:
        """解析APA格式引用"""
        pattern = self.citation_patterns['apa_style']
        match = re.search(pattern, citation_text, re.IGNORECASE)
        
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': 'apa_style',
            'authors': [],
            'year': '',
            'title': '',
            'journal': '',
            'confidence': 0.7
        }
        
        if match:
            parsed_info['authors'] = self.parse_authors(match.group(1))
            parsed_info['year'] = match.group(2).strip()
            parsed_info['title'] = match.group(3).strip()
            parsed_info['journal'] = match.group(4).strip()
            parsed_info['confidence'] = 0.9
        
        return parsed_info
    
    def parse_mla_style(self, citation_text: str) -> Dict[str, str]:
        """解析MLA格式引用"""
        pattern = self.citation_patterns['mla_style']
        match = re.search(pattern, citation_text, re.IGNORECASE)
        
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': 'mla_style',
            'authors': [],
            'title': '',
            'journal': '',
            'year': '',
            'confidence': 0.7
        }
        
        if match:
            parsed_info['authors'] = self.parse_authors(match.group(1))
            parsed_info['title'] = match.group(2).strip()
            parsed_info['journal'] = match.group(3).strip()
            parsed_info['year'] = match.group(4).strip()
            parsed_info['confidence'] = 0.9
        
        return parsed_info
    
    def parse_ieee_style(self, citation_text: str) -> Dict[str, str]:
        """解析IEEE格式引用"""
        pattern = self.citation_patterns['ieee_style']
        match = re.search(pattern, citation_text, re.IGNORECASE)
        
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': 'ieee_style',
            'reference_number': '',
            'authors': [],
            'title': '',
            'journal': '',
            'year': '',
            'confidence': 0.7
        }
        
        if match:
            parsed_info['reference_number'] = match.group(1).strip()
            parsed_info['authors'] = self.parse_authors(match.group(2))
            parsed_info['title'] = match.group(3).strip()
            parsed_info['journal'] = match.group(4).strip()
            parsed_info['year'] = match.group(5).strip()
            parsed_info['confidence'] = 0.9
        
        return parsed_info
    
    def parse_doi(self, citation_text: str) -> Dict[str, str]:
        """解析DOI信息"""
        pattern = self.citation_patterns['doi_pattern']
        match = re.search(pattern, citation_text, re.IGNORECASE)
        
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': 'doi_pattern',
            'doi': '',
            'confidence': 0.8
        }
        
        if match:
            parsed_info['doi'] = match.group(1).strip()
            parsed_info['confidence'] = 0.95
        
        return parsed_info
    
    def parse_isbn(self, citation_text: str) -> Dict[str, str]:
        """解析ISBN信息"""
        pattern = self.citation_patterns['isbn_pattern']
        match = re.search(pattern, citation_text, re.IGNORECASE)
        
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': 'isbn_pattern',
            'isbn': '',
            'confidence': 0.8
        }
        
        if match:
            parsed_info['isbn'] = match.group(1).strip()
            parsed_info['confidence'] = 0.95
        
        return parsed_info
    
    def parse_auto(self, citation_text: str) -> Dict[str, str]:
        """自动识别并解析文献引用格式"""
        best_match = None
        best_confidence = 0.0
        
        # 尝试所有模式
        for pattern_name in self.citation_patterns.keys():
            try:
                parsed = self.parse_by_pattern(citation_text, pattern_name)
                if parsed.get('confidence', 0) > best_confidence:
                    best_confidence = parsed.get('confidence', 0)
                    best_match = parsed
            except Exception as e:
                logger.warning(f"模式 {pattern_name} 解析失败: {e}")
                continue
        
        if best_match:
            return best_match
        
        # 如果所有模式都失败，尝试通用解析
        return self.parse_generic(citation_text)
    
    def parse_generic(self, citation_text: str) -> Dict[str, str]:
        """通用文献引用解析"""
        parsed_info = {
            'original_text': citation_text,
            'pattern_type': 'generic',
            'authors': [],
            'title': '',
            'journal': '',
            'year': '',
            'confidence': 0.3
        }
        
        # 提取年份
        year_match = re.search(r'\b(19|20)\d{2}\b', citation_text)
        if year_match:
            parsed_info['year'] = year_match.group(0)
            parsed_info['confidence'] += 0.2
        
        # 提取DOI
        doi_match = re.search(r'doi:\s*(10\.\d+\/[^\s]+)', citation_text, re.IGNORECASE)
        if doi_match:
            parsed_info['doi'] = doi_match.group(1)
            parsed_info['confidence'] += 0.3
        
        # 尝试提取作者（简单方法）
        authors = self.extract_authors_generic(citation_text)
        if authors:
            parsed_info['authors'] = authors
            parsed_info['confidence'] += 0.2
        
        return parsed_info
    
    def parse_authors(self, author_text: str) -> List[str]:
        """解析作者信息"""
        if not author_text:
            return []
        
        authors = []
        
        # 分割多个作者
        author_separators = [' and ', ' & ', ', ', ';']
        author_list = [author_text]
        
        for separator in author_separators:
            new_list = []
            for author in author_list:
                new_list.extend(author.split(separator))
            author_list = new_list
        
        # 清理和标准化作者名
        for author in author_list:
            cleaned_author = self.clean_author_name(author.strip())
            if cleaned_author:
                authors.append(cleaned_author)
        
        return authors
    
    def clean_author_name(self, author_name: str) -> str:
        """清理和标准化作者姓名"""
        if not author_name:
            return ""
        
        # 移除多余的空格和标点
        author_name = re.sub(r'\s+', ' ', author_name.strip())
        author_name = re.sub(r'[^\w\s\.\-\']', '', author_name)
        
        # 检查是否是有效的作者名
        if len(author_name) < 2 or len(author_name) > 50:
            return ""
        
        # 检查是否包含至少一个字母
        if not re.search(r'[a-zA-Z]', author_name):
            return ""
        
        return author_name
    
    def extract_authors_generic(self, citation_text: str) -> List[str]:
        """通用作者提取方法"""
        authors = []
        
        for pattern in self.author_patterns:
            matches = re.findall(pattern, citation_text)
            for match in matches:
                cleaned_author = self.clean_author_name(match)
                if cleaned_author and cleaned_author not in authors:
                    authors.append(cleaned_author)
        
        return authors[:5]  # 限制最多5个作者
    
    def post_process_citation(self, parsed_info: Dict[str, str]) -> Dict[str, str]:
        """后处理文献信息"""
        # 清理标题
        if parsed_info.get('title'):
            parsed_info['title'] = self.clean_title(parsed_info['title'])
        
        # 清理期刊名
        if parsed_info.get('journal'):
            parsed_info['journal'] = self.clean_journal_name(parsed_info['journal'])
        
        # 验证年份
        if parsed_info.get('year'):
            parsed_info['year'] = self.validate_year(parsed_info['year'])
        
        # 清理DOI
        if parsed_info.get('doi'):
            parsed_info['doi'] = self.clean_doi(parsed_info['doi'])
        
        # 计算最终置信度
        parsed_info['confidence'] = self.calculate_final_confidence(parsed_info)
        
        return parsed_info
    
    def clean_title(self, title: str) -> str:
        """清理文献标题"""
        if not title:
            return ""
        
        # 移除引号
        title = re.sub(r'^["\']|["\']$', '', title.strip())
        
        # 移除多余的空格
        title = re.sub(r'\s+', ' ', title)
        
        # 限制长度
        if len(title) > 200:
            title = title[:200] + "..."
        
        return title.strip()
    
    def clean_journal_name(self, journal: str) -> str:
        """清理期刊名称"""
        if not journal:
            return ""
        
        # 展开缩写
        for abbrev, full_name in self.journal_abbreviations.items():
            journal = journal.replace(abbrev, full_name)
        
        # 移除多余的空格和标点
        journal = re.sub(r'\s+', ' ', journal.strip())
        journal = re.sub(r'[,\.]$', '', journal)
        
        return journal.strip()
    
    def validate_year(self, year: str) -> str:
        """验证年份"""
        if not year:
            return ""
        
        # 提取4位数字年份
        year_match = re.search(r'\b(19|20)\d{2}\b', year)
        if year_match:
            year_int = int(year_match.group(0))
            current_year = datetime.now().year
            
            # 检查年份合理性
            if 1900 <= year_int <= current_year + 1:
                return str(year_int)
        
        return ""
    
    def clean_doi(self, doi: str) -> str:
        """清理DOI"""
        if not doi:
            return ""
        
        # 移除doi:前缀
        doi = re.sub(r'^doi:\s*', '', doi, flags=re.IGNORECASE)
        
        # 验证DOI格式
        if re.match(r'^10\.\d+\/[^\s]+$', doi):
            return doi
        
        return ""
    
    def calculate_final_confidence(self, parsed_info: Dict[str, str]) -> float:
        """计算最终置信度"""
        base_confidence = parsed_info.get('confidence', 0.0)
        
        # 根据提取到的信息调整置信度
        if parsed_info.get('title'):
            base_confidence += 0.1
        if parsed_info.get('authors'):
            base_confidence += 0.1
        if parsed_info.get('year'):
            base_confidence += 0.1
        if parsed_info.get('journal'):
            base_confidence += 0.1
        if parsed_info.get('doi'):
            base_confidence += 0.2
        
        return min(base_confidence, 1.0)
    
    def parse_multiple_citations(self, citations: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """批量解析多个文献引用"""
        parsed_citations = []

        for citation in citations:
            try:
                # 如果引用已经有了完整的信息（来自PDFReader），就直接使用
                if (citation.get('authors') and citation.get('title') and
                    citation.get('pattern_type') == 'numbered_reference'):

                    # 直接使用已解析的信息，只做格式标准化
                    parsed = {
                        'original_text': citation.get('full_match', ''),
                        'pattern_type': citation.get('pattern_type', ''),
                        'title': citation.get('title', ''),
                        'authors': [citation.get('authors', '')] if citation.get('authors') else [],
                        'journal': citation.get('journal', ''),
                        'year': citation.get('year', ''),
                        'volume': '',
                        'issue': '',
                        'pages': '',
                        'doi': citation.get('doi', ''),
                        'isbn': '',
                        'publisher': '',
                        'confidence': 0.9,  # 高置信度，因为是从References部分解析的
                        'source_file': citation.get('source_file', ''),
                        'source_type': citation.get('source_type', ''),
                        'start_pos': citation.get('start_pos', 0),
                        'end_pos': citation.get('end_pos', 0),
                        'reference_number': citation.get('reference_number', '')
                    }

                else:
                    # 对于其他类型的引用，使用原来的解析方法
                    citation_text = citation.get('full_match', '')
                    pattern_type = citation.get('pattern_type', None)

                    parsed = self.parse_citation(citation_text, pattern_type)

                    # 合并原始信息
                    parsed.update({
                        'source_file': citation.get('source_file', ''),
                        'source_type': citation.get('source_type', ''),
                        'start_pos': citation.get('start_pos', 0),
                        'end_pos': citation.get('end_pos', 0),
                        'reference_number': citation.get('reference_number', '')
                    })

                parsed_citations.append(parsed)

            except Exception as e:
                logger.error(f"解析文献引用失败: {e}")
                continue

        logger.info(f"成功解析 {len(parsed_citations)} 个文献引用")
        return parsed_citations
