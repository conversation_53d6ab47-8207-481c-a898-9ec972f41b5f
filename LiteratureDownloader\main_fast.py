#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动版本 - 跳过可能有问题的模块
"""

import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 简单的logger
class SimpleLogger:
    def info(self, msg):
        print(f"[INFO] {msg}")
    
    def error(self, msg):
        print(f"[ERROR] {msg}")
    
    def warning(self, msg):
        print(f"[WARNING] {msg}")

# 替换loguru
sys.modules['loguru'] = type('MockModule', (), {'logger': SimpleLogger()})()

class FastMainWindow:
    """快速启动主窗口"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_basic_modules()
        self.create_widgets()
        
        # 存储识别结果
        self.citations = []
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("文献识别下载自动化程序 (快速版)")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 确保窗口显示在前台
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
    
    def setup_variables(self):
        """设置变量"""
        self.status_var = tk.StringVar(value="就绪")
        self.current_pdf_file = None
    
    def setup_basic_modules(self):
        """初始化基本模块"""
        try:
            print("初始化PDFReader...")
            from modules.pdf_reader import PDFReader
            self.pdf_reader = PDFReader()
            print("PDFReader初始化完成")
            
            print("初始化CitationParser...")
            from modules.citation_parser import CitationParser
            self.citation_parser = CitationParser()
            print("CitationParser初始化完成")
            
            print("基本模块初始化完成")
        except Exception as e:
            print(f"模块初始化失败: {e}")
            messagebox.showerror("错误", f"模块初始化失败: {e}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 文献识别选项卡
        self.recognition_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.recognition_frame, text="文献识别")
        
        # 创建文献识别界面
        self.create_recognition_widgets()
        
        # 状态栏
        self.create_status_bar()
        
        # 布局
        self.notebook.pack(fill=tk.BOTH, expand=True)
    
    def create_recognition_widgets(self):
        """创建文献识别界面"""
        # PDF识别区域
        pdf_group = ttk.LabelFrame(self.recognition_frame, text="PDF文件识别")
        
        ttk.Button(pdf_group, text="选择PDF文件", 
                  command=self.select_pdf_file).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(pdf_group, text="识别PDF引用", 
                  command=self.recognize_pdf_citations).pack(side=tk.LEFT, padx=5)
        
        pdf_group.pack(fill=tk.X, padx=10, pady=5)
        
        # 识别结果显示
        result_group = ttk.LabelFrame(self.recognition_frame, text="识别结果")
        
        columns = ('标题', '作者', '年份', '期刊', '置信度')
        self.citation_tree = ttk.Treeview(result_group, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.citation_tree.heading(col, text=col)
            self.citation_tree.column(col, width=150)
        
        citation_scrollbar = ttk.Scrollbar(result_group, orient=tk.VERTICAL, 
                                         command=self.citation_tree.yview)
        self.citation_tree.configure(yscrollcommand=citation_scrollbar.set)
        
        result_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.citation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        citation_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        ttk.Label(self.status_frame, text="状态:").pack(side=tk.LEFT, padx=5)
        ttk.Label(self.status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
    
    def select_pdf_file(self):
        """选择PDF文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.current_pdf_file = file_path
            self.status_var.set(f"已选择: {Path(file_path).name}")
            print(f"选择PDF文件: {file_path}")
    
    def recognize_pdf_citations(self):
        """识别PDF中的文献引用"""
        if not hasattr(self, 'current_pdf_file') or not self.current_pdf_file:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return
        
        try:
            self.status_var.set("正在识别PDF中的文献引用...")
            self.root.update()
            
            print(f"开始识别PDF: {self.current_pdf_file}")
            
            # 提取引用
            citations = self.pdf_reader.extract_citations_from_pdf(self.current_pdf_file)
            print(f"提取到 {len(citations)} 个原始引用")
            
            # 解析引用
            parsed_citations = self.citation_parser.parse_multiple_citations(citations)
            print(f"解析了 {len(parsed_citations)} 个引用")
            
            self.citations = parsed_citations
            self.update_citation_display()
            
            self.status_var.set(f"识别完成，找到 {len(parsed_citations)} 个文献引用")
            print(f"PDF识别完成，找到 {len(parsed_citations)} 个引用")
            
        except Exception as e:
            print(f"PDF识别失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"PDF识别失败: {e}")
            self.status_var.set("识别失败")
    
    def update_citation_display(self):
        """更新文献引用显示"""
        # 清空现有内容
        for item in self.citation_tree.get_children():
            self.citation_tree.delete(item)

        # 添加新内容
        for i, citation in enumerate(self.citations):
            # 获取引用编号
            ref_num = citation.get('reference_number', str(i+1))
            
            # 处理作者信息
            authors = citation.get('authors', '')
            if isinstance(authors, list):
                author_str = ', '.join(authors[:2])  # 只显示前两个作者
                if len(authors) > 2:
                    author_str += ' et al.'
            elif isinstance(authors, str):
                author_str = authors.strip() if authors else '未识别'
            else:
                author_str = '未识别'

            # 获取标题
            title = citation.get('title', '')
            if isinstance(title, str):
                title = title.strip()
            else:
                title = str(title) if title else ''
            
            if not title:
                # 如果没有标题，显示原始文本的前部分
                full_text = citation.get('full_match', '')
                if full_text:
                    title = f"[{ref_num}] {full_text[:60]}..."
                else:
                    title = f"[{ref_num}] 未识别标题"

            # 获取期刊信息
            journal = citation.get('journal', '')
            if isinstance(journal, str):
                journal = journal.strip()
            else:
                journal = str(journal) if journal else ''
            if not journal:
                journal = '未识别'

            # 获取年份
            year = citation.get('year', '')
            if isinstance(year, str):
                year = year.strip()
            else:
                year = str(year) if year else ''
            if not year:
                year = '未识别'

            # 计算置信度
            confidence = citation.get('confidence', 0)
            if confidence == 0:
                # 根据信息完整性估算置信度
                score = 0
                if citation.get('title'): score += 0.4
                if citation.get('authors'): score += 0.3
                if citation.get('year'): score += 0.2
                if citation.get('journal'): score += 0.1
                confidence = score

            # 插入到表格中
            self.citation_tree.insert('', tk.END, values=(
                title[:100] + '...' if len(title) > 100 else title,
                author_str[:50] + '...' if len(author_str) > 50 else author_str,
                year,
                journal[:40] + '...' if len(journal) > 40 else journal,
                f"{confidence:.2f}"
            ))

def main():
    """主函数"""
    try:
        print("启动快速版本...")
        
        # 创建主窗口
        root = tk.Tk()
        app = FastMainWindow(root)
        
        # 设置窗口关闭事件
        def on_closing():
            print("程序正在关闭")
            root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动GUI主循环
        print("启动GUI界面")
        root.mainloop()
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("错误", f"程序运行出错: {e}")
    finally:
        print("程序结束")

if __name__ == "__main__":
    main()
