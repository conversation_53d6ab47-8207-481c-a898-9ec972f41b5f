@echo off
title Static Rock Mechanics System V1.0

echo ========================================
echo  Static Rock Mechanics System V1.0
echo ========================================
echo.
echo Starting system...
echo.

REM Check .NET 8
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET 8 Runtime not found
    echo Please install .NET 8 Desktop Runtime
    echo Download: https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM Build and run
echo Building project...
dotnet build >nul 2>&1
if %errorlevel% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Starting Static Rock Mechanics System...
echo.
echo ========================================
echo   Login Info
echo   Username: admin
echo   Password: 123
echo ========================================
echo.
dotnet run

if %errorlevel% neq 0 (
    echo.
    echo System error occurred
    pause
)
