#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web of Science 搜索器
专门用于在Web of Science中搜索和下载文献
"""

import re
import time
import json
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, quote
from loguru import logger

try:
    import requests
    from bs4 import BeautifulSoup
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.keys import Keys
except ImportError as e:
    logger.error(f"Web of Science搜索器导入失败: {e}")
    raise

from config.settings import WOS_CONFIG, NETWORK_CONFIG


class WOSSearcher:
    """Web of Science 搜索器"""
    
    def __init__(self):
        self.config = WOS_CONFIG
        self.network_config = NETWORK_CONFIG
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.network_config['user_agent']
        })

        # Selenium WebDriver - 延迟初始化
        self.driver = None
        self._driver_initialized = False
    
    def setup_webdriver(self):
        """设置Selenium WebDriver - 使用用户现有的Chrome配置"""
        try:
            chrome_options = Options()

            # 使用用户的Chrome配置目录，保持登录状态
            import os
            user_data_dir = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data")
            if os.path.exists(user_data_dir):
                chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
                chrome_options.add_argument("--profile-directory=Default")
                logger.info("使用用户Chrome配置，保持校园网登录状态")

            # 基本配置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={self.network_config["user_agent"]}')

            # 不使用无头模式，让用户能看到搜索过程
            # chrome_options.add_argument('--headless')

            # 尝试使用webdriver-manager自动管理ChromeDriver
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                from selenium.webdriver.chrome.service import Service

                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("WebDriver初始化成功（使用webdriver-manager）")

            except ImportError:
                # 如果没有webdriver-manager，使用系统PATH中的ChromeDriver
                self.driver = webdriver.Chrome(options=chrome_options)
                logger.info("WebDriver初始化成功（使用系统ChromeDriver）")

        except Exception as e:
            logger.error(f"WebDriver初始化失败: {e}")
            logger.error("请确保已安装Chrome浏览器和ChromeDriver")
            logger.error("可以运行: pip install webdriver-manager")
            logger.warning("Web of Science搜索功能将不可用，但PDF识别功能正常")
            self.driver = None
    
    def _ensure_driver_initialized(self):
        """确保WebDriver已初始化"""
        if not self._driver_initialized:
            logger.info("首次使用，正在初始化WebDriver...")
            self.setup_webdriver()
            self._driver_initialized = True

    def search_by_title(self, title: str, max_results: int = 10) -> List[Dict[str, str]]:
        """根据标题搜索文献"""
        self._ensure_driver_initialized()

        if not self.driver:
            logger.error("WebDriver初始化失败")
            return []
        
        try:
            logger.info(f"在Web of Science中搜索: {title}")
            
            # 访问搜索页面
            self.driver.get(self.config['search_url'])
            time.sleep(3)
            
            # 查找搜索框并输入标题
            search_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[data-ta="search-input"], input[placeholder*="搜索"], input[type="text"]'))
            )
            
            search_box.clear()
            search_box.send_keys(title)
            time.sleep(1)
            
            # 点击搜索按钮或按回车
            try:
                search_button = self.driver.find_element(By.CSS_SELECTOR, 'button[data-ta="search-button"], button[type="submit"], .search-button')
                search_button.click()
            except:
                search_box.send_keys(Keys.RETURN)
            
            # 等待搜索结果加载
            time.sleep(5)
            
            # 解析搜索结果
            results = self.parse_search_results(max_results)
            
            logger.info(f"找到 {len(results)} 个搜索结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def search_by_author_and_keywords(self, authors: str, keywords: str, year: str = "", max_results: int = 10) -> List[Dict[str, str]]:
        """根据作者和关键词搜索文献"""
        self._ensure_driver_initialized()

        if not self.driver:
            logger.error("WebDriver初始化失败")
            return []
        
        try:
            # 构建搜索查询
            query_parts = []
            if authors:
                query_parts.append(f'AU=({authors})')
            if keywords:
                query_parts.append(f'TS=({keywords})')
            if year:
                query_parts.append(f'PY={year}')
            
            search_query = ' AND '.join(query_parts)
            logger.info(f"搜索查询: {search_query}")
            
            # 访问高级搜索页面
            self.driver.get(self.config['search_url'])
            time.sleep(3)
            
            # 尝试切换到高级搜索模式
            try:
                advanced_link = self.driver.find_element(By.LINK_TEXT, "高级搜索")
                advanced_link.click()
                time.sleep(2)
            except:
                pass
            
            # 输入搜索查询
            search_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[data-ta="search-input"], textarea, input[type="text"]'))
            )
            
            search_box.clear()
            search_box.send_keys(search_query)
            time.sleep(1)
            
            # 执行搜索
            try:
                search_button = self.driver.find_element(By.CSS_SELECTOR, 'button[data-ta="search-button"], button[type="submit"]')
                search_button.click()
            except:
                search_box.send_keys(Keys.RETURN)
            
            # 等待结果
            time.sleep(5)
            
            # 解析结果
            results = self.parse_search_results(max_results)
            
            logger.info(f"找到 {len(results)} 个搜索结果")
            return results
            
        except Exception as e:
            logger.error(f"高级搜索失败: {e}")
            return []
    
    def parse_search_results(self, max_results: int = 10) -> List[Dict[str, str]]:
        """解析搜索结果页面"""
        results = []
        
        try:
            # 等待结果加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '.search-results, .results, .record'))
            )
            
            # 查找结果项目
            result_selectors = [
                '.search-results-item',
                '.record',
                '.result-item',
                '[data-ta="result-item"]',
                '.summary-record'
            ]
            
            result_items = []
            for selector in result_selectors:
                result_items = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if result_items:
                    break
            
            if not result_items:
                logger.warning("未找到搜索结果项目")
                return []
            
            logger.info(f"找到 {len(result_items)} 个结果项目")
            
            for i, item in enumerate(result_items[:max_results]):
                try:
                    result = self.extract_result_info(item)
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    logger.warning(f"解析第 {i+1} 个结果失败: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {e}")
        
        return results
    
    def extract_result_info(self, item) -> Optional[Dict[str, str]]:
        """从结果项目中提取信息"""
        try:
            result = {
                'title': '',
                'authors': '',
                'journal': '',
                'year': '',
                'doi': '',
                'url': '',
                'abstract': '',
                'citation_count': '',
                'source': 'Web of Science'
            }
            
            # 提取标题
            title_selectors = ['.title-link', '.title', 'h3 a', '.record-title', '[data-ta="title"]']
            for selector in title_selectors:
                try:
                    title_elem = item.find_element(By.CSS_SELECTOR, selector)
                    result['title'] = title_elem.text.strip()
                    result['url'] = title_elem.get_attribute('href') or ''
                    break
                except:
                    continue
            
            # 提取作者
            author_selectors = ['.authors', '.author', '.record-authors', '[data-ta="authors"]']
            for selector in author_selectors:
                try:
                    authors_elem = item.find_element(By.CSS_SELECTOR, selector)
                    result['authors'] = authors_elem.text.strip()
                    break
                except:
                    continue
            
            # 提取期刊
            journal_selectors = ['.journal-title', '.journal', '.source-title', '[data-ta="journal"]']
            for selector in journal_selectors:
                try:
                    journal_elem = item.find_element(By.CSS_SELECTOR, selector)
                    result['journal'] = journal_elem.text.strip()
                    break
                except:
                    continue
            
            # 提取年份
            year_selectors = ['.year', '.pub-year', '.date', '[data-ta="year"]']
            for selector in year_selectors:
                try:
                    year_elem = item.find_element(By.CSS_SELECTOR, selector)
                    year_text = year_elem.text.strip()
                    year_match = re.search(r'\b(19|20)\d{2}\b', year_text)
                    if year_match:
                        result['year'] = year_match.group(0)
                    break
                except:
                    continue
            
            # 提取DOI
            doi_selectors = ['.doi-link', '.doi', '[data-ta="doi"]']
            for selector in doi_selectors:
                try:
                    doi_elem = item.find_element(By.CSS_SELECTOR, selector)
                    doi_text = doi_elem.text.strip()
                    doi_match = re.search(r'10\.\d+/[^\s]+', doi_text)
                    if doi_match:
                        result['doi'] = doi_match.group(0)
                    break
                except:
                    continue
            
            # 提取引用次数
            citation_selectors = ['.citation-count', '.times-cited', '[data-ta="citation-count"]']
            for selector in citation_selectors:
                try:
                    citation_elem = item.find_element(By.CSS_SELECTOR, selector)
                    result['citation_count'] = citation_elem.text.strip()
                    break
                except:
                    continue
            
            # 只返回有标题的结果
            if result['title']:
                return result
            else:
                return None
                
        except Exception as e:
            logger.warning(f"提取结果信息失败: {e}")
            return None
    
    def close(self):
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver已关闭")
            except Exception as e:
                logger.warning(f"关闭WebDriver失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.close()
