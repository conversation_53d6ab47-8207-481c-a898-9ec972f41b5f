# 增强版静态岩石力学参数法系统使用说明

## 系统概述

增强版静态岩石力学参数法系统是基于静态岩石力学参数（密度、纵波速度、横波速度）计算脆性指数的专业软件。相比传统版本，本系统增加了强大的数据兼容性和对比分析功能。

### 主要特性

- ✅ **多格式数据导入**：支持 Excel (.xlsx/.xls)、CSV (.csv)、JSON (.json) 格式
- ✅ **智能文件识别**：自动识别对比数据文件格式
- ✅ **批量数据处理**：支持批量导入和处理多个数据源
- ✅ **增强对比图功能**：支持多系统数据对比显示
- ✅ **图片关联显示**：自动关联并显示相关图片文件
- ✅ **数据标准化存储**：统一的数据交换格式
- ✅ **向后兼容**：完全兼容原有系统的数据格式

## 系统要求

### 硬件要求
- CPU：Intel Core i3 或同等性能处理器
- 内存：4GB RAM（推荐 8GB）
- 硬盘：至少 500MB 可用空间
- 显示器：1024x768 分辨率（推荐 1920x1080）

### 软件要求
- 操作系统：Windows 10 或更高版本
- .NET 6.0 Desktop Runtime
- Microsoft Visual C++ 2015-2022 Redistributable

## 安装与启动

### 方法一：直接运行
1. 双击 `启动系统.bat` 文件
2. 系统会自动检测运行环境并启动

### 方法二：命令行启动
```bash
cd EnhancedStaticRockMechanicsSystem
dotnet run
```

### 方法三：编译后运行
```bash
dotnet build
dotnet run --project EnhancedStaticRockMechanicsSystem.csproj
```

## 功能使用指南

### 1. 基础数据导入

#### 1.1 标准数据导入
1. 点击 **"导入数据"** 按钮
2. 选择包含以下列的 Excel 文件：
   - 顶深/m（必需）
   - 底深/m（可选，默认等于顶深）
   - 密度/(g/cm³)（必需）
   - 纵波速度/(m/s)（必需）
   - 横波速度/(m/s)（必需）

#### 1.2 数据格式要求
```
顶深/m | 底深/m | 密度/(g/cm³) | 纵波速度/(m/s) | 横波速度/(m/s)
2500.0 | 2501.0 | 2.45         | 4200          | 2400
2501.0 | 2502.0 | 2.48         | 4250          | 2450
```

### 2. 脆性指数计算

#### 2.1 计算原理
系统使用以下公式计算脆性指数：

1. **动态杨氏模量**：
   ```
   E = ρ × vs² × (3vp² - 4vs²) / (vp² - vs²)
   ```

2. **动态泊松比**：
   ```
   ν = (vp² - 2vs²) / (2(vp² - vs²))
   ```

3. **脆性指数**：
   ```
   BI = (E_norm + (1-ν)_norm) / 2 × 100
   ```

#### 2.2 计算步骤
1. 导入数据后，点击 **"计算脆性指数"** 按钮
2. 系统自动计算所有数据点的脆性指数
3. 结果显示在图表和数据表格中

### 3. 增强功能使用

#### 3.1 批量导入数据
1. 点击 **"批量导入数据"** 按钮
2. 在向导中选择多个数据文件
3. 系统支持以下文件格式：
   - Excel 文件 (.xlsx, .xls)
   - CSV 文件 (.csv)
   - JSON 文件 (.json)
4. 预览数据后确认导入
5. 自动显示增强对比图

#### 3.2 智能文件识别
系统能够自动识别以下文件名格式：
- `矿物组分法_对比数据_20250701_181602.xlsx`
- `StaticRockMechanics_ComparisonData_20250701.csv`
- `岩石力学_对比数据_20250701.json`

#### 3.3 查看对比图
1. 点击 **"查看对比图"** 按钮
2. 系统自动加载所有可用的对比数据
3. 支持以下数据源：
   - 标准化数据目录
   - 传统临时文件位置
   - 用户指定文件

#### 3.4 存为对比图
1. 完成脆性指数计算后
2. 点击 **"存为对比图"** 按钮
3. 数据保存到标准位置，供其他系统使用
4. 同时保持与原有系统的兼容性

### 4. 对比图功能

#### 4.1 多系统对比
- 支持同时显示多个系统的计算结果
- 不同系统使用不同颜色区分
- 支持图例显示和隐藏

#### 4.2 图片关联显示
- 自动查找与数据文件相关的图片
- 支持 PNG、JPG、BMP、GIF、TIFF 格式
- 侧边栏显示关联图片

#### 4.3 交互功能
- 图表缩放和平移
- 数据点悬停显示详细信息
- 图片切换和查看
- 图表保存为图片

## 数据格式说明

### 输入数据格式

#### Excel/CSV 格式
```csv
顶深/m,底深/m,密度/(g/cm³),纵波速度/(m/s),横波速度/(m/s)
2500.0,2501.0,2.45,4200,2400
2501.0,2502.0,2.48,4250,2450
```

#### JSON 格式
```json
{
  "FormatVersion": "3.0",
  "ExportInfo": {
    "SystemName": "静态岩石力学参数法",
    "ExportTime": "2025-07-01T18:16:02",
    "DataCount": 100
  },
  "DataPoints": [
    {
      "TopDepth": 2500.0,
      "BottomDepth": 2501.0,
      "BrittleIndex": 65.8
    }
  ]
}
```

### 输出数据格式

#### 计算结果
- 顶深 (m)
- 底深 (m)
- 脆性指数 (%)
- 地质点ID (GeoID)

#### 对比数据
- 系统名称
- 数据来源
- 导入时间
- 关联图片列表

## 故障排除

### 常见问题

#### 1. 系统无法启动
**问题**：双击启动文件无反应
**解决方案**：
- 检查是否安装了 .NET 6.0 Desktop Runtime
- 以管理员身份运行启动文件
- 检查防病毒软件是否阻止了程序运行

#### 2. 数据导入失败
**问题**：Excel 文件无法导入
**解决方案**：
- 确保文件未被其他程序占用
- 检查文件格式是否正确
- 验证必需列是否存在

#### 3. 计算结果异常
**问题**：脆性指数计算结果不合理
**解决方案**：
- 检查输入数据的单位是否正确
- 验证纵波速度是否大于横波速度
- 确保密度值在合理范围内 (1.5-3.0 g/cm³)

#### 4. 对比图无数据
**问题**：查看对比图时显示无数据
**解决方案**：
- 先在各系统中保存对比图数据
- 使用批量导入功能手动导入数据
- 检查数据文件路径是否正确

### 技术支持

如遇到其他问题，请联系技术支持：
- 邮箱：<EMAIL>
- 电话：400-123-4567
- 在线文档：https://docs.britsystem.com

## 更新日志

### 版本 2.0.0 (2025-07-01)
- ✨ 新增批量数据导入功能
- ✨ 新增智能文件识别机制
- ✨ 新增图片关联显示功能
- ✨ 增强对比图显示效果
- ✨ 改进数据兼容性
- 🐛 修复数据导入时的编码问题
- 🐛 修复图表显示异常
- ⚡ 优化系统性能和稳定性

### 版本 1.0.0 (2024-12-01)
- 🎉 初始版本发布
- ✨ 基础脆性指数计算功能
- ✨ Excel 数据导入导出
- ✨ 图表显示功能

---

**版权所有 © 2025 BritSystem. 保留所有权利。**
