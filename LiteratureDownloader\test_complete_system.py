#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试
测试从PDF识别到Web of Science搜索的完整流程
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_complete_workflow():
    """测试完整工作流程"""
    print("完整系统测试")
    print("=" * 60)
    
    # 步骤1：测试PDF识别
    print("步骤1：测试PDF文献识别...")
    try:
        from modules.pdf_reader import PDFReader
        from modules.citation_parser import CitationParser
        
        pdf_reader = PDFReader()
        citation_parser = CitationParser()
        
        pdf_file = "paper.pdf"
        if not Path(pdf_file).exists():
            print(f"✗ PDF文件不存在: {pdf_file}")
            return
        
        # 提取引用
        text = pdf_reader.extract_text(pdf_file)
        citations = pdf_reader.find_citations(text)
        parsed_citations = citation_parser.parse_multiple_citations(citations)
        
        print(f"✓ PDF识别成功，找到 {len(parsed_citations)} 个引用")
        
        # 显示前3个引用
        print("\n前3个识别的引用:")
        for i, citation in enumerate(parsed_citations[:3], 1):
            authors = citation.get('authors', [])
            if isinstance(authors, list) and authors:
                author_str = ', '.join(authors[:2])
            else:
                author_str = str(authors) if authors else '未知'
            
            title = citation.get('title', '未知')
            year = citation.get('year', '未知')
            
            print(f"  {i}. 作者: {author_str}")
            print(f"     标题: {title[:60]}...")
            print(f"     年份: {year}")
        
    except Exception as e:
        print(f"✗ PDF识别失败: {e}")
        return
    
    # 步骤2：测试Web of Science搜索
    print(f"\n步骤2：测试Web of Science搜索...")
    try:
        from modules.wos_searcher import WOSSearcher
        
        wos_searcher = WOSSearcher()
        print("✓ WOS搜索器初始化成功")
        
        # 选择一个有标题的引用进行搜索
        test_citation = None
        for citation in parsed_citations:
            title = citation.get('title', '')
            if title and title != '未知' and len(title) > 20:
                test_citation = citation
                break
        
        if test_citation:
            title = test_citation.get('title', '')
            print(f"搜索标题: {title[:50]}...")
            
            search_results = wos_searcher.search_by_title(title, max_results=3)
            
            if search_results:
                print(f"✓ 搜索成功，找到 {len(search_results)} 个结果")
                
                print("\n搜索结果:")
                for i, result in enumerate(search_results, 1):
                    print(f"  结果 {i}:")
                    print(f"    标题: {result.get('title', '未知')[:60]}...")
                    print(f"    作者: {result.get('authors', '未知')[:40]}...")
                    print(f"    期刊: {result.get('journal', '未知')}")
                    print(f"    年份: {result.get('year', '未知')}")
                    if result.get('citation_count'):
                        print(f"    引用次数: {result.get('citation_count')}")
            else:
                print("✗ 搜索未找到结果")
        else:
            print("✗ 没有找到合适的标题进行搜索")
        
        wos_searcher.close()
        
    except Exception as e:
        print(f"✗ Web of Science搜索失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 步骤3：测试GUI启动
    print(f"\n步骤3：测试GUI启动...")
    try:
        import tkinter as tk
        from gui.main_window import MainWindow
        
        # 创建测试窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = MainWindow(root)
        print("✓ GUI初始化成功")
        
        # 检查关键组件
        if hasattr(app, 'wos_searcher'):
            print("✓ WOS搜索器已集成到GUI")
        else:
            print("✗ WOS搜索器未集成到GUI")
        
        root.destroy()
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 60)
    print("完整系统测试完成")

def test_wos_only():
    """仅测试Web of Science搜索"""
    print("Web of Science 搜索测试")
    print("=" * 40)
    
    try:
        from modules.wos_searcher import WOSSearcher
        
        wos_searcher = WOSSearcher()
        
        # 测试简单搜索
        test_queries = [
            "machine learning",
            "artificial intelligence",
            "deep learning"
        ]
        
        for query in test_queries:
            print(f"\n搜索: '{query}'")
            try:
                results = wos_searcher.search_by_title(query, max_results=2)
                print(f"找到 {len(results)} 个结果")
                
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result.get('title', '未知')[:50]}...")
                    
            except Exception as e:
                print(f"搜索 '{query}' 失败: {e}")
        
        wos_searcher.close()
        
    except Exception as e:
        print(f"WOS搜索测试失败: {e}")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整系统测试 (PDF识别 + WOS搜索 + GUI)")
    print("2. 仅测试Web of Science搜索")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            test_complete_workflow()
        elif choice == "2":
            test_wos_only()
        else:
            print("无效选择，运行WOS搜索测试")
            test_wos_only()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")  # 防止窗口闪退
