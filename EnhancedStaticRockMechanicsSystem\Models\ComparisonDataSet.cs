using System;
using System.Collections.Generic;

namespace EnhancedStaticRockMechanicsSystem.Models
{
    /// <summary>
    /// 对比数据集模型
    /// </summary>
    public class ComparisonDataSet
    {
        /// <summary>
        /// 系统名称
        /// </summary>
        public string SystemName { get; set; }

        /// <summary>
        /// 数据源路径
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// 导入时间
        /// </summary>
        public DateTime ImportTime { get; set; }

        /// <summary>
        /// 数据点列表
        /// </summary>
        public List<BrittlenessDataPoint> DataPoints { get; set; } = new List<BrittlenessDataPoint>();

        /// <summary>
        /// 关联图片列表
        /// </summary>
        public List<string> AssociatedImages { get; set; } = new List<string>();

        /// <summary>
        /// 元数据字典
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 数据点数量
        /// </summary>
        public int DataPointCount => DataPoints.Count;

        /// <summary>
        /// 深度范围
        /// </summary>
        public (double Min, double Max) DepthRange
        {
            get
            {
                if (DataPoints.Count == 0) return (0, 0);
                
                double min = double.MaxValue;
                double max = double.MinValue;
                
                foreach (var point in DataPoints)
                {
                    if (point.TopDepth < min) min = point.TopDepth;
                    if (point.BottomDepth > max) max = point.BottomDepth;
                }
                
                return (min, max);
            }
        }

        /// <summary>
        /// 脆性指数范围
        /// </summary>
        public (double Min, double Max) BrittlenessRange
        {
            get
            {
                if (DataPoints.Count == 0) return (0, 0);
                
                double min = double.MaxValue;
                double max = double.MinValue;
                
                foreach (var point in DataPoints)
                {
                    if (point.BrittleIndex < min) min = point.BrittleIndex;
                    if (point.BrittleIndex > max) max = point.BrittleIndex;
                }
                
                return (min, max);
            }
        }

        /// <summary>
        /// 验证数据集有效性
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(SystemName) && 
                   DataPoints.Count > 0 && 
                   DataPoints.TrueForAll(p => p.IsValid());
        }
    }
}
