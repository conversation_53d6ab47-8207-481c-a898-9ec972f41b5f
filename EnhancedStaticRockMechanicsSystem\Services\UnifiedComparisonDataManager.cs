using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using EnhancedStaticRockMechanicsSystem.Models;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 统一的对比数据管理器
    /// </summary>
    public class UnifiedComparisonDataManager
    {
        private readonly ExtendedCsvImportService csvImportService;
        private readonly ExtendedExcelImportService excelImportService;
        private readonly ImageAssociationService imageService;
        
        public UnifiedComparisonDataManager()
        {
            csvImportService = new ExtendedCsvImportService();
            excelImportService = new ExtendedExcelImportService();
            imageService = new ImageAssociationService();
        }

        /// <summary>
        /// 从多种数据源加载对比数据
        /// </summary>
        public async Task<List<ComparisonDataSet>> LoadComparisonDataFromMultipleSources(string[] filePaths)
        {
            var dataSets = new List<ComparisonDataSet>();
            var loadTasks = new List<Task<ComparisonDataSet>>();

            foreach (var filePath in filePaths)
            {
                if (!File.Exists(filePath))
                {
                    LoggingService.Instance.Warning($"文件不存在: {filePath}");
                    continue;
                }

                var fileInfo = ComparisonFileParser.ParseFileName(filePath);
                if (!fileInfo.IsValidComparisonFile)
                {
                    LoggingService.Instance.Info($"跳过非对比数据文件: {filePath}");
                    continue;
                }

                // 根据文件类型选择导入服务
                Task<ComparisonDataSet> loadTask = fileInfo.FileExtension.ToLower() switch
                {
                    ".csv" => csvImportService.ImportComparisonDataFromCsv(filePath),
                    ".xlsx" or ".xls" => excelImportService.ImportComparisonDataFromExcel(filePath),
                    ".json" => LoadFromJsonFile(filePath),
                    _ => Task.FromResult<ComparisonDataSet>(null)
                };

                if (loadTask != null)
                {
                    loadTasks.Add(loadTask);
                }
            }

            // 并行加载所有文件
            var results = await Task.WhenAll(loadTasks);
            
            foreach (var result in results)
            {
                if (result != null && result.DataPoints.Count > 0)
                {
                    // 查找关联图片
                    result.AssociatedImages = imageService.FindAssociatedImages(result.DataSource);
                    dataSets.Add(result);
                }
            }

            LoggingService.Instance.Info($"成功加载 {dataSets.Count} 个数据集");
            return dataSets;
        }

        /// <summary>
        /// 从JSON文件加载数据（兼容原有格式）
        /// </summary>
        private async Task<ComparisonDataSet> LoadFromJsonFile(string filePath)
        {
            try
            {
                string jsonContent = await File.ReadAllTextAsync(filePath);
                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                var dataSet = new ComparisonDataSet
                {
                    SystemName = data.SystemName?.ToString() ?? "未知系统",
                    DataSource = filePath,
                    ImportTime = DateTime.Now,
                    DataPoints = new List<BrittlenessDataPoint>()
                };

                if (data.DataPoints != null)
                {
                    foreach (var point in data.DataPoints)
                    {
                        var dataPoint = new BrittlenessDataPoint
                        {
                            TopDepth = Convert.ToDouble(point.TopDepth ?? point.Depth ?? 0),
                            BottomDepth = Convert.ToDouble(point.BottomDepth ?? point.Depth ?? 0),
                            BrittleIndex = Convert.ToDouble(point.BrittleIndex ?? point.BrittlenessIndex ?? 0)
                        };

                        if (dataPoint.IsValid())
                        {
                            dataPoint.GenerateGeoID();
                            dataSet.DataPoints.Add(dataPoint);
                        }
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"JSON文件加载失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 智能文件选择对话框
        /// </summary>
        public string[] ShowSmartFileDialog()
        {
            using (var ofd = new OpenFileDialog())
            {
                ofd.Title = "选择对比数据文件";
                ofd.Filter = "所有支持的格式|*.xlsx;*.xls;*.csv;*.json|" +
                            "Excel文件|*.xlsx;*.xls|" +
                            "CSV文件|*.csv|" +
                            "JSON文件|*.json|" +
                            "所有文件|*.*";
                ofd.Multiselect = true;
                ofd.CheckFileExists = true;

                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    return ofd.FileNames;
                }
            }

            return new string[0];
        }

        /// <summary>
        /// 批量导入向导
        /// </summary>
        public async Task<List<ComparisonDataSet>> ShowBatchImportWizard()
        {
            var wizard = new Forms.BatchImportWizard();
            if (wizard.ShowDialog() == DialogResult.OK)
            {
                return await LoadComparisonDataFromMultipleSources(wizard.SelectedFiles);
            }

            return new List<ComparisonDataSet>();
        }

        /// <summary>
        /// 从标准位置加载对比数据
        /// </summary>
        public async Task<List<ComparisonDataSet>> LoadFromStandardLocations()
        {
            var dataSets = new List<ComparisonDataSet>();
            
            try
            {
                // 1. 检查标准化数据目录
                var standardDataPath = AppConfig.SharedDataPath;
                if (Directory.Exists(standardDataPath))
                {
                    var dataFiles = Directory.GetFiles(standardDataPath, "*.*")
                        .Where(f => IsValidDataFile(f))
                        .OrderByDescending(f => File.GetLastWriteTime(f))
                        .Take(10) // 只取最新的10个文件
                        .ToArray();
                        
                    var standardDataSets = await LoadComparisonDataFromMultipleSources(dataFiles);
                    dataSets.AddRange(standardDataSets);
                }
                
                // 2. 检查传统临时文件位置（向后兼容）
                var legacyFiles = new[]
                {
                    Path.Combine(AppConfig.TempDataPath, "BritSystem_MineralogicalData.json"),
                    Path.Combine(AppConfig.TempDataPath, "BritSystem_StaticRockMechanicsData.json")
                };
                
                var existingLegacyFiles = legacyFiles.Where(File.Exists).ToArray();
                if (existingLegacyFiles.Length > 0)
                {
                    var legacyDataSets = await LoadComparisonDataFromMultipleSources(existingLegacyFiles);
                    
                    // 避免重复添加相同系统的数据
                    foreach (var legacyDataSet in legacyDataSets)
                    {
                        if (!dataSets.Any(ds => ds.SystemName == legacyDataSet.SystemName))
                        {
                            dataSets.Add(legacyDataSet);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"从标准位置加载数据失败: {ex.Message}");
            }
            
            return dataSets;
        }

        private bool IsValidDataFile(string filePath)
        {
            var validExtensions = new[] { ".xlsx", ".xls", ".csv", ".json" };
            var extension = Path.GetExtension(filePath).ToLower();
            return validExtensions.Contains(extension);
        }

        /// <summary>
        /// 保存数据集到标准位置
        /// </summary>
        public async Task<string> SaveDataSetToStandardLocation(ComparisonDataSet dataSet, string format = "json")
        {
            try
            {
                var standardPath = AppConfig.SharedDataPath;
                Directory.CreateDirectory(standardPath);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"{dataSet.SystemName}_对比数据_{timestamp}.{format}";
                var filePath = Path.Combine(standardPath, fileName);

                switch (format.ToLower())
                {
                    case "json":
                        await SaveAsJson(dataSet, filePath);
                        break;
                    case "csv":
                        await SaveAsCsv(dataSet, filePath);
                        break;
                    default:
                        throw new ArgumentException($"不支持的格式: {format}");
                }

                LoggingService.Instance.Info($"数据集已保存到: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"保存数据集失败: {ex.Message}");
                throw;
            }
        }

        private async Task SaveAsJson(ComparisonDataSet dataSet, string filePath)
        {
            var exportData = new
            {
                FormatVersion = "3.0",
                ExportInfo = new
                {
                    SystemName = dataSet.SystemName,
                    ExportTime = DateTime.Now,
                    DataCount = dataSet.DataPoints.Count,
                    OriginalSource = dataSet.DataSource
                },
                DataPoints = dataSet.DataPoints.Select(dp => new
                {
                    TopDepth = dp.TopDepth,
                    BottomDepth = dp.BottomDepth,
                    BrittleIndex = dp.BrittleIndex,
                    GeoID = dp.GeoID
                }),
                AssociatedImages = dataSet.AssociatedImages,
                Metadata = dataSet.Metadata
            };

            var jsonData = JsonConvert.SerializeObject(exportData, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, jsonData);
        }

        private async Task SaveAsCsv(ComparisonDataSet dataSet, string filePath)
        {
            var lines = new List<string>
            {
                "# 对比数据导出文件",
                $"# 系统名称: {dataSet.SystemName}",
                $"# 导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                $"# 数据点数量: {dataSet.DataPoints.Count}",
                "",
                "序号,顶深(m),底深(m),脆性指数(%),GeoID"
            };

            for (int i = 0; i < dataSet.DataPoints.Count; i++)
            {
                var point = dataSet.DataPoints[i];
                lines.Add($"{i + 1},{point.TopDepth},{point.BottomDepth},{point.BrittleIndex},{point.GeoID}");
            }

            await File.WriteAllLinesAsync(filePath, lines);
        }
    }
}
