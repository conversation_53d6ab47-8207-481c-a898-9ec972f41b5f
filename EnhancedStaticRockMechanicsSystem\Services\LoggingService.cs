using System;
using System.IO;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 日志服务
    /// </summary>
    public class LoggingService
    {
        private static readonly Lazy<LoggingService> _instance = new Lazy<LoggingService>(() => new LoggingService());
        private readonly string _logFilePath;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static LoggingService Instance => _instance.Value;

        private LoggingService()
        {
            try
            {
                string logFolder = AppConfig.LogFolderPath;
                Directory.CreateDirectory(logFolder);
                _logFilePath = Path.Combine(logFolder, $"EnhancedStaticRockMechanics_{DateTime.Now:yyyyMMdd}.log");
            }
            catch
            {
                // 如果无法创建日志文件夹，使用临时文件夹
                _logFilePath = Path.Combine(Path.GetTempPath(), $"EnhancedStaticRockMechanics_{DateTime.Now:yyyyMMdd}.log");
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public void Warning(string message)
        {
            WriteLog("WARN", message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void Debug(string message)
        {
            WriteLog("DEBUG", message);
        }

        private void WriteLog(string level, string message)
        {
            try
            {
                lock (_lockObject)
                {
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] {message}";
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);
                    
                    // 同时输出到控制台（调试时有用）
                    Console.WriteLine(logEntry);
                }
            }
            catch
            {
                // 忽略日志写入错误，避免影响主程序
            }
        }

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        /// <summary>
        /// 清理旧日志文件
        /// </summary>
        public void CleanOldLogs(int keepDays = 7)
        {
            try
            {
                string logFolder = Path.GetDirectoryName(_logFilePath);
                if (Directory.Exists(logFolder))
                {
                    var files = Directory.GetFiles(logFolder, "EnhancedStaticRockMechanics_*.log");
                    var cutoffDate = DateTime.Now.AddDays(-keepDays);
                    
                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTime < cutoffDate)
                        {
                            File.Delete(file);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Warning($"清理旧日志文件失败: {ex.Message}");
            }
        }
    }
}
