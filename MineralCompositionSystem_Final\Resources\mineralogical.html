<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>脆性指数系统 - 矿物组分法</title>
    <link rel="stylesheet" href="mineralogical.css">
    <style>
    /* 高亮行样式 */
    .highlighted-row {
        background-color: rgba(69, 243, 255, 0.3) !important;
        font-weight: bold;
    }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>脆性指数系统</h1>
            <div class="user-info">
                <span class="username" id="current-username">admin</span>
                <a href="#" class="logout-btn" id="logout-btn">退出登录</a>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#" id="dashboard-btn">控制面板</a></li>
                <li><a href="#" class="active">矿物组分法</a></li>
                <li><a href="#">数据管理</a></li>
                <li><a href="#">指数计算</a></li>
                <li><a href="#">报告生成</a></li>
                <li><a href="#">系统设置</a></li>
            </ul>
        </nav>

        <main>
            <div class="page-header">
                <h2>矿物组分法</h2>
                <p>通过矿物组分计算脆性指数</p>
            </div>

            <div class="content-container">
                <div class="left-panel">
                    <div class="tool-panel">
                        <h3>数据操作</h3>
                        <div class="button-group">
                            <button id="btn-import" class="primary-btn">导入数据</button>
                            <button id="btn-export" class="primary-btn">导出数据</button>
                            <button id="btn-add-row" class="primary-btn">添加行</button>
                            <button id="btn-delete-row" class="primary-btn">删除行</button>
                        </div>
                    </div>

                    <div class="data-panel">
                        <h3>矿物数据</h3>
                        <div class="table-container">
                            <table id="mineral-table">
                                <thead>
                                    <tr>
                                        <th>顶深(m)</th>
                                        <th>底深(m)</th>
                                        <th>石英(%)</th>
                                        <th>长石(%)</th>
                                        <th>碳酸盐(%)</th>
                                        <th>黏土(%)</th>
                                        <th>脆性指数(%)</th>
                                    </tr>
                                </thead>
                                <tbody id="mineral-data">
                                    <!-- 数据将通过JavaScript动态添加 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="calculator-panel">
                        <h3>脆性指数计算器</h3>
                        <div class="calculator-form">
                            <div class="form-group">
                                <label for="input-quartz">石英含量(%):</label>
                                <input type="number" id="input-quartz" min="0" max="100" step="0.1" value="30">
                            </div>
                            <div class="form-group">
                                <label for="input-feldspar">长石含量(%):</label>
                                <input type="number" id="input-feldspar" min="0" max="100" step="0.1" value="20">
                            </div>
                            <div class="form-group">
                                <label for="input-carbonate">碳酸盐含量(%):</label>
                                <input type="number" id="input-carbonate" min="0" max="100" step="0.1" value="40">
                            </div>
                            <div class="form-group">
                                <label for="input-clay">黏土含量(%):</label>
                                <input type="number" id="input-clay" min="0" max="100" step="0.1" value="10">
                            </div>
                            <button id="btn-calculate" class="primary-btn">计算脆性指数</button>
                            <div class="result-container">
                                <span>脆性指数: </span>
                                <span id="brittleness-result">--</span>
                                <span>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="right-panel">
                    <div class="chart-controls">
                        <h3>曲线图</h3>
                        <div class="button-group">
                            <button id="btn-generate-curve" class="primary-btn">生成曲线</button>
                            <button id="btn-reset-view" class="primary-btn">重置视图</button>
                            <button id="btn-save-image" class="primary-btn">保存图像</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="brittleness-chart"></canvas>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2023 脆性指数系统 - 版权所有</p>
        </footer>
    </div>

    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p id="loading-text">正在加载...</p>
    </div>

    <div id="file-dialog" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h3>选择文件</h3>
            <div class="file-input-container">
                <input type="file" id="file-input" accept=".xlsx,.xls,.csv">
                <button id="btn-file-select" class="primary-btn">选择文件</button>
            </div>
            <div class="file-info">
                <p>未选择文件</p>
            </div>
            <div class="modal-footer">
                <button id="btn-file-cancel" class="secondary-btn">取消</button>
                <button id="btn-file-import" class="primary-btn">导入</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 检查是否在WebView2环境中运行
        const isRunningInWebView = () => {
            return window.chrome && window.chrome.webview;
        };

        // 显示加载动画
        function showLoading(message = '正在加载...') {
            const loading = document.getElementById('loading');
            const loadingText = document.getElementById('loading-text');
            loadingText.textContent = message;
            loading.style.display = 'flex';
        }

        // 隐藏加载动画
        function hideLoading() {
            const loading = document.getElementById('loading');
            loading.style.display = 'none';
        }

        // 显示文件对话框
        function showFileDialog() {
            const fileDialog = document.getElementById('file-dialog');
            fileDialog.style.display = 'block';
        }

        // 隐藏文件对话框
        function hideFileDialog() {
            const fileDialog = document.getElementById('file-dialog');
            fileDialog.style.display = 'none';
        }

        // 初始化图表
        let brittlenessChart;
        function initChart() {
            const ctx = document.getElementById('brittleness-chart').getContext('2d');
            brittlenessChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '脆性指数',
                        data: [],
                        backgroundColor: 'rgba(69, 243, 255, 0.7)',
                        borderColor: 'rgba(69, 243, 255, 1)',
                        borderWidth: 1,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '脆性指数 (%)'
                            },
                            min: 0,
                            max: 100
                        },
                        y: {
                            type: 'linear',
                            title: {
                                display: true,
                                text: '深度 (m)'
                            },
                            reverse: true // 深度增加时向下显示
                        }
                    }
                }
            });
        }

        // 更新图表数据
        function updateChart(data) {
            if (!brittlenessChart) {
                initChart();
            }

            const chartData = data.map(item => ({
                x: item.brittleness,
                y: (item.topDepth + item.bottomDepth) / 2 // 使用中点深度
            }));

            brittlenessChart.data.datasets[0].data = chartData;
            brittlenessChart.update();
            
            // 添加图表点击事件处理
            if (!brittlenessChart._clickHandler) {
                brittlenessChart._clickHandler = true;
                brittlenessChart.canvas.addEventListener('click', function(evt) {
                    const points = brittlenessChart.getElementsAtEventForMode(evt, 'nearest', { intersect: true }, false);
                    if (points.length) {
                        const firstPoint = points[0];
                        const dataIndex = firstPoint.index;
                        const datasetIndex = firstPoint.datasetIndex;
                        const dataValue = brittlenessChart.data.datasets[datasetIndex].data[dataIndex];
                        
                        // 找到对应的表格行并高亮显示
                        highlightTableRow(dataValue.x);
                    }
                });
            }
        }

        // 填充表格数据
        function populateTable(data) {
            const tableBody = document.getElementById('mineral-data');
            tableBody.innerHTML = '';

            data.forEach((item, index) => {
                const row = document.createElement('tr');
                row.dataset.brittleness = item.brittleness.toFixed(2); // 添加数据属性用于后续查找
                row.dataset.index = index; // 添加索引属性
                row.innerHTML = `
                    <td>${item.topDepth.toFixed(2)}</td>
                    <td>${item.bottomDepth.toFixed(2)}</td>
                    <td>${item.quartz.toFixed(2)}</td>
                    <td>${item.feldspar.toFixed(2)}</td>
                    <td>${item.carbonate.toFixed(2)}</td>
                    <td>${item.clay.toFixed(2)}</td>
                    <td>${item.brittleness.toFixed(2)}</td>
                `;
                
                // 添加行点击事件
                row.addEventListener('click', function() {
                    highlightTableRow(item.brittleness);
                });
                
                tableBody.appendChild(row);
            });
        }

        // 高亮表格行的函数
        function highlightTableRow(brittlenessValue) {
            // 获取所有表格行
            const rows = document.querySelectorAll('#mineral-data tr');
            
            // 先移除所有行的高亮样式
            rows.forEach(row => {
                row.classList.remove('highlighted-row');
            });
            
            // 查找匹配的行并高亮显示
            let found = false;
            rows.forEach(row => {
                const rowBrittleness = parseFloat(row.dataset.brittleness);
                // 使用近似匹配，因为浮点数可能有微小差异
                if (Math.abs(rowBrittleness - brittlenessValue) < 0.01) {
                    row.classList.add('highlighted-row');
                    // 滚动到可见区域
                    row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    found = true;
                }
            });
            
            if (!found) {
                console.log(`未找到脆性指数为 ${brittlenessValue} 的数据行`);
            }
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化图表
            initChart();

            // 返回仪表盘按钮事件
            document.getElementById('dashboard-btn').addEventListener('click', function (e) {
                e.preventDefault();
                if (isRunningInWebView()) {
                    // 在WebView2中运行时，通知C#返回仪表盘
                    window.chrome.webview.postMessage({
                        action: 'backToDashboard'
                    });
                    // 关闭当前窗体并返回OK
                    window.chrome.webview.postMessage({
                        action: 'closeWithOK'
                    });
                } else {
                    // 在浏览器中运行时，直接跳转到仪表盘页面
                    window.location.href = 'dashboard.html';
                }
            });

            // 退出登录按钮事件
            document.getElementById('logout-btn').addEventListener('click', function (e) {
                e.preventDefault();
                if (isRunningInWebView()) {
                    // 在WebView2中运行时，通知C#进行退出操作
                    window.chrome.webview.postMessage({
                        action: 'closeWithOK'
                    });
                } else {
                    // 在浏览器中运行时，直接跳转到登录页
                    window.location.href = 'index.html';
                }
            });

            // 导入数据按钮事件
            document.getElementById('btn-import').addEventListener('click', function () {
                showFileDialog();
            });

            // 文件对话框关闭按钮事件
            document.querySelector('.close-btn').addEventListener('click', function () {
                hideFileDialog();
            });

            // 文件对话框取消按钮事件
            document.getElementById('btn-file-cancel').addEventListener('click', function () {
                hideFileDialog();
            });

            // 计算脆性指数按钮事件
            document.getElementById('btn-calculate').addEventListener('click', function () {
                const quartz = parseFloat(document.getElementById('input-quartz').value) || 0;
                const feldspar = parseFloat(document.getElementById('input-feldspar').value) || 0;
                const carbonate = parseFloat(document.getElementById('input-carbonate').value) || 0;
                const clay = parseFloat(document.getElementById('input-clay').value) || 0;

                if (isRunningInWebView()) {
                    // 在WebView2中运行时，通知C#计算脆性指数
                    window.chrome.webview.postMessage({
                        action: 'calculateBrittleness',
                        quartz: quartz,
                        feldspar: feldspar,
                        carbonate: carbonate,
                        clay: clay
                    });
                } else {
                    // 在浏览器中运行时，本地计算脆性指数
                    const brittleness = (quartz + feldspar + carbonate) / (quartz + feldspar + carbonate + clay) * 100;
                    document.getElementById('brittleness-result').textContent = brittleness.toFixed(2);
                }
            });

            // 生成曲线按钮事件
            document.getElementById('btn-generate-curve').addEventListener('click', function () {
                if (isRunningInWebView()) {
                    // 在WebView2中运行时，请求矿物数据
                    window.chrome.webview.postMessage({
                        action: 'getMineralData'
                    });
                } else {
                    // 在浏览器中运行时，使用示例数据
                    const sampleData = [
                        { topDepth: 100.0, bottomDepth: 110.0, quartz: 30.0, feldspar: 20.0, carbonate: 40.0, clay: 10.0, brittleness: 50.0 },
                        { topDepth: 110.0, bottomDepth: 120.0, quartz: 35.0, feldspar: 25.0, carbonate: 30.0, clay: 10.0, brittleness: 60.0 },
                        { topDepth: 120.0, bottomDepth: 130.0, quartz: 40.0, feldspar: 20.0, carbonate: 30.0, clay: 10.0, brittleness: 60.0 },
                        { topDepth: 130.0, bottomDepth: 140.0, quartz: 45.0, feldspar: 15.0, carbonate: 30.0, clay: 10.0, brittleness: 60.0 },
                        { topDepth: 140.0, bottomDepth: 150.0, quartz: 50.0, feldspar: 10.0, carbonate: 30.0, clay: 10.0, brittleness: 60.0 }
                    ];
                    populateTable(sampleData);
                    updateChart(sampleData);
                }
            });

            // 重置视图按钮事件
            document.getElementById('btn-reset-view').addEventListener('click', function () {
                if (brittlenessChart) {
                    brittlenessChart.resetZoom();
                }
            });

            // 如果在WebView2中运行，请求矿物数据
            if (isRunningInWebView()) {
                window.chrome.webview.postMessage({
                    action: 'getMineralData'
                });
            }
        });

        // 监听来自C#的消息
        if (isRunningInWebView()) {
            window.chrome.webview.addEventListener('message', event => {
                const data = event.data;

                if (data.action === 'mineralData') {
                    // 处理矿物数据
                    populateTable(data.data);
                    updateChart(data.data);
                } else if (data.action === 'brittlenessResult') {
                    // 处理脆性指数计算结果
                    document.getElementById('brittleness-result').textContent = data.brittleness;
                }
            });
        }
    </script>
</body>

</html>