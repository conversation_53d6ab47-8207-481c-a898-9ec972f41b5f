#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础步骤测试 - 逐步排查问题
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_step_1():
    """测试步骤1：导入模块"""
    print("步骤1：测试模块导入...")
    try:
        from modules.pdf_reader import PDFReader
        print("✓ PDFReader 导入成功")
    except Exception as e:
        print(f"✗ PDFReader 导入失败: {e}")
        return False
    
    try:
        from modules.citation_parser import CitationParser
        print("✓ CitationParser 导入成功")
    except Exception as e:
        print(f"✗ CitationParser 导入失败: {e}")
        return False
    
    return True

def test_step_2():
    """测试步骤2：创建对象"""
    print("\n步骤2：测试对象创建...")
    try:
        from modules.pdf_reader import PDFReader
        pdf_reader = PDFReader()
        print("✓ PDFReader 对象创建成功")
    except Exception as e:
        print(f"✗ PDFReader 对象创建失败: {e}")
        return False, None
    
    try:
        from modules.citation_parser import CitationParser
        parser = CitationParser()
        print("✓ CitationParser 对象创建成功")
    except Exception as e:
        print(f"✗ CitationParser 对象创建失败: {e}")
        return False, None
    
    return True, (pdf_reader, parser)

def test_step_3(pdf_reader):
    """测试步骤3：PDF文本提取"""
    print("\n步骤3：测试PDF文本提取...")
    pdf_file = "paper.pdf"
    
    if not Path(pdf_file).exists():
        print(f"✗ PDF文件不存在: {pdf_file}")
        return False, None
    
    try:
        text = pdf_reader.extract_text(pdf_file)
        if text:
            print(f"✓ PDF文本提取成功，长度: {len(text)} 字符")
            return True, text
        else:
            print("✗ PDF文本提取失败：返回空文本")
            return False, None
    except Exception as e:
        print(f"✗ PDF文本提取失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_step_4(pdf_reader, text):
    """测试步骤4：References部分提取"""
    print("\n步骤4：测试References部分提取...")
    try:
        references_text = pdf_reader.extract_references_section(text)
        if references_text:
            print(f"✓ References部分提取成功，长度: {len(references_text)} 字符")
            return True, references_text
        else:
            print("✗ References部分提取失败：返回空文本")
            return False, None
    except Exception as e:
        print(f"✗ References部分提取失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_step_5(pdf_reader, references_text):
    """测试步骤5：引用解析"""
    print("\n步骤5：测试引用解析...")
    try:
        citations = pdf_reader.parse_references_section(references_text)
        if citations:
            print(f"✓ 引用解析成功，找到 {len(citations)} 个引用")
            
            # 检查第一个引用的结构
            if len(citations) > 0:
                first_citation = citations[0]
                print(f"第一个引用的字段:")
                for key, value in first_citation.items():
                    print(f"  {key}: {type(value).__name__} = {repr(value)[:50]}...")
            
            return True, citations
        else:
            print("✗ 引用解析失败：返回空列表")
            return False, None
    except Exception as e:
        print(f"✗ 引用解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_step_6(parser, citations):
    """测试步骤6：引用进一步解析"""
    print("\n步骤6：测试引用进一步解析...")
    try:
        parsed_citations = parser.parse_multiple_citations(citations)
        if parsed_citations:
            print(f"✓ 引用进一步解析成功，处理 {len(parsed_citations)} 个引用")
            
            # 检查第一个解析后引用的结构
            if len(parsed_citations) > 0:
                first_parsed = parsed_citations[0]
                print(f"第一个解析后引用的字段:")
                for key, value in first_parsed.items():
                    print(f"  {key}: {type(value).__name__} = {repr(value)[:50]}...")
            
            return True, parsed_citations
        else:
            print("✗ 引用进一步解析失败：返回空列表")
            return False, None
    except Exception as e:
        print(f"✗ 引用进一步解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主测试函数"""
    print("开始基础步骤测试...")
    print("=" * 60)
    
    # 步骤1：导入测试
    if not test_step_1():
        print("\n测试在步骤1失败，停止测试")
        return
    
    # 步骤2：对象创建测试
    success, objects = test_step_2()
    if not success:
        print("\n测试在步骤2失败，停止测试")
        return
    
    pdf_reader, parser = objects
    
    # 步骤3：PDF文本提取测试
    success, text = test_step_3(pdf_reader)
    if not success:
        print("\n测试在步骤3失败，停止测试")
        return
    
    # 步骤4：References部分提取测试
    success, references_text = test_step_4(pdf_reader, text)
    if not success:
        print("\n测试在步骤4失败，停止测试")
        return
    
    # 步骤5：引用解析测试
    success, citations = test_step_5(pdf_reader, references_text)
    if not success:
        print("\n测试在步骤5失败，停止测试")
        return
    
    # 步骤6：引用进一步解析测试
    success, parsed_citations = test_step_6(parser, citations)
    if not success:
        print("\n测试在步骤6失败，停止测试")
        return
    
    print("\n" + "=" * 60)
    print("✓ 所有基础步骤测试通过！")
    print(f"最终结果：成功处理 {len(parsed_citations)} 个文献引用")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n程序异常退出: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")  # 防止窗口闪退
