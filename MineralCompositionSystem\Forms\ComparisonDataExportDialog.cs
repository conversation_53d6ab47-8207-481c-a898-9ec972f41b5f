using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MineralCompositionSystem.Services;

namespace MineralCompositionSystem.Forms
{
    /// <summary>
    /// 对比图数据导出对话框
    /// </summary>
    public partial class ComparisonDataExportDialog : Form
    {
        private List<object> _dataPoints;
        private string _systemName;

        // 控件声明
        private GroupBox gbExportFormat;
        private RadioButton rbJSON;
        private RadioButton rbExcel;
        private RadioButton rbCSV;
        private RadioButton rbXML;
        private CheckBox cbMultipleFormats;

        private GroupBox gbOutputLocation;
        private RadioButton rbLocalFile;
        private RadioButton rbRemovableDevice;
        private RadioButton rbNetworkLocation;
        private RadioButton rbCustomPath;

        private GroupBox gbOptions;
        private CheckBox cbIncludeMetadata;
        private CheckBox cbCompressOutput;
        private CheckBox cbOpenAfterExport;
        private CheckBox cbShowPreview;

        private TextBox txtOutputPath;
        private Button btnBrowse;
        private Button btnPreview;
        private Button btnExport;
        private Button btnCancel;

        private Label lblDataCount;
        private Label lblEstimatedSize;
        private ProgressBar progressBar;

        public ComparisonDataExportDialog(List<object> dataPoints, string systemName)
        {
            _dataPoints = dataPoints ?? new List<object>();
            _systemName = systemName ?? "未知系统";

            InitializeComponent();
            InitializeData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.Text = "导出对比图数据";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 导出格式组
            gbExportFormat = new GroupBox();
            gbExportFormat.Text = "导出格式";
            gbExportFormat.Location = new Point(12, 12);
            gbExportFormat.Size = new Size(280, 120);

            rbJSON = new RadioButton();
            rbJSON.Text = "JSON格式 (推荐)";
            rbJSON.Location = new Point(10, 20);
            rbJSON.Checked = true;

            rbExcel = new RadioButton();
            rbExcel.Text = "Excel格式 (.xlsx)";
            rbExcel.Location = new Point(10, 45);

            rbCSV = new RadioButton();
            rbCSV.Text = "CSV格式";
            rbCSV.Location = new Point(10, 70);

            rbXML = new RadioButton();
            rbXML.Text = "XML格式";
            rbXML.Location = new Point(150, 20);

            cbMultipleFormats = new CheckBox();
            cbMultipleFormats.Text = "同时导出多种格式";
            cbMultipleFormats.Location = new Point(10, 95);
            cbMultipleFormats.CheckedChanged += CbMultipleFormats_CheckedChanged;

            gbExportFormat.Controls.AddRange(new Control[] { rbJSON, rbExcel, rbCSV, rbXML, cbMultipleFormats });

            // 输出位置组
            gbOutputLocation = new GroupBox();
            gbOutputLocation.Text = "输出位置";
            gbOutputLocation.Location = new Point(300, 12);
            gbOutputLocation.Size = new Size(280, 120);

            rbLocalFile = new RadioButton();
            rbLocalFile.Text = "本地文件";
            rbLocalFile.Location = new Point(10, 20);
            rbLocalFile.Checked = true;

            rbRemovableDevice = new RadioButton();
            rbRemovableDevice.Text = "可移动设备";
            rbRemovableDevice.Location = new Point(10, 45);

            rbNetworkLocation = new RadioButton();
            rbNetworkLocation.Text = "网络位置";
            rbNetworkLocation.Location = new Point(10, 70);

            rbCustomPath = new RadioButton();
            rbCustomPath.Text = "自定义路径";
            rbCustomPath.Location = new Point(150, 20);

            gbOutputLocation.Controls.AddRange(new Control[] { rbLocalFile, rbRemovableDevice, rbNetworkLocation, rbCustomPath });

            // 选项组
            gbOptions = new GroupBox();
            gbOptions.Text = "导出选项";
            gbOptions.Location = new Point(12, 140);
            gbOptions.Size = new Size(568, 80);

            cbIncludeMetadata = new CheckBox();
            cbIncludeMetadata.Text = "包含元数据信息";
            cbIncludeMetadata.Location = new Point(10, 20);
            cbIncludeMetadata.Checked = true;

            cbCompressOutput = new CheckBox();
            cbCompressOutput.Text = "压缩输出文件";
            cbCompressOutput.Location = new Point(150, 20);

            cbOpenAfterExport = new CheckBox();
            cbOpenAfterExport.Text = "导出后打开文件";
            cbOpenAfterExport.Location = new Point(290, 20);
            cbOpenAfterExport.Checked = true;

            cbShowPreview = new CheckBox();
            cbShowPreview.Text = "显示数据预览";
            cbShowPreview.Location = new Point(430, 20);
            cbShowPreview.Checked = true;

            gbOptions.Controls.AddRange(new Control[] { cbIncludeMetadata, cbCompressOutput, cbOpenAfterExport, cbShowPreview });

            // 输出路径
            Label lblPath = new Label();
            lblPath.Text = "输出路径:";
            lblPath.Location = new Point(12, 235);
            lblPath.Size = new Size(70, 20);

            txtOutputPath = new TextBox();
            txtOutputPath.Location = new Point(85, 233);
            txtOutputPath.Size = new Size(400, 23);
            txtOutputPath.Text = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);

            btnBrowse = new Button();
            btnBrowse.Text = "浏览...";
            btnBrowse.Location = new Point(495, 232);
            btnBrowse.Size = new Size(75, 25);
            btnBrowse.Click += BtnBrowse_Click;

            // 信息标签
            lblDataCount = new Label();
            lblDataCount.Location = new Point(12, 270);
            lblDataCount.Size = new Size(200, 20);

            lblEstimatedSize = new Label();
            lblEstimatedSize.Location = new Point(220, 270);
            lblEstimatedSize.Size = new Size(200, 20);

            // 进度条
            progressBar = new ProgressBar();
            progressBar.Location = new Point(12, 300);
            progressBar.Size = new Size(568, 23);
            progressBar.Visible = false;

            // 按钮
            btnPreview = new Button();
            btnPreview.Text = "预览数据";
            btnPreview.Location = new Point(340, 340);
            btnPreview.Size = new Size(80, 30);
            btnPreview.Click += BtnPreview_Click;

            btnExport = new Button();
            btnExport.Text = "开始导出";
            btnExport.Location = new Point(430, 340);
            btnExport.Size = new Size(80, 30);
            btnExport.Click += BtnExport_Click;

            btnCancel = new Button();
            btnCancel.Text = "取消";
            btnCancel.Location = new Point(520, 340);
            btnCancel.Size = new Size(60, 30);
            btnCancel.DialogResult = DialogResult.Cancel;

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] {
                gbExportFormat, gbOutputLocation, gbOptions,
                lblPath, txtOutputPath, btnBrowse,
                lblDataCount, lblEstimatedSize, progressBar,
                btnPreview, btnExport, btnCancel
            });

            this.ResumeLayout(false);
        }

        private void InitializeData()
        {
            lblDataCount.Text = $"数据点数量: {_dataPoints.Count}";

            // 估算文件大小
            int estimatedSize = _dataPoints.Count * 100; // 每个数据点大约100字节
            lblEstimatedSize.Text = $"预估大小: {FormatFileSize(estimatedSize)}";
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
        }

        private void CbMultipleFormats_CheckedChanged(object sender, EventArgs e)
        {
            bool multipleEnabled = cbMultipleFormats.Checked;
            rbJSON.Enabled = !multipleEnabled;
            rbExcel.Enabled = !multipleEnabled;
            rbCSV.Enabled = !multipleEnabled;
            rbXML.Enabled = !multipleEnabled;

            if (multipleEnabled)
            {
                rbJSON.Checked = false;
                rbExcel.Checked = false;
                rbCSV.Checked = false;
                rbXML.Checked = false;
            }
            else
            {
                rbJSON.Checked = true;
            }
        }

        private void BtnBrowse_Click(object sender, EventArgs e)
        {
            if (rbLocalFile.Checked || rbCustomPath.Checked)
            {
                using (FolderBrowserDialog dialog = new FolderBrowserDialog())
                {
                    dialog.Description = "选择导出文件夹";
                    dialog.SelectedPath = txtOutputPath.Text;

                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        txtOutputPath.Text = dialog.SelectedPath;
                    }
                }
            }
            else if (rbRemovableDevice.Checked)
            {
                ShowRemovableDevices();
            }
            else if (rbNetworkLocation.Checked)
            {
                ShowNetworkLocationDialog();
            }
        }

        private void ShowRemovableDevices()
        {
            var removableDevices = DriveInfo.GetDrives()
                .Where(d => d.DriveType == DriveType.Removable && d.IsReady)
                .ToList();

            if (removableDevices.Count == 0)
            {
                MessageBox.Show("未检测到可移动设备！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string message = "检测到以下可移动设备:\n\n";
            for (int i = 0; i < removableDevices.Count; i++)
            {
                var drive = removableDevices[i];
                message += $"{i + 1}. {drive.Name} ({FormatFileSize(drive.AvailableFreeSpace)} 可用)\n";
            }
            message += "\n请在路径框中输入设备盘符 (如: E:\\)";

            MessageBox.Show(message, "可移动设备", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNetworkLocationDialog()
        {
            using (var inputDialog = new Form())
            {
                inputDialog.Text = "网络位置";
                inputDialog.Size = new Size(400, 150);
                inputDialog.StartPosition = FormStartPosition.CenterParent;
                inputDialog.FormBorderStyle = FormBorderStyle.FixedDialog;
                inputDialog.MaximizeBox = false;
                inputDialog.MinimizeBox = false;

                var label = new Label();
                label.Text = "请输入网络路径 (如: \\\\server\\share):";
                label.Location = new Point(12, 15);
                label.Size = new Size(360, 20);

                var textBox = new TextBox();
                textBox.Location = new Point(12, 40);
                textBox.Size = new Size(360, 23);
                textBox.Text = txtOutputPath.Text;

                var btnOK = new Button();
                btnOK.Text = "确定";
                btnOK.Location = new Point(217, 75);
                btnOK.Size = new Size(75, 25);
                btnOK.DialogResult = DialogResult.OK;

                var btnCancel = new Button();
                btnCancel.Text = "取消";
                btnCancel.Location = new Point(297, 75);
                btnCancel.Size = new Size(75, 25);
                btnCancel.DialogResult = DialogResult.Cancel;

                inputDialog.Controls.AddRange(new Control[] { label, textBox, btnOK, btnCancel });

                if (inputDialog.ShowDialog() == DialogResult.OK && !string.IsNullOrEmpty(textBox.Text))
                {
                    txtOutputPath.Text = textBox.Text;
                }
            }
        }

        private void BtnPreview_Click(object sender, EventArgs e)
        {
            if (!cbShowPreview.Checked)
            {
                cbShowPreview.Checked = true;
            }

            ShowDataPreview();
        }

        private void ShowDataPreview()
        {
            var previewForm = new DataPreviewForm(_dataPoints, _systemName);
            previewForm.ShowDialog();
        }

        private async void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtOutputPath.Text))
                {
                    MessageBox.Show("请选择输出路径！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (_dataPoints.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 显示预览（如果选中）
                if (cbShowPreview.Checked)
                {
                    var result = MessageBox.Show("是否要查看数据预览？", "确认", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                    if (result == DialogResult.Cancel) return;
                    if (result == DialogResult.Yes)
                    {
                        ShowDataPreview();
                        return;
                    }
                }

                // 开始导出
                await PerformExport();

                MessageBox.Show("数据导出完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task PerformExport()
        {
            progressBar.Visible = true;
            progressBar.Value = 0;

            var exportService = new ComparisonDataExportService();
            var exportOptions = new ComparisonDataExportOptions
            {
                OutputPath = txtOutputPath.Text,
                SystemName = _systemName,
                IncludeMetadata = cbIncludeMetadata.Checked,
                CompressOutput = cbCompressOutput.Checked,
                OpenAfterExport = cbOpenAfterExport.Checked
            };

            if (cbMultipleFormats.Checked)
            {
                // 导出多种格式
                var formats = new List<ExportFormat> {
                    ExportFormat.JSON,
                    ExportFormat.Excel,
                    ExportFormat.CSV,
                    ExportFormat.XML
                };
                await exportService.ExportMultipleFormats(_dataPoints, exportOptions, formats,
                    progress => progressBar.Value = progress);
            }
            else
            {
                // 导出单一格式
                ExportFormat format = GetSelectedFormat();
                await exportService.ExportSingleFormat(_dataPoints, exportOptions, format,
                    progress => progressBar.Value = progress);
            }

            progressBar.Visible = false;
        }

        private ExportFormat GetSelectedFormat()
        {
            if (rbJSON.Checked) return ExportFormat.JSON;
            if (rbExcel.Checked) return ExportFormat.Excel;
            if (rbCSV.Checked) return ExportFormat.CSV;
            if (rbXML.Checked) return ExportFormat.XML;
            return ExportFormat.JSON;
        }
    }
}
