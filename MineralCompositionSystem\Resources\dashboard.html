<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>脆性指数系统 - 控制面板</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@4.24.13/dist/antd.min.css">
    <script src="https://cdn.jsdelivr.net/npm/antd@4.24.13/dist/antd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@3.4.21/dist/vue.global.prod.js"></script>
</head>
<body>
<div id="app">
    <a-layout class="container" style="min-height:100vh;">
        <a-layout-header style="background:#fff;display:flex;justify-content:space-between;align-items:center;box-shadow:0 2px 8px #f0f1f2;">
            <div style="font-size:1.5rem;font-weight:bold;color:#2a5298;">脆性指数系统</div>
            <div class="user-info">
                <span class="username" id="current-username">{{ username }}</span>
                <a @click="logout" class="logout-btn" style="margin-left:24px;">退出登录</a>
            </div>
        </a-layout-header>
        <a-layout>
            <a-layout-sider width="220" style="background:#fff;">
                <a-menu mode="inline" :selectedKeys="[activeMenu]" @click="onMenuClick">
                    <a-menu-item key="dashboard">控制面板</a-menu-item>
                    <a-menu-item key="mineralogical">矿物组分法</a-menu-item>
                    <a-menu-item key="data">数据管理</a-menu-item>
                    <a-menu-item key="index">指数计算</a-menu-item>
                    <a-menu-item key="report">报告生成</a-menu-item>
                    <a-menu-item key="setting">系统设置</a-menu-item>
                </a-menu>
            </a-layout-sider>
            <a-layout-content style="padding:32px;">
                <div class="dashboard-header" style="margin-bottom:24px;">
                    <h2 style="margin-bottom:8px;">控制面板</h2>
                    <p>欢迎使用脆性指数系统，{{ username }}</p>
                </div>
                <a-row gutter="24" class="stats-container" style="margin-bottom:32px;">
                    <a-col :span="6" v-for="(stat, idx) in stats" :key="idx">
                        <a-card hoverable style="text-align:center;">
                            <h3>{{ stat.title }}</h3>
                            <a-statistic :value="stat.value" :valueStyle="{color:'#2a5298',fontSize:'2rem'}" />
                            <p class="stat-desc">{{ stat.description }}</p>
                        </a-card>
                    </a-col>
                </a-row>
                <a-card title="最近活动" class="recent-activity">
                    <a-table :dataSource="activities" :columns="activityColumns" rowKey="id" size="small" bordered :pagination="false" />
                </a-card>
            </a-layout-content>
        </a-layout>
        <a-layout-footer style="text-align:center;background:#fff;">&copy; 2023 脆性指数系统 - 版权所有</a-layout-footer>
    </a-layout>
</div>
<script>
const { createApp, ref, reactive, onMounted } = Vue;
createApp({
    setup() {
        const username = ref('admin');
        const activeMenu = ref('dashboard');
        const stats = ref([
            { title: '数据总量', value: 0, description: '条记录' },
            { title: '计算任务', value: 0, description: '个进行中' },
            { title: '报告数量', value: 0, description: '份已生成' },
            { title: '系统状态', value: '正常', description: '运行良好' }
        ]);
        const activities = ref([]);
        const activityColumns = [
            { title: '时间', dataIndex: 'time', key: 'time', width: 160 },
            { title: '活动', dataIndex: 'activity', key: 'activity', width: 120 },
            { title: '用户', dataIndex: 'user', key: 'user', width: 100 },
            { title: '状态', dataIndex: 'status', key: 'status', width: 100, customRender: ({text}) => text==='成功'?Vue.h('span',{style:'color:#52c41a'},'成功'):text }
        ];
        const isRunningInWebView = () => window.chrome && window.chrome.webview;
        const logout = () => {
            if (isRunningInWebView()) {
                window.chrome.webview.postMessage({ action: 'closeWithOK' });
            } else {
                window.location.href = 'index.html';
            }
        };
        const onMenuClick = ({ key }) => {
            if (key === 'mineralogical') {
                if (isRunningInWebView()) {
                    window.chrome.webview.postMessage({ action: 'openMineralogical' });
                    window.chrome.webview.postMessage({ action: 'closeWithOK' });
                } else {
                    alert('此功能仅在应用程序中可用');
                }
            }
            activeMenu.value = key;
        };
        onMounted(() => {
            if (isRunningInWebView()) {
                window.chrome.webview.postMessage({ action: 'getUserInfo' });
                window.chrome.webview.postMessage({ action: 'getSystemStats' });
                window.chrome.webview.postMessage({ action: 'getRecentActivities' });
                window.chrome.webview.addEventListener('message', event => {
                    const data = event.data;
                    if (data.action === 'userInfo') {
                        username.value = data.username || 'admin';
                    }
                    if (data.action === 'systemStats' && Array.isArray(data.stats)) {
                        data.stats.forEach((item, idx) => {
                            if (stats.value[idx]) {
                                stats.value[idx].value = item.value;
                                stats.value[idx].description = item.description;
                            }
                        });
                    }
                    if (data.action === 'recentActivities' && Array.isArray(data.activities)) {
                        activities.value = data.activities.map((a,i) => ({ id: i+1, ...a }));
                    }
                });
            } else {
                // 浏览器演示数据
                stats.value = [
                    { title: '数据总量', value: 1234, description: '条记录' },
                    { title: '计算任务', value: 5, description: '个进行中' },
                    { title: '报告数量', value: 42, description: '份已生成' },
                    { title: '系统状态', value: '正常', description: '运行良好' }
                ];
                activities.value = [
                    { time: '2023-06-15 14:30', activity: '数据导入', user: 'admin', status: '成功' },
                    { time: '2023-06-15 13:45', activity: '指数计算', user: 'admin', status: '成功' },
                    { time: '2023-06-15 11:20', activity: '报告生成', user: 'admin', status: '成功' },
                    { time: '2023-06-14 16:05', activity: '系统备份', user: 'admin', status: '成功' }
                ];
            }
        });
        return { username, stats, activities, activityColumns, logout, activeMenu, onMenuClick };
    }
}).use(antd).mount('#app');
</script>
</body>
</html>