#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全测试 - 检查数据类型
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.pdf_reader import PDFReader
from modules.citation_parser import CitationParser

def check_data_types(citations):
    """检查引用数据的类型"""
    print("检查数据类型...")
    
    for i, citation in enumerate(citations[:3]):  # 只检查前3个
        print(f"\n引用 {i+1} 的数据类型:")
        for key, value in citation.items():
            print(f"  {key}: {type(value).__name__} = {repr(value)}")

def test_safe():
    """安全测试"""
    pdf_file = "paper.pdf"
    
    if not Path(pdf_file).exists():
        print(f"错误: 找不到文件 {pdf_file}")
        return
    
    print("开始安全测试...")
    print("=" * 50)
    
    try:
        # 创建PDF读取器
        pdf_reader = PDFReader()
        
        # 提取引用
        print("1. 提取PDF文本...")
        text = pdf_reader.extract_text(pdf_file)
        
        print("2. 识别文献引用...")
        citations = pdf_reader.find_citations(text)
        
        print(f"3. 识别结果: {len(citations)} 个引用")
        
        # 检查数据类型
        if citations:
            check_data_types(citations)
        
        # 测试解析器
        print("\n4. 测试引用解析器...")
        parser = CitationParser()
        
        try:
            parsed_citations = parser.parse_multiple_citations(citations)
            print(f"5. 解析成功: {len(parsed_citations)} 个引用")
            
            # 检查解析后的数据类型
            if parsed_citations:
                print("\n解析后的数据类型:")
                check_data_types(parsed_citations)
                
        except Exception as e:
            print(f"5. 解析失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 50)
        print("安全测试完成")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_safe()
