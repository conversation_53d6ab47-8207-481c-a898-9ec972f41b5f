using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Newtonsoft.Json;
using System.Collections.Generic;
using StaticRockMechanicsSystem.Services;
using StaticRockMechanicsSystem.Models;

namespace StaticRockMechanicsSystem.Forms
{
    /// <summary>
    /// 静态岩石力学参数法脆性指数计算窗体
    /// </summary>
    public partial class StaticRockMechanicsForm : Form
    {
        #region 字段和属性

        private readonly string username = "";
        private DataTable mechanicsData = new DataTable();
        private DataTable? originalMechanicsData;
        private string? currentExcelFile;

        // 图表缩放相关字段
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private const double MAX_ZOOM = 15.0;  // Y轴最大放大15倍
        private const double MAX_X_ZOOM = 3.0; // X轴最大放大3倍
        private const double MIN_ZOOM = 1.0;   // 最小不缩小
        private const double ZOOM_FACTOR = 1.2;

        // 数据点和交互相关字段
        private List<RockMechanicsDataPoint> dataPoints = new List<RockMechanicsDataPoint>();
        private HashSet<int> selectedRows = new HashSet<int>();
        private bool showDataPoints = false; // 控制是否显示数据点

        // 列名识别字典
        private readonly Dictionary<string, List<string>> columnPatterns = new Dictionary<string, List<string>>
        {
            ["深度"] = new List<string> { "深度", "depth", "depths", "顶深", "井深", "md", "tvd", "顶深/m", "深度/m", "深度(m)" },
            ["密度"] = new List<string> { "密度", "ρ", "rho", "rhob", "density", "den", "密度/(g/cm³)", "密度(g/cm³)", "岩石密度" },
            ["纵波速度"] = new List<string> { "纵波速度", "vp", "纵波", "p波", "dt", "纵波时差", "纵波速度/(m/s)", "纵波速度(m/s)", "vp(m/s)" },
            ["横波速度"] = new List<string> { "横波速度", "vs", "横波", "s波", "dts", "横波时差", "横波速度/(m/s)", "横波速度(m/s)", "vs(m/s)" }
        };

        #endregion

        #region 构造函数

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public StaticRockMechanicsForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎使用静态岩石力学参数法, {username}";
            }
        }

        #endregion

        #region 初始化方法

        private void InitializeForm()
        {
            // 初始化数据表
            InitializeDataTable();

            // 绑定事件
            Load += StaticRockMechanicsForm_Load;
            Resize += StaticRockMechanicsForm_Resize;
            FormClosing += StaticRockMechanicsForm_FormClosing;

            // 绑定单位选择事件（如果控件存在）
            BindUnitSelectionEvents();
        }

        private void InitializeDataTable()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("顶深/m", typeof(double));
            mechanicsData.Columns.Add("底深/m", typeof(double));
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            if (dgvMechanicsData != null)
            {
                dgvMechanicsData.DataSource = mechanicsData;
            }
        }

        /// <summary>
        /// 绑定单位选择控件的事件处理器
        /// </summary>
        private void BindUnitSelectionEvents()
        {
            try
            {
                // 查找并绑定密度单位选择控件
                var rbDensityRho = FindControlByName("rbDensityRho") as RadioButton;
                var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;

                // 查找并绑定纵波单位选择控件
                var rbVelocityVp = FindControlByName("rbVelocityVp") as RadioButton;
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;

                // 查找并绑定横波单位选择控件
                var rbVelocityVs = FindControlByName("rbVelocityVs") as RadioButton;
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;

                // 设置默认选择：DT和DTS（因为这样计算出的脆性指数更准确）
                if (rbVelocityDt != null) rbVelocityDt.Checked = true;
                if (rbVelocityDts != null) rbVelocityDts.Checked = true;
                if (rbDensityRho != null) rbDensityRho.Checked = true; // 默认选择ρ

                // 绑定事件处理器
                if (rbDensityRho != null) rbDensityRho.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbDensityRhob != null) rbDensityRhob.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityVp != null) rbVelocityVp.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityDt != null) rbVelocityDt.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityVs != null) rbVelocityVs.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityDts != null) rbVelocityDts.CheckedChanged += UnitSelection_CheckedChanged;
            }
            catch (Exception ex)
            {
                // 如果控件不存在，忽略错误（设计器中可能还未创建）
                System.Diagnostics.Debug.WriteLine($"绑定单位选择事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归查找控件
        /// </summary>
        private Control? FindControlByName(string name)
        {
            return FindControlByName(this, name);
        }

        /// <summary>
        /// 递归查找指定名称的控件
        /// </summary>
        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        #endregion

        #region 事件处理方法

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
            UpdateParameterLabels();
        }

        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            // 窗体大小改变时调整控件位置
            // 这里可以添加响应式布局代码，或者在设计器中使用Anchor和Dock属性
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnEmergencyExit_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有图表数据可以保存
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("没有图表数据可以保存为对比图！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 保存当前图表数据到全局存储
                SaveChartDataForComparison();

                MessageBox.Show("图表数据已保存，可以在其他系统中查看对比图！", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存对比图数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存图表数据用于对比
        /// </summary>
        private void SaveChartDataForComparison()
        {
            try
            {
                // 从图表中提取数据点，确保坐标轴正确
                var chartDataPoints = new List<object>();

                if (chartBrittleness.Series.Count > 0)
                {
                    var series = chartBrittleness.Series[0];
                    foreach (var point in series.Points)
                    {
                        double brittleIndex = point.XValue; // X轴是脆性指数
                        double depth = point.YValues[0];    // Y轴是深度

                        // 确保脆性指数在0-100范围内，深度大于0
                        if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                        {
                            chartDataPoints.Add(new
                            {
                                TopDepth = depth,
                                BottomDepth = depth,
                                BrittleIndex = brittleIndex
                            });
                        }
                    }
                }

                // 创建对比数据结构
                var comparisonData = new
                {
                    SystemName = "静态岩石力学参数法",
                    DataPoints = chartDataPoints,
                    SaveTime = DateTime.Now,
                    DataCount = chartDataPoints.Count
                };

                // 将数据序列化为JSON并保存到临时文件
                string jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(comparisonData, Newtonsoft.Json.Formatting.Indented);
                string tempPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");
                File.WriteAllText(tempPath, jsonData);

                System.Diagnostics.Debug.WriteLine($"静态岩石力学参数法图表数据已保存到: {tempPath}, 数据点数量: {chartDataPoints.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存对比图数据时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 查看对比图按钮点击事件
        /// </summary>
        private void BtnViewComparison_Click(object sender, EventArgs e)
        {
            try
            {
                // 简化的对比图功能
                MessageBox.Show("对比图功能暂未实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开对比图时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 单位选择变化事件处理器
        /// </summary>
        private void UnitSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is RadioButton rb && rb.Checked)
            {
                // 更新标签文本以反映当前选择的单位
                UpdateParameterLabels();
            }
        }

        /// <summary>
        /// 更新参数标签文本
        /// </summary>
        private void UpdateParameterLabels()
        {
            try
            {
                // 查找标签控件
                var lblDensity = FindControlByName("lblDensity") as Label;
                var lblVp = FindControlByName("lblVp") as Label;
                var lblVs = FindControlByName("lblVs") as Label;

                // 查找单位选择控件
                var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;

                // 更新密度标签
                if (lblDensity != null)
                {
                    lblDensity.Text = rbDensityRhob?.Checked == true ? "岩石密度 RHOB (g/cm³):" : "岩石密度 ρ (g/cm³):";
                }

                // 更新纵波标签
                if (lblVp != null)
                {
                    lblVp.Text = rbVelocityDt?.Checked == true ? "纵波时差 DT (μs/m):" : "纵波速度 Vp (m/s):";
                }

                // 更新横波标签
                if (lblVs != null)
                {
                    lblVs.Text = rbVelocityDts?.Checked == true ? "横波时差 DTS (μs/m):" : "横波速度 Vs (m/s):";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新参数标签时出错: {ex.Message}");
            }
        }

        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有导入的数据需要批量计算
                if (mechanicsData != null && mechanicsData.Rows.Count > 0)
                {
                    // 批量计算模式
                    CalculateBatchData();
                }
                else
                {
                    // 单个计算模式
                    CalculateSingleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 单个数据计算
        /// </summary>
        private void CalculateSingleData()
        {
            // 获取输入参数
            if (!double.TryParse(txtDensity.Text, out double inputDensity) ||
                !double.TryParse(txtVp.Text, out double inputVp) ||
                !double.TryParse(txtVs.Text, out double inputVs))
            {
                MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 进行单位转换，统一为标准单位
            double density = ConvertDensityToStandard(inputDensity);
            double vp = ConvertVelocityToStandard(inputVp, true); // true表示纵波
            double vs = ConvertVelocityToStandard(inputVs, false); // false表示横波

            // 使用RockMechanicsCalculationService计算
            var result = RockMechanicsCalculationService.CalculateComplete(density, vp, vs);

            // 显示计算结果
            lblCalculationResult.Text = $"Ed={result.DynamicYoungsModulus:F3}GPa, μd={result.DynamicPoissonsRatio:F4}, Es={result.StaticYoungsModulus:F3}GPa, μs={result.StaticPoissonsRatio:F4}, BRIT={result.BrittlenessIndex:F2}%";

            // 添加到数据表
            DataRow newRow = mechanicsData.NewRow();
            newRow["顶深/m"] = 0.0; // 默认值，用户可以在导入数据时修改
            newRow["底深/m"] = 0.0; // 默认值
            newRow["密度/(g/cm³)"] = density;
            newRow["纵波速度/(m/s)"] = vp;
            newRow["横波速度/(m/s)"] = vs;
            newRow["动态杨氏模量/GPa"] = result.DynamicYoungsModulus;
            newRow["动态泊松比"] = result.DynamicPoissonsRatio;
            newRow["静态杨氏模量/GPa"] = result.StaticYoungsModulus;
            newRow["静态泊松比"] = result.StaticPoissonsRatio;
            newRow["脆性指数/%"] = result.BrittlenessIndex;

            mechanicsData.Rows.Add(newRow);

            // 检查数据是否异常
            string warningMessage = "";
            if (vp <= Math.Sqrt(2) * vs)
            {
                warningMessage = "\n注意：输入数据可能异常（Vp ≤ √2·Vs），请检查数据准确性。";
            }

            MessageBox.Show($"计算成功！脆性指数为: {result.BrittlenessIndex:F2}%{warningMessage}", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 批量数据计算
        /// </summary>
        private void CalculateBatchData()
        {
            // 智能识别列名
            string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
            string? densityColumnName = FindColumnByPattern(mechanicsData, "密度");
            string? vpColumnName = FindColumnByPattern(mechanicsData, "纵波速度");
            string? vsColumnName = FindColumnByPattern(mechanicsData, "横波速度");

            // 检查必需的列是否存在
            if (string.IsNullOrEmpty(depthColumnName))
            {
                MessageBox.Show("未找到深度列，请检查数据格式！支持的深度列名包括：深度、depth、顶深、井深等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(densityColumnName))
            {
                MessageBox.Show("未找到密度列，请检查数据格式！支持的密度列名包括：密度、ρ、rho、rhob等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(vpColumnName))
            {
                MessageBox.Show("未找到纵波速度列，请检查数据格式！支持的纵波速度列名包括：纵波速度、vp、dt等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(vsColumnName))
            {
                MessageBox.Show("未找到横波速度列，请检查数据格式！支持的横波速度列名包括：横波速度、vs、dts等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 添加计算结果列（如果不存在）
            if (!mechanicsData.Columns.Contains("动态杨氏模量/GPa"))
                mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            if (!mechanicsData.Columns.Contains("动态泊松比"))
                mechanicsData.Columns.Add("动态泊松比", typeof(double));
            if (!mechanicsData.Columns.Contains("静态杨氏模量/GPa"))
                mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            if (!mechanicsData.Columns.Contains("静态泊松比"))
                mechanicsData.Columns.Add("静态泊松比", typeof(double));
            if (!mechanicsData.Columns.Contains("脆性指数/%"))
                mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            int calculatedCount = 0;
            int errorCount = 0;
            List<double> esValues = new List<double>();
            List<double> musValues = new List<double>();

            // 第一遍：计算所有数据并收集Es和μs值
            foreach (DataRow row in mechanicsData.Rows)
            {
                try
                {
                    if (row[densityColumnName] == DBNull.Value || row[vpColumnName] == DBNull.Value || row[vsColumnName] == DBNull.Value)
                        continue;

                    double density = Convert.ToDouble(row[densityColumnName]);
                    double vp = Convert.ToDouble(row[vpColumnName]);
                    double vs = Convert.ToDouble(row[vsColumnName]);

                    // 数据验证
                    var (isValid, errorMessage) = RockMechanicsCalculationService.ValidateInputParameters(density, vp, vs);
                    if (!isValid)
                    {
                        errorCount++;
                        continue;
                    }

                    // 计算岩石力学参数
                    var result = RockMechanicsCalculationService.CalculateComplete(density, vp, vs);

                    // 收集Es和μs值用于计算范围
                    esValues.Add(result.StaticYoungsModulus);
                    musValues.Add(result.StaticPoissonsRatio);

                    // 保存计算结果
                    row["动态杨氏模量/GPa"] = result.DynamicYoungsModulus;
                    row["动态泊松比"] = result.DynamicPoissonsRatio;
                    row["静态杨氏模量/GPa"] = result.StaticYoungsModulus;
                    row["静态泊松比"] = result.StaticPoissonsRatio;

                    calculatedCount++;
                }
                catch (Exception ex)
                {
                    errorCount++;
                    System.Diagnostics.Debug.WriteLine($"计算行数据时出错: {ex.Message}");
                }
            }

            if (calculatedCount == 0)
            {
                MessageBox.Show("没有有效的数据可以计算！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 计算Es和μs的范围
            double EsMin = esValues.Min();
            double EsMax = esValues.Max();
            double MuSMin = musValues.Min();
            double MuSMax = musValues.Max();

            // 第二遍：使用动态范围计算脆性指数
            foreach (DataRow row in mechanicsData.Rows)
            {
                try
                {
                    if (row["静态杨氏模量/GPa"] == DBNull.Value || row["静态泊松比"] == DBNull.Value)
                        continue;

                    double Es = Convert.ToDouble(row["静态杨氏模量/GPa"]);
                    double MuS = Convert.ToDouble(row["静态泊松比"]);

                    double brittlenessIndex = RockMechanicsCalculationService.CalculateBrittlenessIndex(Es, MuS, EsMin, EsMax, MuSMin, MuSMax);
                    row["脆性指数/%"] = brittlenessIndex;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"计算脆性指数时出错: {ex.Message}");
                }
            }

            // 更新图表
            UpdateChart();

            string message = $"批量计算完成！\n成功计算: {calculatedCount} 条记录\n";
            if (errorCount > 0)
            {
                message += $"错误记录: {errorCount} 条\n";
            }
            message += $"Es范围: {EsMin:F2} - {EsMax:F2} GPa\nμs范围: {MuSMin:F4} - {MuSMax:F4}";

            MessageBox.Show(message, "计算完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 根据模式查找列名
        /// </summary>
        private string? FindColumnByPattern(DataTable table, string patternKey)
        {
            if (!columnPatterns.ContainsKey(patternKey))
                return null;

            var patterns = columnPatterns[patternKey];

            foreach (DataColumn column in table.Columns)
            {
                string columnName = column.ColumnName.ToLower().Trim();
                foreach (string pattern in patterns)
                {
                    if (columnName.Contains(pattern.ToLower()))
                    {
                        return column.ColumnName;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 将密度转换为标准单位 (g/cm³)
        /// </summary>
        private double ConvertDensityToStandard(double inputValue)
        {
            // 检查是否选择了RHOB单位
            var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;

            // 无论选择什么单位，密度都是g/cm³，直接返回
            return inputValue;
        }

        /// <summary>
        /// 将速度转换为标准单位 (m/s)
        /// </summary>
        private double ConvertVelocityToStandard(double inputValue, bool isVp)
        {
            if (isVp)
            {
                // 纵波速度转换
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;
                if (rbVelocityDt?.Checked == true)
                {
                    // DT (μs/m) 转换为 Vp (m/s)
                    return 1000000.0 / inputValue;
                }
                else
                {
                    // 已经是 Vp (m/s)
                    return inputValue;
                }
            }
            else
            {
                // 横波速度转换
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;
                if (rbVelocityDts?.Checked == true)
                {
                    // DTS (μs/m) 转换为 Vs (m/s)
                    return 1000000.0 / inputValue;
                }
                else
                {
                    // 已经是 Vs (m/s)
                    return inputValue;
                }
            }
        }

        /// <summary>
        /// 更新图表
        /// </summary>
        private void UpdateChart()
        {
            try
            {
                if (chartBrittleness == null || mechanicsData == null || mechanicsData.Rows.Count == 0)
                    return;

                // 清空现有数据
                chartBrittleness.Series.Clear();
                chartBrittleness.ChartAreas.Clear();
                chartBrittleness.Legends.Clear();

                // 创建图表区域
                ChartArea chartArea = new ChartArea("MainArea");
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";
                chartArea.AxisY.IsReversed = true; // Y轴反转，深度从上到下
                chartArea.AxisX.Minimum = 0;
                chartArea.AxisX.Maximum = 100;
                chartBrittleness.ChartAreas.Add(chartArea);

                // 创建数据系列
                Series series = new Series("脆性指数");
                series.ChartType = SeriesChartType.Line;
                series.Color = Color.Blue;
                series.BorderWidth = 2;
                series.MarkerStyle = MarkerStyle.Circle;
                series.MarkerSize = 4;
                series.MarkerColor = Color.Red;

                // 添加数据点
                string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
                if (!string.IsNullOrEmpty(depthColumnName))
                {
                    foreach (DataRow row in mechanicsData.Rows)
                    {
                        try
                        {
                            if (row[depthColumnName] != DBNull.Value && row["脆性指数/%"] != DBNull.Value)
                            {
                                double depth = Convert.ToDouble(row[depthColumnName]);
                                double brittlenessIndex = Convert.ToDouble(row["脆性指数/%"]);

                                if (brittlenessIndex >= 0 && brittlenessIndex <= 100 && depth > 0)
                                {
                                    series.Points.AddXY(brittlenessIndex, depth);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"添加图表数据点时出错: {ex.Message}");
                        }
                    }
                }

                chartBrittleness.Series.Add(series);

                // 添加图例
                Legend legend = new Legend("Legend");
                legend.Docking = Docking.Top;
                chartBrittleness.Legends.Add(legend);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新图表时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 导入数据按钮点击事件
        /// </summary>
        private void BtnImport_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "Excel文件|*.xlsx;*.xls|所有文件|*.*";
                    openFileDialog.Title = "选择要导入的Excel文件";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        ImportExcelData(openFileDialog.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出数据按钮点击事件
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "Excel文件|*.xlsx|所有文件|*.*";
                    saveFileDialog.Title = "保存计算结果";
                    saveFileDialog.FileName = $"静态岩石力学参数计算结果_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExportToExcel(saveFileDialog.FileName);
                        MessageBox.Show("数据导出成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入Excel数据
        /// </summary>
        private void ImportExcelData(string filePath)
        {
            try
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook;
                    if (Path.GetExtension(filePath).ToLower() == ".xlsx")
                    {
                        workbook = new XSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new HSSFWorkbook(stream);
                    }

                    ISheet sheet = workbook.GetSheetAt(0);
                    if (sheet == null)
                    {
                        MessageBox.Show("Excel文件中没有找到工作表！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 读取表头
                    IRow headerRow = sheet.GetRow(0);
                    if (headerRow == null)
                    {
                        MessageBox.Show("Excel文件中没有找到表头！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 创建新的数据表
                    DataTable newData = new DataTable();
                    for (int i = 0; i < headerRow.LastCellNum; i++)
                    {
                        ICell cell = headerRow.GetCell(i);
                        string columnName = cell?.ToString() ?? $"列{i + 1}";
                        newData.Columns.Add(columnName, typeof(object));
                    }

                    // 读取数据行
                    for (int i = 1; i <= sheet.LastRowNum; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue;

                        DataRow dataRow = newData.NewRow();
                        for (int j = 0; j < newData.Columns.Count && j < row.LastCellNum; j++)
                        {
                            ICell cell = row.GetCell(j);
                            if (cell != null)
                            {
                                switch (cell.CellType)
                                {
                                    case CellType.Numeric:
                                        dataRow[j] = cell.NumericCellValue;
                                        break;
                                    case CellType.String:
                                        dataRow[j] = cell.StringCellValue;
                                        break;
                                    case CellType.Boolean:
                                        dataRow[j] = cell.BooleanCellValue;
                                        break;
                                    default:
                                        dataRow[j] = cell.ToString();
                                        break;
                                }
                            }
                        }
                        newData.Rows.Add(dataRow);
                    }

                    // 保存原始数据
                    originalMechanicsData = newData.Copy();
                    mechanicsData = newData;
                    currentExcelFile = filePath;

                    // 更新数据网格
                    if (dgvMechanicsData != null)
                    {
                        dgvMechanicsData.DataSource = mechanicsData;
                    }

                    MessageBox.Show($"成功导入 {mechanicsData.Rows.Count} 行数据！", "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入Excel文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出到Excel文件
        /// </summary>
        private void ExportToExcel(string filePath)
        {
            try
            {
                var workbook = new XSSFWorkbook();
                try
                {
                    var sheet = workbook.CreateSheet("静态岩石力学参数计算结果");

                    // 创建表头
                    var headerRow = sheet.CreateRow(0);
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        var cell = headerRow.CreateCell(i);
                        cell.SetCellValue(mechanicsData.Columns[i].ColumnName);

                        // 设置表头样式
                        var style = workbook.CreateCellStyle();
                        var font = workbook.CreateFont();
                        font.IsBold = true;
                        style.SetFont(font);
                        cell.CellStyle = style;
                    }

                    // 填充数据
                    for (int i = 0; i < mechanicsData.Rows.Count; i++)
                    {
                        var dataRow = sheet.CreateRow(i + 1);
                        for (int j = 0; j < mechanicsData.Columns.Count; j++)
                        {
                            var cell = dataRow.CreateCell(j);
                            var value = mechanicsData.Rows[i][j];

                            if (value != null && value != DBNull.Value)
                            {
                                if (double.TryParse(value.ToString(), out double numValue))
                                {
                                    cell.SetCellValue(numValue);
                                }
                                else
                                {
                                    cell.SetCellValue(value.ToString());
                                }
                            }
                        }
                    }

                    // 自动调整列宽
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        sheet.AutoSizeColumn(i);
                    }

                    // 保存文件
                    using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                    {
                        workbook.Write(stream);
                    }
                }
                finally
                {
                    workbook?.Close();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"导出Excel文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存曲线按钮点击事件
        /// </summary>
        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartBrittleness?.Series.Count == 0)
                {
                    MessageBox.Show("没有图表数据可以保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "PNG图片|*.png|JPEG图片|*.jpg|所有文件|*.*";
                    saveFileDialog.Title = "保存图表";
                    saveFileDialog.FileName = $"脆性指数曲线_{DateTime.Now:yyyyMMdd_HHmmss}.png";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        chartBrittleness.SaveImage(saveFileDialog.FileName, System.Windows.Forms.DataVisualization.Charting.ChartImageFormat.Png);
                        MessageBox.Show("图表保存成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图表时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输入框
                if (txtDensity != null) txtDensity.Text = "";
                if (txtVp != null) txtVp.Text = "";
                if (txtVs != null) txtVs.Text = "";
                if (lblCalculationResult != null) lblCalculationResult.Text = "计算结果将在此显示";

                // 清空数据表
                mechanicsData.Clear();

                // 清空图表
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                }

                MessageBox.Show("数据已重置！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 生成曲线按钮点击事件
        /// </summary>
        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("请先导入数据或进行计算！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查是否有脆性指数数据
                if (!mechanicsData.Columns.Contains("脆性指数/%"))
                {
                    MessageBox.Show("请先计算脆性指数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 更新图表
                UpdateChart();

                MessageBox.Show("脆性指数曲线已生成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
