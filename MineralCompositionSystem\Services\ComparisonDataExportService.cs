using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace MineralCompositionSystem.Services
{
    /// <summary>
    /// 对比图数据导出格式
    /// </summary>
    public enum ExportFormat
    {
        JSON,
        Excel,
        CSV,
        XML
    }

    /// <summary>
    /// 导出选项
    /// </summary>
    public class ComparisonDataExportOptions
    {
        public string OutputPath { get; set; }
        public string SystemName { get; set; }
        public bool IncludeMetadata { get; set; } = true;
        public bool CompressOutput { get; set; } = false;
        public bool OpenAfterExport { get; set; } = true;
        public string CustomFileName { get; set; }
    }

    /// <summary>
    /// 对比图数据导出服务
    /// </summary>
    public class ComparisonDataExportService
    {
        /// <summary>
        /// 导出单一格式
        /// </summary>
        public async Task<string> ExportSingleFormat(List<object> dataPoints, ComparisonDataExportOptions options,
            ExportFormat format, Action<int> progressCallback = null)
        {
            progressCallback?.Invoke(0);

            string fileName = GenerateFileName(options.SystemName, format, options.CustomFileName);
            string filePath = Path.Combine(options.OutputPath, fileName);

            progressCallback?.Invoke(20);

            switch (format)
            {
                case ExportFormat.JSON:
                    await ExportToJson(dataPoints, filePath, options);
                    break;
                case ExportFormat.Excel:
                    await ExportToExcel(dataPoints, filePath, options);
                    break;
                case ExportFormat.CSV:
                    await ExportToCsv(dataPoints, filePath, options);
                    break;
                case ExportFormat.XML:
                    await ExportToXml(dataPoints, filePath, options);
                    break;
            }

            progressCallback?.Invoke(80);

            // 压缩文件（如果需要）
            if (options.CompressOutput)
            {
                filePath = await CompressFile(filePath);
            }

            progressCallback?.Invoke(90);

            // 打开文件（如果需要）
            if (options.OpenAfterExport)
            {
                System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{filePath}\"");
            }

            progressCallback?.Invoke(100);

            return filePath;
        }

        /// <summary>
        /// 导出多种格式
        /// </summary>
        public async Task<List<string>> ExportMultipleFormats(List<object> dataPoints, ComparisonDataExportOptions options,
            List<ExportFormat> formats, Action<int> progressCallback = null)
        {
            var exportedFiles = new List<string>();
            int totalFormats = formats.Count;

            for (int i = 0; i < totalFormats; i++)
            {
                var format = formats[i];
                int baseProgress = (i * 100) / totalFormats;
                int formatProgress = 100 / totalFormats;

                var formatOptions = new ComparisonDataExportOptions
                {
                    OutputPath = options.OutputPath,
                    SystemName = options.SystemName,
                    IncludeMetadata = options.IncludeMetadata,
                    CompressOutput = options.CompressOutput,
                    OpenAfterExport = false, // 批量导出时不自动打开
                    CustomFileName = options.CustomFileName
                };

                string filePath = await ExportSingleFormat(dataPoints, formatOptions, format,
                    progress => progressCallback?.Invoke(baseProgress + (progress * formatProgress / 100)));

                exportedFiles.Add(filePath);
            }

            // 批量导出完成后打开文件夹
            if (options.OpenAfterExport && exportedFiles.Count > 0)
            {
                System.Diagnostics.Process.Start("explorer.exe", options.OutputPath);
            }

            return exportedFiles;
        }

        private async Task ExportToJson(List<object> dataPoints, string filePath, ComparisonDataExportOptions options)
        {
            var exportData = new
            {
                Metadata = options.IncludeMetadata ? new
                {
                    SystemName = options.SystemName,
                    ExportTime = DateTime.Now,
                    DataCount = dataPoints.Count,
                    ExportVersion = "1.0",
                    Description = "脆性指数对比图数据"
                } : null,
                DataPoints = dataPoints
            };

            string jsonContent = JsonConvert.SerializeObject(exportData, Newtonsoft.Json.Formatting.Indented);
            await File.WriteAllTextAsync(filePath, jsonContent, Encoding.UTF8);
        }

        private async Task ExportToExcel(List<object> dataPoints, string filePath, ComparisonDataExportOptions options)
        {
            await Task.Run(() =>
            {
                var workbook = new XSSFWorkbook();
                try
                {
                    // 创建数据工作表
                    var dataSheet = workbook.CreateSheet("对比数据");

                    // 创建标题行
                    var headerRow = dataSheet.CreateRow(0);
                    headerRow.CreateCell(0).SetCellValue("序号");
                    headerRow.CreateCell(1).SetCellValue("顶深(m)");
                    headerRow.CreateCell(2).SetCellValue("底深(m)");
                    headerRow.CreateCell(3).SetCellValue("脆性指数(%)");
                    headerRow.CreateCell(4).SetCellValue("数据来源");

                    // 设置标题行样式
                    var headerStyle = workbook.CreateCellStyle();
                    var headerFont = workbook.CreateFont();
                    headerFont.IsBold = true;
                    headerStyle.SetFont(headerFont);
                    headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightBlue.Index;
                    headerStyle.FillPattern = FillPattern.SolidForeground;

                    for (int i = 0; i < 5; i++)
                    {
                        headerRow.GetCell(i).CellStyle = headerStyle;
                    }

                    // 填充数据
                    for (int i = 0; i < dataPoints.Count; i++)
                    {
                        var item = dataPoints[i];
                        var json = JsonConvert.SerializeObject(item);
                        var data = JsonConvert.DeserializeObject<dynamic>(json);

                        var row = dataSheet.CreateRow(i + 1);
                        row.CreateCell(0).SetCellValue(i + 1);
                        row.CreateCell(1).SetCellValue((double)(data.TopDepth ?? 0));
                        row.CreateCell(2).SetCellValue((double)(data.BottomDepth ?? 0));
                        row.CreateCell(3).SetCellValue((double)(data.BrittleIndex ?? 0));
                        row.CreateCell(4).SetCellValue(options.SystemName);
                    }

                    // 自动调整列宽
                    for (int i = 0; i < 5; i++)
                    {
                        dataSheet.AutoSizeColumn(i);
                    }

                    // 创建元数据工作表（如果需要）
                    if (options.IncludeMetadata)
                    {
                        var metaSheet = workbook.CreateSheet("元数据");
                        var metaRows = new[]
                        {
                            new[] { "系统名称", options.SystemName },
                            new[] { "导出时间", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                            new[] { "数据点数量", dataPoints.Count.ToString() },
                            new[] { "导出版本", "1.0" },
                            new[] { "描述", "脆性指数对比图数据" }
                        };

                        for (int i = 0; i < metaRows.Length; i++)
                        {
                            var row = metaSheet.CreateRow(i);
                            row.CreateCell(0).SetCellValue(metaRows[i][0]);
                            row.CreateCell(1).SetCellValue(metaRows[i][1]);
                        }

                        metaSheet.AutoSizeColumn(0);
                        metaSheet.AutoSizeColumn(1);
                    }

                    // 保存文件
                    using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                    {
                        workbook.Write(fs);
                    }
                }
                finally
                {
                    workbook?.Close();
                }
            });
        }

        private async Task ExportToCsv(List<object> dataPoints, string filePath, ComparisonDataExportOptions options)
        {
            var csvContent = new StringBuilder();

            // 添加元数据注释（如果需要）
            if (options.IncludeMetadata)
            {
                csvContent.AppendLine($"# 系统名称: {options.SystemName}");
                csvContent.AppendLine($"# 导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                csvContent.AppendLine($"# 数据点数量: {dataPoints.Count}");
                csvContent.AppendLine($"# 描述: 脆性指数对比图数据");
                csvContent.AppendLine();
            }

            // 添加标题行
            csvContent.AppendLine("序号,顶深(m),底深(m),脆性指数(%),数据来源");

            // 添加数据行
            for (int i = 0; i < dataPoints.Count; i++)
            {
                var item = dataPoints[i];
                var json = JsonConvert.SerializeObject(item);
                var data = JsonConvert.DeserializeObject<dynamic>(json);

                csvContent.AppendLine($"{i + 1},{data.TopDepth ?? 0},{data.BottomDepth ?? 0},{data.BrittleIndex ?? 0},{options.SystemName}");
            }

            await File.WriteAllTextAsync(filePath, csvContent.ToString(), Encoding.UTF8);
        }

        private async Task ExportToXml(List<object> dataPoints, string filePath, ComparisonDataExportOptions options)
        {
            await Task.Run(() =>
            {
                var doc = new XmlDocument();

                // 创建根元素
                var root = doc.CreateElement("ComparisonData");
                doc.AppendChild(root);

                // 添加元数据（如果需要）
                if (options.IncludeMetadata)
                {
                    var metadata = doc.CreateElement("Metadata");

                    var systemName = doc.CreateElement("SystemName");
                    systemName.InnerText = options.SystemName;
                    metadata.AppendChild(systemName);

                    var exportTime = doc.CreateElement("ExportTime");
                    exportTime.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    metadata.AppendChild(exportTime);

                    var dataCount = doc.CreateElement("DataCount");
                    dataCount.InnerText = dataPoints.Count.ToString();
                    metadata.AppendChild(dataCount);

                    var description = doc.CreateElement("Description");
                    description.InnerText = "脆性指数对比图数据";
                    metadata.AppendChild(description);

                    root.AppendChild(metadata);
                }

                // 添加数据点
                var dataPointsElement = doc.CreateElement("DataPoints");

                for (int i = 0; i < dataPoints.Count; i++)
                {
                    var item = dataPoints[i];
                    var json = JsonConvert.SerializeObject(item);
                    var data = JsonConvert.DeserializeObject<dynamic>(json);

                    var pointElement = doc.CreateElement("DataPoint");
                    pointElement.SetAttribute("Index", (i + 1).ToString());

                    var topDepth = doc.CreateElement("TopDepth");
                    topDepth.InnerText = (data.TopDepth ?? 0).ToString();
                    pointElement.AppendChild(topDepth);

                    var bottomDepth = doc.CreateElement("BottomDepth");
                    bottomDepth.InnerText = (data.BottomDepth ?? 0).ToString();
                    pointElement.AppendChild(bottomDepth);

                    var brittleIndex = doc.CreateElement("BrittleIndex");
                    brittleIndex.InnerText = (data.BrittleIndex ?? 0).ToString();
                    pointElement.AppendChild(brittleIndex);

                    var source = doc.CreateElement("Source");
                    source.InnerText = options.SystemName;
                    pointElement.AppendChild(source);

                    dataPointsElement.AppendChild(pointElement);
                }

                root.AppendChild(dataPointsElement);

                // 保存文件
                doc.Save(filePath);
            });
        }

        private async Task<string> CompressFile(string filePath)
        {
            string compressedPath = Path.ChangeExtension(filePath, ".zip");

            await Task.Run(() =>
            {
                using (var archive = ZipFile.Open(compressedPath, ZipArchiveMode.Create))
                {
                    archive.CreateEntryFromFile(filePath, Path.GetFileName(filePath));
                }

                // 删除原文件
                File.Delete(filePath);
            });

            return compressedPath;
        }

        private string GenerateFileName(string systemName, ExportFormat format, string customFileName = null)
        {
            if (!string.IsNullOrEmpty(customFileName))
            {
                return customFileName;
            }

            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string extension = GetFileExtension(format);

            return $"{systemName}_对比数据_{timestamp}.{extension}";
        }

        private string GetFileExtension(ExportFormat format)
        {
            switch (format)
            {
                case ExportFormat.JSON: return "json";
                case ExportFormat.Excel: return "xlsx";
                case ExportFormat.CSV: return "csv";
                case ExportFormat.XML: return "xml";
                default: return "json";
            }
        }
    }
}
