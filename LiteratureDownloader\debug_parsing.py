#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试引用解析过程
详细显示每个步骤的结果
"""

import sys
import re
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.pdf_reader import PDFReader

def debug_single_reference(ref_text, ref_number):
    """调试单个引用的解析过程"""
    print(f"\n{'='*80}")
    print(f"调试引用 [{ref_number}]")
    print(f"{'='*80}")
    print(f"原始文本: {ref_text}")
    print(f"文本长度: {len(ref_text)} 字符")
    
    if len(ref_text.strip()) < 20:
        print("❌ 文本太短，跳过")
        return None
    
    citation = {
        'pattern_type': 'reference_line',
        'full_match': ref_text.strip(),
        'reference_number': ref_number,
        'authors': '',
        'title': '',
        'journal': '',
        'year': '',
        'doi': '',
        'url': ''
    }
    
    clean_text = ref_text.strip()
    print(f"\n1. 清理后文本: {clean_text}")
    
    # 1. 提取年份
    print(f"\n2. 查找年份...")
    year_patterns = [
        r'\((\d{4})\)',  # (2021)
        r'\b(\d{4})\b',  # 独立的年份
    ]
    
    for i, pattern in enumerate(year_patterns):
        year_matches = re.findall(pattern, clean_text)
        print(f"   模式 {i+1} '{pattern}': {year_matches}")
        for year in year_matches:
            if 1900 <= int(year) <= 2030:
                citation['year'] = year
                print(f"   ✓ 找到年份: {year}")
                break
        if citation['year']:
            break
    
    # 2. 提取DOI
    print(f"\n3. 查找DOI...")
    doi_patterns = [
        r'https://doi\.org/(10\.\d+/[^\s,]+)',
        r'doi:\s*(10\.\d+/[^\s,]+)',
        r'DOI:\s*(10\.\d+/[^\s,]+)',
    ]
    
    for i, pattern in enumerate(doi_patterns):
        doi_match = re.search(pattern, clean_text, re.IGNORECASE)
        print(f"   模式 {i+1} '{pattern}': {'找到' if doi_match else '未找到'}")
        if doi_match:
            citation['doi'] = doi_match.group(1)
            print(f"   ✓ 找到DOI: {citation['doi']}")
            break
    
    # 3. 提取作者
    print(f"\n4. 查找作者...")
    author_patterns = [
        r'^([A-Z]\.\s*[A-Z][a-zA-Z\-\']*(?:,\s*[A-Z]\.\s*[A-Z][a-zA-Z\-\']*)*),\s*([^,]+),',
        r'^([A-Z][a-zA-Z\-\']*(?:,\s*[A-Z][a-zA-Z\-\']*)*),\s*([^,]+),',
        r'^([^,]+),\s*([^,]+),',
    ]
    
    for i, pattern in enumerate(author_patterns):
        author_match = re.search(pattern, clean_text)
        print(f"   模式 {i+1} '{pattern}': {'找到' if author_match else '未找到'}")
        if author_match:
            potential_authors = author_match.group(1).strip()
            potential_title = author_match.group(2).strip()
            print(f"   候选作者: '{potential_authors}'")
            print(f"   候选标题: '{potential_title}'")
            
            # 验证作者格式
            if (re.search(r'[A-Z]', potential_authors) and 
                len(potential_authors) < 150 and
                not re.search(r'\d{4}', potential_authors)):
                
                citation['authors'] = potential_authors
                print(f"   ✓ 确认作者: {citation['authors']}")
                
                # 验证标题
                if (len(potential_title) > 10 and 
                    not re.match(r'^\d+$', potential_title) and
                    not re.search(r'^\d{4}$', potential_title)):
                    citation['title'] = potential_title
                    print(f"   ✓ 确认标题: {citation['title']}")
                break
    
    # 4. 如果没有找到标题，尝试其他方法
    if not citation['title']:
        print(f"\n5. 尝试其他标题提取方法...")
        title_patterns = [
            r',\s*([^,]+),\s*\d{4}',
            r'"([^"]+)"',
            r',\s*([^,]+),\s*[A-Z][^,]*\s+\d+',
        ]
        
        for i, pattern in enumerate(title_patterns):
            title_match = re.search(pattern, clean_text)
            print(f"   模式 {i+1} '{pattern}': {'找到' if title_match else '未找到'}")
            if title_match:
                title = title_match.group(1).strip()
                print(f"   候选标题: '{title}'")
                if (len(title) > 10 and len(title) < 200 and 
                    not re.match(r'^\d+$', title) and
                    not re.search(r'^\d{4}$', title)):
                    citation['title'] = title
                    print(f"   ✓ 确认标题: {citation['title']}")
                    break
    
    # 5. 提取期刊
    print(f"\n6. 查找期刊...")
    journal_patterns = [
        r',\s*([A-Z][a-zA-Z\s\.]+)\s+\d+\s*\(\d{4}\)',
        r',\s*([A-Z][a-zA-Z\s\.]+)\.\s*\d+',
        r',\s*([A-Z][a-zA-Z\s\.]+),\s*\d+',
    ]
    
    for i, pattern in enumerate(journal_patterns):
        journal_match = re.search(pattern, clean_text)
        print(f"   模式 {i+1} '{pattern}': {'找到' if journal_match else '未找到'}")
        if journal_match:
            journal = journal_match.group(1).strip()
            journal = re.sub(r'\.$', '', journal)  # 移除末尾的点
            print(f"   候选期刊: '{journal}'")
            if (len(journal) > 3 and len(journal) < 100 and 
                not re.match(r'^\d+$', journal)):
                citation['journal'] = journal
                print(f"   ✓ 确认期刊: {citation['journal']}")
                break
    
    # 6. 如果仍然没有作者，尝试更宽松的匹配
    if not citation['authors']:
        print(f"\n7. 尝试宽松的作者匹配...")
        first_part = clean_text.split(',')[0].strip()
        print(f"   第一部分: '{first_part}'")
        if (len(first_part) > 3 and len(first_part) < 100 and 
            re.search(r'[A-Z]', first_part)):
            citation['authors'] = first_part
            print(f"   ✓ 确认作者: {citation['authors']}")
    
    # 最终结果
    print(f"\n{'='*50}")
    print(f"最终解析结果:")
    print(f"作者: '{citation.get('authors', '未识别')}'")
    print(f"标题: '{citation.get('title', '未识别')}'")
    print(f"期刊: '{citation.get('journal', '未识别')}'")
    print(f"年份: '{citation.get('year', '未识别')}'")
    print(f"DOI: '{citation.get('doi', '未识别')}'")
    print(f"{'='*50}")
    
    return citation

def debug_parsing():
    """调试解析过程"""
    pdf_file = "paper.pdf"
    
    if not Path(pdf_file).exists():
        print(f"错误: 找不到文件 {pdf_file}")
        return
    
    print("开始调试引用解析过程...")
    
    # 创建PDF读取器
    pdf_reader = PDFReader()
    
    # 提取文本
    text = pdf_reader.extract_text(pdf_file)
    references_text = pdf_reader.extract_references_section(text)
    
    if not references_text:
        print("错误: 未找到References部分")
        return
    
    # 查找前3个编号引用进行详细调试
    numbered_pattern = r'\[(\d+)\]\s*(.*?)(?=\n\s*\[\d+\]|\n\s*$|$)'
    numbered_matches = list(re.finditer(numbered_pattern, references_text, re.DOTALL))
    
    print(f"找到 {len(numbered_matches)} 个编号引用")
    print(f"将详细调试前3个引用...")
    
    for i, match in enumerate(numbered_matches[:3]):
        ref_number = match.group(1)
        ref_content = match.group(2).strip()
        ref_content = re.sub(r'\s+', ' ', ref_content)  # 清理空格
        
        debug_single_reference(ref_content, ref_number)

if __name__ == "__main__":
    debug_parsing()
