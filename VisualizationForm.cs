using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using BritSystem.Models;
using BritSystem.Controls;
using BritSystem.Services;

namespace BritSystem
{
    public partial class VisualizationForm : Form
    {
        private DataTable _resultData;
        private List<string> _brittleMinerals;
        private List<string> _ductileMinerals;

        // 控件字段已在Designer.cs文件中声明

        // 堆叠柱状图控件
        private MineralStackedBarChartControl _stackedBarChart;

        // 搜索功能相关字段
        private DataTable _originalResultData; // 保存原始数据，用于还原



        // 深度数据
        private List<double> _depths;
        private int _currentDepthIndex = 0;

        // 饼状图相关字段
        private bool _isExpanded = false;
        private string _expandedType = "";
        private bool _isDispersed = false; // 是否处于分散状态
        private string _dispersedType = ""; // 分散的类型（脆性矿物或塑性矿物）
        private OriginalPointInfo _originalPointInfo = null;
        private Dictionary<string, Color> _pieColors = new Dictionary<string, Color>
        {
            ["脆性矿物"] = Color.Blue,
            ["塑性矿物"] = Color.Green,
            ["石英%"] = Color.FromArgb(255, 51, 204),
            ["碳酸盐矿物%"] = Color.FromArgb(51, 153, 255),
            ["斜长石%"] = Color.Gray,
            ["钾长石（正长石）%"] = Color.Orange,
            ["黏土矿物总量%"] = Color.FromArgb(153, 102, 51)
        };

        /// <summary>
        /// 原始点信息，用于还原分裂的饼块
        /// </summary>
        private class OriginalPointInfo
        {
            public int Index { get; set; }
            public double Value { get; set; }
            public Color Color { get; set; }
            public string LegendText { get; set; } = "";
            public string Label { get; set; } = "";
            public string Tag { get; set; } = "";
        }

        public VisualizationForm(DataTable resultData, List<string> brittleMinerals, List<string> ductileMinerals)
        {
            // 使用LoggingService确保日志能写入文件
            LoggingService.Instance.Info("===== VisualizationForm 构造函数开始 =====");

            _resultData = resultData;
            _originalResultData = resultData?.Copy(); // 保存原始数据副本
            _brittleMinerals = brittleMinerals ?? new List<string>();
            _ductileMinerals = ductileMinerals ?? new List<string>();

            LoggingService.Instance.Info($"接收到数据: 行数={_resultData?.Rows.Count ?? 0}, 脆性矿物={_brittleMinerals.Count}, 塑性矿物={_ductileMinerals.Count}");
            LoggingService.Instance.Info($"脆性矿物列表: {string.Join(", ", _brittleMinerals)}");
            LoggingService.Instance.Info($"塑性矿物列表: {string.Join(", ", _ductileMinerals)}");

            LoggingService.Instance.Info("调用InitializeComponent...");
            InitializeComponent();
            LoggingService.Instance.Info("InitializeComponent完成");

            LoggingService.Instance.Info("调用InitializeDepthData...");
            InitializeDepthData();
            LoggingService.Instance.Info("InitializeDepthData完成");

            LoggingService.Instance.Info("调用InitializeControls...");
            InitializeControls();
            LoggingService.Instance.Info("InitializeControls完成");

            LoggingService.Instance.Info("调用LoadData...");
            LoadData();
            LoggingService.Instance.Info("LoadData完成");

            LoggingService.Instance.Info("===== VisualizationForm 构造函数完成 =====");
        }

        // InitializeComponent现在在Designer.cs文件中

        private void VisualizationForm_Load(object sender, EventArgs e)
        {
            LoggingService.Instance.Info("===== VisualizationForm_Load 开始 =====");

            // 窗体加载时的初始化
            LoggingService.Instance.Info($"深度数据数量: {_depths.Count}");

            if (_depths.Count > 0)
            {
                _currentDepthIndex = 0;
                LoggingService.Instance.Info($"设置当前深度索引: {_currentDepthIndex}");

                LoggingService.Instance.Info("调用UpdateCurrentDepthLabel...");
                UpdateCurrentDepthLabel();

                LoggingService.Instance.Info("调用UpdatePieChart...");
                UpdatePieChart();
                LoggingService.Instance.Info("UpdatePieChart完成");
            }
            else
            {
                LoggingService.Instance.Info("警告: 没有深度数据");
            }

            LoggingService.Instance.Info("===== VisualizationForm_Load 完成 =====");
        }

        /// <summary>
        /// 初始化深度数据
        /// </summary>
        private void InitializeDepthData()
        {
            _depths = new List<double>();

            if (_resultData != null && _resultData.Rows.Count > 0)
            {
                try
                {
                    _depths = _resultData.Rows.Cast<DataRow>()
                        .Where(row => row["顶深/m"] != DBNull.Value)
                        .Select(row => Convert.ToDouble(row["顶深/m"]))
                        .Distinct()
                        .OrderBy(d => d)
                        .ToList();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"初始化深度数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 初始化控件（使用Designer生成的控件）
        /// </summary>
        private void InitializeControls()
        {
            // 控件已在Designer中创建，这里只需要初始化功能

            // 初始化饼状图控件
            InitializePieChartControls();

            // 初始化堆叠柱状图控件
            InitializeStackedBarChart();

            // 初始化坐标轴控制
            InitializeAxisControls();

            // 初始化搜索控件
            InitializeSearchControls();

            // 绑定事件
            trackBarDepth.ValueChanged += DepthTrackBar_ValueChanged;
        }

        /// <summary>
        /// 初始化饼状图控件（使用Designer生成的控件）
        /// </summary>
        private void InitializePieChartControls()
        {
            LoggingService.Instance.Info("===== 初始化饼状图控件 =====");

            // 设置深度滑动条的范围
            if (_depths != null && _depths.Count > 0)
            {
                trackBarDepth.Minimum = 0;
                trackBarDepth.Maximum = Math.Max(0, _depths.Count - 1);
                trackBarDepth.Value = 0;
                trackBarDepth.TickFrequency = Math.Max(1, _depths.Count / 10);
                trackBarDepth.LargeChange = Math.Max(1, _depths.Count / 10);
                trackBarDepth.SmallChange = 1;
                LoggingService.Instance.Info($"深度滑动条设置: 范围0-{_depths.Count - 1}");
            }
            else
            {
                LoggingService.Instance.Info("警告: 深度数据为空，无法设置滑动条");
            }

            // 确保饼状图正确初始化
            if (chartPie != null)
            {
                LoggingService.Instance.Info("开始初始化chartPie控件");

                // 清除现有内容
                chartPie.Series.Clear();
                chartPie.ChartAreas.Clear();
                chartPie.Legends.Clear();
                chartPie.Titles.Clear();

                // 创建ChartArea
                ChartArea chartArea = new ChartArea("PieChartArea")
                {
                    BackColor = Color.White,
                    BorderColor = Color.Gray,
                    BorderWidth = 1
                };
                chartPie.ChartAreas.Add(chartArea);
                LoggingService.Instance.Info("ChartArea已创建");

                // 创建Legend
                Legend legend = new Legend("PieLegend")
                {
                    Alignment = StringAlignment.Center,
                    Docking = Docking.Bottom,
                    Font = new Font("Microsoft YaHei", 9F, FontStyle.Regular),
                    BackColor = Color.Transparent
                };
                chartPie.Legends.Add(legend);
                LoggingService.Instance.Info("Legend已创建");

                // 创建默认Title
                Title title = new Title("脆性矿物 vs 塑性矿物比例")
                {
                    Font = new Font("Microsoft YaHei", 14F, FontStyle.Bold),
                    ForeColor = Color.Black
                };
                chartPie.Titles.Add(title);
                LoggingService.Instance.Info("Title已创建");

                // 设置图表基本属性
                chartPie.BackColor = Color.White;
                chartPie.BorderlineColor = Color.Gray;
                chartPie.BorderlineWidth = 1;
                chartPie.BorderlineDashStyle = ChartDashStyle.Solid;

                LoggingService.Instance.Info("chartPie控件初始化完成");
            }
            else
            {
                LoggingService.Instance.Info("错误: chartPie控件为null");
            }

            LoggingService.Instance.Info("===== 饼状图控件初始化完成 =====");
        }

        /// <summary>
        /// 初始化堆叠柱状图控件（使用Designer生成的面板）
        /// </summary>
        private void InitializeStackedBarChart()
        {
            _stackedBarChart = new MineralStackedBarChartControl
            {
                Dock = DockStyle.Fill
            };

            panelStackedChart.Controls.Add(_stackedBarChart);
        }

        /// <summary>
        /// 初始化坐标轴控制面板
        /// </summary>
        private void InitializeAxisControls()
        {
            Console.WriteLine("===== 初始化坐标轴控制面板 =====");
            LoggingService.Instance.Info("===== 初始化坐标轴控制面板 =====");

            // 设置默认值
            numXMin.Value = 0;
            numXMax.Value = 100;
            numXInterval.Value = 20;
            lblXUnit.Text = "矿物含量(%)";

            Console.WriteLine("X轴默认值设置完成");
            LoggingService.Instance.Info("X轴默认值设置完成");

            // 默认不启用手动设置，使用自动模式
            chkXAutoRange.Checked = false;

            // 注意：根据用户要求，不允许修改坐标轴
            // 图表控件将使用默认的自动坐标轴设置
            Console.WriteLine("坐标轴控制面板已初始化，但不会修改图表的坐标轴设置");
            LoggingService.Instance.Info("坐标轴控制面板已初始化，但不会修改图表的坐标轴设置");

            Console.WriteLine($"复选框状态: chkXAutoRange.Checked={chkXAutoRange.Checked}");
            LoggingService.Instance.Info($"复选框状态: chkXAutoRange.Checked={chkXAutoRange.Checked}");

            // 先移除可能存在的事件绑定，避免重复绑定
            chkXAutoRange.CheckedChanged -= ChkXAutoRange_CheckedChanged;

            // 绑定复选框事件
            chkXAutoRange.CheckedChanged += ChkXAutoRange_CheckedChanged;

            Console.WriteLine("事件绑定完成");
            LoggingService.Instance.Info("事件绑定完成");

            // 初始化控件状态
            UpdateXAxisControlsState();

            Console.WriteLine("===== 坐标轴控制面板初始化完成 =====");
            LoggingService.Instance.Info("===== 坐标轴控制面板初始化完成 =====");
        }



        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadData()
        {
            LoggingService.Instance.Info("===== 开始加载数据 =====");

            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                LoggingService.Instance.Info("错误: 没有数据可供可视化");
                MessageBox.Show("没有数据可供可视化", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            LoggingService.Instance.Info($"数据行数: {_resultData.Rows.Count}");
            LoggingService.Instance.Info($"深度数量: {_depths.Count}");

            // 加载堆叠柱状图数据
            _stackedBarChart.ResultData = _resultData;
            _stackedBarChart.BrittleMinerals = _brittleMinerals;
            _stackedBarChart.DuctileMinerals = _ductileMinerals;
            LoggingService.Instance.Info("堆叠柱状图数据已设置");

            // 更新深度滑动条
            if (_depths.Count > 0)
            {
                trackBarDepth.Maximum = _depths.Count - 1;
                LoggingService.Instance.Info($"深度滑动条最大值设置为: {trackBarDepth.Maximum}");

                UpdateCurrentDepthLabel();
                LoggingService.Instance.Info("开始更新饼状图...");
                UpdatePieChart();
                LoggingService.Instance.Info("饼状图更新完成");
            }
            else
            {
                LoggingService.Instance.Info("警告: 没有深度数据");
            }

            LoggingService.Instance.Info("===== 数据加载完成 =====");
        }

        /// <summary>
        /// 深度滑动条值改变事件
        /// </summary>
        private void DepthTrackBar_ValueChanged(object sender, EventArgs e)
        {
            _currentDepthIndex = trackBarDepth.Value;
            LoggingService.Instance.Info($"===== 深度滑动条值改变: {_currentDepthIndex} =====");
            UpdateCurrentDepthLabel();

            // 强制重置饼状图状态
            _isExpanded = false;
            _isDispersed = false;
            _expandedType = "";
            _dispersedType = "";
            _originalPointInfo = null;

            UpdatePieChart();
        }

        /// <summary>
        /// 更新当前深度标签
        /// </summary>
        private void UpdateCurrentDepthLabel()
        {
            if (_currentDepthIndex >= 0 && _currentDepthIndex < _depths.Count)
            {
                double currentDepth = _depths[_currentDepthIndex];
                lblCurrentDepth.Text = $"当前深度: {currentDepth:F0}m";
            }
        }

        // InitializeComponent现在在Designer.cs文件中


        /// <summary>
        /// 更新饼状图
        /// </summary>
        private void UpdatePieChart()
        {
            LoggingService.Instance.Info("===== 开始更新饼状图 =====");

            // 检查chartPie是否已初始化
            if (chartPie == null)
            {
                LoggingService.Instance.Info("错误: chartPie为null");
                return;
            }
            LoggingService.Instance.Info("chartPie已初始化");

            // 检查深度数据
            if (_depths == null || _depths.Count == 0)
            {
                LoggingService.Instance.Info("错误: 深度数据为空");
                return;
            }

            if (_currentDepthIndex < 0 || _currentDepthIndex >= _depths.Count)
            {
                LoggingService.Instance.Info($"错误: 深度索引无效 - _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                return;
            }
            LoggingService.Instance.Info($"当前深度索引: {_currentDepthIndex}");

            double currentDepth = _depths[_currentDepthIndex];
            LoggingService.Instance.Info($"当前深度: {currentDepth}m");

            // 检查数据
            if (_resultData == null)
            {
                LoggingService.Instance.Info("错误: _resultData为null");
                return;
            }
            LoggingService.Instance.Info($"结果数据行数: {_resultData.Rows.Count}");

            if (_brittleMinerals == null || _ductileMinerals == null)
            {
                LoggingService.Instance.Info($"错误: 矿物列表为null - _brittleMinerals={_brittleMinerals?.Count}, _ductileMinerals={_ductileMinerals?.Count}");
                return;
            }
            LoggingService.Instance.Info($"脆性矿物数量: {_brittleMinerals.Count}, 塑性矿物数量: {_ductileMinerals.Count}");

            // 查找当前深度的数据行
            DataRow currentRow = null;
            foreach (DataRow row in _resultData.Rows)
            {
                if (row["顶深/m"] != DBNull.Value)
                {
                    double rowDepth = Convert.ToDouble(row["顶深/m"]);
                    if (Math.Abs(rowDepth - currentDepth) < 0.1) // 允许小的误差
                    {
                        currentRow = row;
                        LoggingService.Instance.Info($"找到匹配的数据行，深度: {rowDepth}m");
                        break;
                    }
                }
            }

            if (currentRow == null)
            {
                LoggingService.Instance.Info("错误: 未找到匹配的数据行");
                return;
            }

            // 清除现有系列
            chartPie.Series.Clear();

            // 设置图表标题
            chartPie.Titles.Clear();
            Title title = new Title($"脆性矿物 vs 塑性矿物比例 (深度: {currentDepth:F0}m)")
            {
                Font = new Font("Microsoft YaHei", 14F, FontStyle.Bold),
                ForeColor = Color.Black
            };
            chartPie.Titles.Add(title);

            // 只在非分裂和非分散状态下重新创建饼状图
            if (!_isExpanded && !_isDispersed)
            {
                LoggingService.Instance.Info("创建基本饼状图（脆性/塑性矿物比例）");
                // 显示基本的脆性/塑性矿物比例
                CreateBasicPieChart(currentRow);
            }
            else
            {
                LoggingService.Instance.Info($"保持当前状态: 分裂={_isExpanded}, 分散={_isDispersed}");
            }

            // 设置图例
            if (chartPie.Legends.Count > 0)
            {
                chartPie.Legends[0].Enabled = true;
                chartPie.Legends[0].Docking = Docking.Bottom;
                LoggingService.Instance.Info("已设置饼状图图例");
            }

            // 绑定鼠标事件（避免重复绑定）
            Console.WriteLine($"[DEBUG] 绑定鼠标事件前 - chartPie对象: {chartPie?.GetHashCode()}");
            LoggingService.Instance.Info($"绑定鼠标事件前 - chartPie对象: {chartPie?.GetHashCode()}");

            chartPie.MouseMove -= ChartPie_MouseMove;
            chartPie.MouseLeave -= ChartPie_MouseLeave;
            chartPie.MouseClick -= ChartPie_MouseClick;

            chartPie.MouseMove += ChartPie_MouseMove;
            chartPie.MouseLeave += ChartPie_MouseLeave;
            chartPie.MouseClick += ChartPie_MouseClick;

            Console.WriteLine("===== 饼状图鼠标事件已绑定 =====");
            Console.WriteLine($"[DEBUG] 绑定鼠标事件后 - chartPie对象: {chartPie?.GetHashCode()}");
            LoggingService.Instance.Info("===== 饼状图鼠标事件已绑定 =====");
            LoggingService.Instance.Info($"绑定鼠标事件后 - chartPie对象: {chartPie?.GetHashCode()}");

            // 强制重绘图表
            chartPie.Invalidate();
            chartPie.Update();

            LoggingService.Instance.Info($"饼状图系列数量: {chartPie.Series.Count}");
            if (chartPie.Series.Count > 0)
            {
                LoggingService.Instance.Info($"第一个系列数据点数量: {chartPie.Series[0].Points.Count}");
            }

            LoggingService.Instance.Info("===== 饼状图更新完成 =====");
        }

        /// <summary>
        /// 创建基本饼状图（脆性/塑性矿物）
        /// </summary>
        private void CreateBasicPieChart(DataRow currentRow)
        {
            LoggingService.Instance.Info("===== 开始创建基本饼状图 =====");

            // 打印数据表的所有列名，用于调试
            LoggingService.Instance.Info("数据表列名:");
            foreach (DataColumn column in _resultData.Columns)
            {
                LoggingService.Instance.Info($"  列名: '{column.ColumnName}'");
            }

            // 计算脆性矿物总量
            double brittleTotal = 0;
            LoggingService.Instance.Info("计算脆性矿物总量:");
            foreach (string mineral in _brittleMinerals)
            {
                // 尝试多种匹配方式
                string actualColumn = FindMatchingColumn(mineral);
                if (!string.IsNullOrEmpty(actualColumn) && currentRow[actualColumn] != DBNull.Value)
                {
                    try
                    {
                        double value = Convert.ToDouble(currentRow[actualColumn]);
                        brittleTotal += value;
                        LoggingService.Instance.Info($"  {mineral} (列名: {actualColumn}): {value}%");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Info($"  {mineral}: 转换数值失败 - {ex.Message}");
                    }
                }
                else
                {
                    LoggingService.Instance.Info($"  {mineral}: 列不存在或值为空 (尝试的列名: {actualColumn})");
                }
            }
            LoggingService.Instance.Info($"脆性矿物总量: {brittleTotal}%");

            // 计算塑性矿物总量
            double ductileTotal = 0;
            LoggingService.Instance.Info("计算塑性矿物总量:");
            foreach (string mineral in _ductileMinerals)
            {
                // 尝试多种匹配方式
                string actualColumn = FindMatchingColumn(mineral);
                if (!string.IsNullOrEmpty(actualColumn) && currentRow[actualColumn] != DBNull.Value)
                {
                    try
                    {
                        double value = Convert.ToDouble(currentRow[actualColumn]);
                        ductileTotal += value;
                        LoggingService.Instance.Info($"  {mineral} (列名: {actualColumn}): {value}%");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Info($"  {mineral}: 转换数值失败 - {ex.Message}");
                    }
                }
                else
                {
                    LoggingService.Instance.Info($"  {mineral}: 列不存在或值为空 (尝试的列名: {actualColumn})");
                }
            }
            LoggingService.Instance.Info($"塑性矿物总量: {ductileTotal}%");

            // 检查是否有有效数据
            if (brittleTotal <= 0 && ductileTotal <= 0)
            {
                LoggingService.Instance.Info("警告: 没有有效的矿物数据，无法创建饼状图");

                // 创建一个提示系列
                Series emptySeries = new Series("NoData")
                {
                    ChartType = SeriesChartType.Pie,
                    IsValueShownAsLabel = true
                };

                DataPoint emptyPoint = new DataPoint(0, 100)
                {
                    LegendText = "无数据",
                    Label = "无数据",
                    Color = Color.LightGray,
                    Tag = "无数据"
                };
                emptySeries.Points.Add(emptyPoint);
                chartPie.Series.Add(emptySeries);

                LoggingService.Instance.Info("已添加无数据提示");
                return;
            }

            // 创建饼状图系列
            LoggingService.Instance.Info("创建饼状图系列...");
            Series pieSeries = new Series("MineralRatio")
            {
                ChartType = SeriesChartType.Pie,
                IsValueShownAsLabel = true,
                LabelFormat = "#.#'%'",
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Regular)
            };

            // 设置饼状图样式
            pieSeries["PieLabelStyle"] = "Outside";
            pieSeries["PieLineColor"] = "Black";
            pieSeries["PieDrawingStyle"] = "Default";

            LoggingService.Instance.Info("饼状图系列已创建");

            // 添加数据点
            int pointCount = 0;
            if (brittleTotal > 0)
            {
                DataPoint brittlePoint = new DataPoint(0, brittleTotal)
                {
                    LegendText = "脆性矿物",
                    Label = $"{brittleTotal:F1}%",
                    Color = _pieColors["脆性矿物"],
                    Tag = "脆性矿物" // 用于识别点击的类型
                };
                pieSeries.Points.Add(brittlePoint);
                pointCount++;
                LoggingService.Instance.Info($"添加脆性矿物数据点: {brittleTotal:F1}%, 颜色: {_pieColors["脆性矿物"]}");
            }

            if (ductileTotal > 0)
            {
                DataPoint ductilePoint = new DataPoint(0, ductileTotal)
                {
                    LegendText = "塑性矿物",
                    Label = $"{ductileTotal:F1}%",
                    Color = _pieColors["塑性矿物"],
                    Tag = "塑性矿物" // 用于识别点击的类型
                };
                pieSeries.Points.Add(ductilePoint);
                pointCount++;
                LoggingService.Instance.Info($"添加塑性矿物数据点: {ductileTotal:F1}%, 颜色: {_pieColors["塑性矿物"]}");
            }

            LoggingService.Instance.Info($"总共添加了 {pointCount} 个数据点");
            chartPie.Series.Add(pieSeries);
            LoggingService.Instance.Info("饼状图系列已添加到图表");

            // 确保图表可见
            chartPie.Visible = true;
            chartPie.Enabled = true;

            LoggingService.Instance.Info("===== 基本饼状图创建完成 =====");
        }



        /// <summary>
        /// 查找匹配的列名
        /// </summary>
        private string FindMatchingColumn(string mineralName)
        {
            if (string.IsNullOrEmpty(mineralName))
                return null;

            // 获取所有列名
            var columnNames = _resultData.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToList();

            // 1. 精确匹配
            var exactMatch = columnNames.FirstOrDefault(col => col.Equals(mineralName, StringComparison.OrdinalIgnoreCase));
            if (!string.IsNullOrEmpty(exactMatch))
            {
                System.Diagnostics.Debug.WriteLine($"找到精确匹配: {mineralName} -> {exactMatch}");
                return exactMatch;
            }

            // 2. 移除百分号后匹配
            string cleanMineralName = mineralName.Replace("%", "").Trim();
            var percentMatch = columnNames.FirstOrDefault(col =>
                col.Replace("%", "").Trim().Equals(cleanMineralName, StringComparison.OrdinalIgnoreCase));
            if (!string.IsNullOrEmpty(percentMatch))
            {
                System.Diagnostics.Debug.WriteLine($"找到百分号匹配: {mineralName} -> {percentMatch}");
                return percentMatch;
            }

            // 3. 包含匹配
            var containsMatch = columnNames.FirstOrDefault(col =>
                col.ToLower().Contains(cleanMineralName.ToLower()) ||
                cleanMineralName.ToLower().Contains(col.ToLower().Replace("%", "").Trim()));
            if (!string.IsNullOrEmpty(containsMatch))
            {
                System.Diagnostics.Debug.WriteLine($"找到包含匹配: {mineralName} -> {containsMatch}");
                return containsMatch;
            }

            System.Diagnostics.Debug.WriteLine($"未找到匹配的列: {mineralName}");
            return null;
        }

        /// <summary>
        /// 获取随机颜色
        /// </summary>
        private Color GetRandomColor(string seed)
        {
            Random rand = new Random(seed.GetHashCode());
            return Color.FromArgb(rand.Next(256), rand.Next(256), rand.Next(256));
        }

        #region 饼状图鼠标事件

        /// <summary>
        /// 饼状图鼠标移动事件 - 只显示浮动效果
        /// </summary>
        private void ChartPie_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                var hitTestResult = chartPie.HitTest(e.X, e.Y);

                if (hitTestResult.ChartElementType == ChartElementType.DataPoint &&
                    hitTestResult.PointIndex >= 0 &&
                    hitTestResult.Series != null)
                {
                    var dataPoint = hitTestResult.Series.Points[hitTestResult.PointIndex];

                    // 添加浮动效果 - 稍微突出显示
                    foreach (DataPoint point in hitTestResult.Series.Points)
                    {
                        point["Exploded"] = "false";
                    }
                    dataPoint["Exploded"] = "true";

                    // 改变鼠标光标
                    chartPie.Cursor = Cursors.Hand;
                }
                else
                {
                    // 移除所有浮动效果
                    if (chartPie.Series.Count > 0)
                    {
                        foreach (DataPoint point in chartPie.Series[0].Points)
                        {
                            point["Exploded"] = "false";
                        }
                    }
                    chartPie.Cursor = Cursors.Default;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"饼状图鼠标移动事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 饼状图鼠标离开事件
        /// </summary>
        private void ChartPie_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                // 移除所有浮动效果
                if (chartPie.Series.Count > 0)
                {
                    foreach (DataPoint point in chartPie.Series[0].Points)
                    {
                        point["Exploded"] = "false";
                    }
                }
                chartPie.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"饼状图鼠标离开事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 饼状图鼠标点击事件 - 左键分裂/还原，右键分散
        /// </summary>
        private void ChartPie_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                // 输出到控制台确保能看到
                Console.WriteLine("===== VisualizationForm.ChartPie_MouseClick 事件开始 =====");
                Console.WriteLine($"[DEBUG] 事件发送者: {sender?.GetType().Name}, HashCode: {sender?.GetHashCode()}");
                Console.WriteLine($"[DEBUG] chartPie对象: {chartPie?.GetType().Name}, HashCode: {chartPie?.GetHashCode()}");
                Console.WriteLine($"[DEBUG] 发送者与chartPie是否相同: {ReferenceEquals(sender, chartPie)}");
                Console.WriteLine($"鼠标位置: X={e.X}, Y={e.Y}");
                Console.WriteLine($"鼠标按键: {e.Button}");
                Console.WriteLine($"图表控件状态: Enabled={chartPie.Enabled}, Visible={chartPie.Visible}");
                Console.WriteLine($"图表系列数量: {chartPie.Series.Count}");

                // 添加LoggingService日志记录
                LoggingService.Instance.Info("===== 饼状图鼠标点击事件开始 =====");
                LoggingService.Instance.Info($"鼠标位置: X={e.X}, Y={e.Y}");
                LoggingService.Instance.Info($"鼠标按键: {e.Button}");
                LoggingService.Instance.Info($"图表控件状态: Enabled={chartPie.Enabled}, Visible={chartPie.Visible}");
                LoggingService.Instance.Info($"图表系列数量: {chartPie.Series.Count}");

                System.Diagnostics.Debug.WriteLine("===== 饼状图鼠标点击事件开始 =====");
                System.Diagnostics.Debug.WriteLine($"鼠标位置: X={e.X}, Y={e.Y}");
                System.Diagnostics.Debug.WriteLine($"鼠标按键: {e.Button}");
                System.Diagnostics.Debug.WriteLine($"图表控件状态: Enabled={chartPie.Enabled}, Visible={chartPie.Visible}");
                System.Diagnostics.Debug.WriteLine($"图表系列数量: {chartPie.Series.Count}");

                // 添加chartPie对象检查
                Console.WriteLine($"[DEBUG] chartPie对象检查: chartPie={chartPie != null}");
                LoggingService.Instance.Info($"chartPie对象检查: chartPie={chartPie != null}");
                if (chartPie == null)
                {
                    Console.WriteLine("[ERROR] chartPie对象为null，无法执行HitTest");
                    LoggingService.Instance.Error("chartPie对象为null，无法执行HitTest");
                    return;
                }

                Console.WriteLine("[DEBUG] 准备执行HitTest...");
                LoggingService.Instance.Info("准备执行HitTest...");
                var hitTestResult = chartPie.HitTest(e.X, e.Y);
                Console.WriteLine("[DEBUG] HitTest执行完成");
                LoggingService.Instance.Info("HitTest执行完成");
                Console.WriteLine($"HitTest结果: ChartElementType={hitTestResult.ChartElementType}");
                Console.WriteLine($"HitTest结果: PointIndex={hitTestResult.PointIndex}");
                Console.WriteLine($"HitTest结果: Series={hitTestResult.Series?.Name ?? "null"}");

                System.Diagnostics.Debug.WriteLine($"HitTest结果: ChartElementType={hitTestResult.ChartElementType}");
                System.Diagnostics.Debug.WriteLine($"HitTest结果: PointIndex={hitTestResult.PointIndex}");
                System.Diagnostics.Debug.WriteLine($"HitTest结果: Series={hitTestResult.Series?.Name ?? "null"}");

                LoggingService.Instance.Info($"HitTest结果: ChartElementType={hitTestResult.ChartElementType}");
                LoggingService.Instance.Info($"HitTest结果: PointIndex={hitTestResult.PointIndex}");
                LoggingService.Instance.Info($"HitTest结果: Series={hitTestResult.Series?.Name ?? "null"}");

                // 处理数据点点击
                if (hitTestResult.ChartElementType == ChartElementType.DataPoint &&
                    hitTestResult.PointIndex >= 0 &&
                    hitTestResult.Series != null)
                {
                    var dataPoint = hitTestResult.Series.Points[hitTestResult.PointIndex];
                    string pointTag = dataPoint.Tag?.ToString();
                    Console.WriteLine($"点击的数据点: Tag={pointTag}, LegendText={dataPoint.LegendText}, Value={dataPoint.YValues[0]}");
                    Console.WriteLine($"当前状态: _isExpanded={_isExpanded}, _isDispersed={_isDispersed}");

                    System.Diagnostics.Debug.WriteLine($"点击的数据点: Tag={pointTag}, LegendText={dataPoint.LegendText}, Value={dataPoint.YValues[0]}");
                    System.Diagnostics.Debug.WriteLine($"当前状态: _isExpanded={_isExpanded}, _isDispersed={_isDispersed}");

                    LoggingService.Instance.Info($"点击的数据点: Tag={pointTag}, LegendText={dataPoint.LegendText}, Value={dataPoint.YValues[0]}");
                    LoggingService.Instance.Info($"当前状态: _isExpanded={_isExpanded}, _isDispersed={_isDispersed}");

                    if (e.Button == MouseButtons.Left)
                    {
                        Console.WriteLine("处理左键点击");
                        System.Diagnostics.Debug.WriteLine("处理左键点击");
                        LoggingService.Instance.Info("处理左键点击");

                        // 左键点击 - 分裂/还原功能
                        // 检查是否点击了基本矿物类型（脆性/塑性）
                        if (pointTag == "脆性矿物" || pointTag == "塑性矿物")
                        {
                            // 检查是否已经处于分裂状态
                            if (!_isExpanded || _expandedType != pointTag)
                            {
                                Console.WriteLine($"点击了基本矿物类型: {pointTag}，开始分裂");
                                System.Diagnostics.Debug.WriteLine($"点击了基本矿物类型: {pointTag}，开始分裂");
                                LoggingService.Instance.Info($"点击了基本矿物类型: {pointTag}，开始分裂");
                                // 分裂饼块
                                SplitPieChart(pointTag, hitTestResult.PointIndex);
                            }
                            else
                            {
                                Console.WriteLine($"点击了已分裂的矿物类型: {pointTag}，开始还原");
                                System.Diagnostics.Debug.WriteLine($"点击了已分裂的矿物类型: {pointTag}，开始还原");
                                LoggingService.Instance.Info($"点击了已分裂的矿物类型: {pointTag}，开始还原");
                                // 如果已经分裂，点击同一类型则还原
                                RestorePieChart();
                            }
                        }
                        // 检查是否点击了分裂后的子矿物
                        else if (pointTag != null && pointTag.Contains("_split_"))
                        {
                            Console.WriteLine($"点击了分裂后的子矿物: {pointTag}，开始还原");
                            System.Diagnostics.Debug.WriteLine($"点击了分裂后的子矿物: {pointTag}，开始还原");
                            LoggingService.Instance.Info($"点击了分裂后的子矿物: {pointTag}，开始还原");
                            // 点击子矿物时还原整个饼图
                            RestorePieChart();
                        }
                        else
                        {
                            Console.WriteLine($"左键点击了其他类型的数据点: {pointTag}");
                            System.Diagnostics.Debug.WriteLine($"左键点击了其他类型的数据点: {pointTag}");
                            LoggingService.Instance.Info($"左键点击了其他类型的数据点: {pointTag}");
                        }
                    }
                    else if (e.Button == MouseButtons.Right)
                    {
                        Console.WriteLine("处理右键点击");
                        System.Diagnostics.Debug.WriteLine("处理右键点击");
                        LoggingService.Instance.Info("处理右键点击");

                        // 右键点击 - 新的分散功能
                        Console.WriteLine($"右键点击饼状图，点击的是: {pointTag}");
                        System.Diagnostics.Debug.WriteLine($"右键点击饼状图，点击的是: {pointTag}");
                        LoggingService.Instance.Info($"右键点击饼状图，点击的是: {pointTag}");

                        // 检查是否点击了基本矿物类型（脆性/塑性）
                        if (pointTag == "脆性矿物" || pointTag == "塑性矿物")
                        {
                            Console.WriteLine($"点击了基本矿物类型: {pointTag}，开始分散");
                            System.Diagnostics.Debug.WriteLine($"点击了基本矿物类型: {pointTag}，开始分散");
                            LoggingService.Instance.Info($"点击了基本矿物类型: {pointTag}，开始分散");
                            // 分散饼块 - 显示具体矿物占总体的比例
                            DispersePieChart(pointTag, hitTestResult.PointIndex);
                        }
                        // 检查是否点击了分散后的子矿物或其他情况
                        else if (pointTag != null && (pointTag.Contains("_dispersed_") || _isDispersed))
                        {
                            Console.WriteLine($"点击了分散后的子矿物: {pointTag}，开始还原");
                            System.Diagnostics.Debug.WriteLine($"点击了分散后的子矿物: {pointTag}，开始还原");
                            LoggingService.Instance.Info($"点击了分散后的子矿物: {pointTag}，开始还原");
                            // 还原饼块
                            RestorePieChart();
                        }
                        else
                        {
                            // 如果右键点击了其他区域，也尝试还原
                            Console.WriteLine($"右键点击了其他区域: {pointTag}，尝试还原饼状图");
                            System.Diagnostics.Debug.WriteLine($"右键点击了其他区域: {pointTag}，尝试还原饼状图");
                            if (_isDispersed || _isExpanded)
                            {
                                Console.WriteLine("当前处于分散或分裂状态，执行还原");
                                System.Diagnostics.Debug.WriteLine("当前处于分散或分裂状态，执行还原");
                                RestorePieChart();
                            }
                            else
                            {
                                Console.WriteLine("当前处于基本状态，无需还原");
                                System.Diagnostics.Debug.WriteLine("当前处于基本状态，无需还原");
                            }
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"其他鼠标按键: {e.Button}");
                    }
                }
                // 处理图例点击 - 修改颜色
                else if (hitTestResult.ChartElementType == ChartElementType.LegendItem &&
                         hitTestResult.Series != null &&
                         hitTestResult.PointIndex >= 0)
                {
                    System.Diagnostics.Debug.WriteLine("点击了图例项");
                    var dataPoint = hitTestResult.Series.Points[hitTestResult.PointIndex];
                    string legendText = dataPoint.LegendText;
                    System.Diagnostics.Debug.WriteLine($"图例文本: {legendText}");

                    // 打开颜色选择对话框
                    using (ColorDialog colorDialog = new ColorDialog())
                    {
                        colorDialog.Color = dataPoint.Color;
                        if (colorDialog.ShowDialog() == DialogResult.OK)
                        {
                            System.Diagnostics.Debug.WriteLine($"选择了新颜色: {colorDialog.Color}");
                            // 更新颜色字典
                            _pieColors[legendText] = colorDialog.Color;

                            // 重新绘制饼状图
                            UpdatePieChart();
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("取消了颜色选择");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"点击了其他图表元素: {hitTestResult.ChartElementType}");
                    LoggingService.Instance.Info($"点击了其他图表元素: {hitTestResult.ChartElementType}");
                    // 如果点击了空白区域且当前有分裂或分散状态，尝试还原
                    if (_isDispersed || _isExpanded)
                    {
                        System.Diagnostics.Debug.WriteLine("点击空白区域，当前有分裂或分散状态，执行还原");
                        LoggingService.Instance.Info("点击空白区域，当前有分裂或分散状态，执行还原");
                        RestorePieChart();
                    }
                }

                Console.WriteLine("===== 饼状图鼠标点击事件结束 =====");
                System.Diagnostics.Debug.WriteLine("===== 饼状图鼠标点击事件结束 =====");
                LoggingService.Instance.Info("===== 饼状图鼠标点击事件结束 =====");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"饼状图鼠标点击事件出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                System.Diagnostics.Debug.WriteLine($"饼状图鼠标点击事件出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                LoggingService.Instance.Error($"饼状图鼠标点击事件出错: {ex.Message}");
                LoggingService.Instance.Error($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 分裂饼状图 - 将选中的扇形分裂成具体矿物成分，显示占总体的百分比
        /// </summary>
        private void SplitPieChart(string mineralType, int pointIndex)
        {
            try
            {
                Console.WriteLine($"===== 开始分裂饼状图: {mineralType} =====");
                System.Diagnostics.Debug.WriteLine($"===== 开始分裂饼状图: {mineralType} =====");
                LoggingService.Instance.Info($"===== 开始分裂饼状图: {mineralType} =====");

                Console.WriteLine($"[DEBUG] 深度检查: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                LoggingService.Instance.Info($"深度检查: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");

                if (_currentDepthIndex < 0 || _currentDepthIndex >= _depths.Count)
                {
                    Console.WriteLine($"[ERROR] 深度索引无效，退出分裂: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                    LoggingService.Instance.Error($"深度索引无效，退出分裂: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                    return;
                }

                double currentDepth = _depths[_currentDepthIndex];
                Console.WriteLine($"当前深度: {currentDepth}m");
                System.Diagnostics.Debug.WriteLine($"当前深度: {currentDepth}m");
                LoggingService.Instance.Info($"当前深度: {currentDepth}m");

                // 查找当前深度的数据行
                DataRow currentRow = null;
                foreach (DataRow row in _resultData.Rows)
                {
                    if (row["顶深/m"] != DBNull.Value)
                    {
                        double rowDepth = Convert.ToDouble(row["顶深/m"]);
                        if (Math.Abs(rowDepth - currentDepth) < 0.1)
                        {
                            currentRow = row;
                            break;
                        }
                    }
                }

                if (currentRow == null)
                {
                    Console.WriteLine($"[ERROR] 未找到当前深度的数据行，退出分裂: 深度={currentDepth}m");
                    LoggingService.Instance.Error($"未找到当前深度的数据行，退出分裂: 深度={currentDepth}m");
                    return;
                }

                Console.WriteLine($"[DEBUG] 找到数据行，开始分裂处理");
                LoggingService.Instance.Info($"找到数据行，开始分裂处理");

                Console.WriteLine($"[DEBUG] 图表系列数量: {chartPie.Series.Count}");
                LoggingService.Instance.Info($"图表系列数量: {chartPie.Series.Count}");

                if (chartPie.Series.Count == 0)
                {
                    Console.WriteLine($"[ERROR] 图表没有系列，退出分裂");
                    LoggingService.Instance.Error($"图表没有系列，退出分裂");
                    return;
                }

                var series = chartPie.Series[0];
                Console.WriteLine($"[DEBUG] 系列数据点数量: {series.Points.Count}, 点击索引: {pointIndex}");
                LoggingService.Instance.Info($"系列数据点数量: {series.Points.Count}, 点击索引: {pointIndex}");

                if (pointIndex < 0 || pointIndex >= series.Points.Count)
                {
                    Console.WriteLine($"[ERROR] 点击索引无效，退出分裂: pointIndex={pointIndex}, Points.Count={series.Points.Count}");
                    LoggingService.Instance.Error($"点击索引无效，退出分裂: pointIndex={pointIndex}, Points.Count={series.Points.Count}");
                    return;
                }

                var originalPoint = series.Points[pointIndex];
                Console.WriteLine($"[DEBUG] 原始点信息: Tag={originalPoint.Tag}, LegendText={originalPoint.LegendText}, Value={originalPoint.YValues[0]}");
                LoggingService.Instance.Info($"原始点信息: Tag={originalPoint.Tag}, LegendText={originalPoint.LegendText}, Value={originalPoint.YValues[0]}");

                // 保存原始点的信息用于还原
                _originalPointInfo = new OriginalPointInfo
                {
                    Index = pointIndex,
                    Value = originalPoint.YValues[0],
                    Color = originalPoint.Color,
                    LegendText = originalPoint.LegendText,
                    Label = originalPoint.Label,
                    Tag = originalPoint.Tag?.ToString()
                };
                Console.WriteLine($"[DEBUG] 原始点信息已保存");
                LoggingService.Instance.Info($"原始点信息已保存");

                // 获取要分裂的矿物列表
                List<string> mineralsToSplit = mineralType == "脆性矿物" ? _brittleMinerals : _ductileMinerals;
                Console.WriteLine($"[DEBUG] 要分裂的矿物类型: {mineralType}, 矿物列表数量: {mineralsToSplit.Count}");
                LoggingService.Instance.Info($"要分裂的矿物类型: {mineralType}, 矿物列表数量: {mineralsToSplit.Count}");

                foreach (string mineral in mineralsToSplit)
                {
                    Console.WriteLine($"[DEBUG] 矿物列表包含: {mineral}");
                    LoggingService.Instance.Info($"矿物列表包含: {mineral}");
                }

                // 计算各个子矿物的值
                List<(string name, double value, Color color)> subMinerals = new List<(string, double, Color)>();
                double categoryTotal = 0; // 该类别的总值

                foreach (string mineral in mineralsToSplit)
                {
                    Console.WriteLine($"[DEBUG] 检查矿物: {mineral}");
                    LoggingService.Instance.Info($"检查矿物: {mineral}");

                    if (_resultData.Columns.Contains(mineral))
                    {
                        Console.WriteLine($"[DEBUG] 数据表包含列: {mineral}");
                        LoggingService.Instance.Info($"数据表包含列: {mineral}");

                        if (currentRow[mineral] != DBNull.Value)
                        {
                            double value = Convert.ToDouble(currentRow[mineral]);
                            Console.WriteLine($"[DEBUG] 矿物 {mineral} 的值: {value}");
                            LoggingService.Instance.Info($"矿物 {mineral} 的值: {value}");

                            if (value > 0)
                            {
                                Color color = _pieColors.ContainsKey(mineral) ? _pieColors[mineral] : GetRandomColor(mineral);
                                subMinerals.Add((mineral, value, color));
                                categoryTotal += value;
                                Console.WriteLine($"[DEBUG] 添加子矿物: {mineral} = {value}, 类别总值: {categoryTotal}");
                                LoggingService.Instance.Info($"添加子矿物: {mineral} = {value}, 类别总值: {categoryTotal}");
                            }
                            else
                            {
                                Console.WriteLine($"[DEBUG] 矿物 {mineral} 的值为0，跳过");
                                LoggingService.Instance.Info($"矿物 {mineral} 的值为0，跳过");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"[DEBUG] 矿物 {mineral} 的值为DBNull，跳过");
                            LoggingService.Instance.Info($"矿物 {mineral} 的值为DBNull，跳过");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[DEBUG] 数据表不包含列: {mineral}");
                        LoggingService.Instance.Info($"数据表不包含列: {mineral}");
                    }
                }

                Console.WriteLine($"[DEBUG] 子矿物总数: {subMinerals.Count}, 类别总值: {categoryTotal}");
                LoggingService.Instance.Info($"子矿物总数: {subMinerals.Count}, 类别总值: {categoryTotal}");

                // 调整子矿物值，使其总和等于原始点的值，保持分裂后扇形角度不变
                double adjustmentFactor = (categoryTotal > 0) ?
                    _originalPointInfo.Value / categoryTotal : 1;

                Console.WriteLine($"[DEBUG] 调整因子: {adjustmentFactor} (原始值: {_originalPointInfo.Value}, 类别总值: {categoryTotal})");
                LoggingService.Instance.Info($"调整因子: {adjustmentFactor} (原始值: {_originalPointInfo.Value}, 类别总值: {categoryTotal})");

                for (int i = 0; i < subMinerals.Count; i++)
                {
                    var subMineral = subMinerals[i];
                    double adjustedValue = subMineral.value * adjustmentFactor;
                    subMinerals[i] = (subMineral.name, adjustedValue, subMineral.color);
                    Console.WriteLine($"[DEBUG] 调整子矿物值: {subMineral.name} 从 {subMineral.value:F1}% 调整为 {adjustedValue:F1}%");
                    LoggingService.Instance.Info($"调整子矿物值: {subMineral.name} 从 {subMineral.value:F1}% 调整为 {adjustedValue:F1}%");
                }

                if (subMinerals.Count == 0)
                {
                    Console.WriteLine($"[ERROR] 没有找到有效的子矿物，退出分裂显示");
                    LoggingService.Instance.Error($"没有找到有效的子矿物，退出分裂显示");
                    return;
                }

                // 真正的分裂：在原有饼状图基础上将选中扇形分裂成多个小扇形
                Console.WriteLine($"[DEBUG] 分裂饼状图扇形: {mineralType}");
                LoggingService.Instance.Info($"分裂饼状图扇形: {mineralType}");

                // 移除原始点
                series.Points.RemoveAt(pointIndex);

                // 在相同位置插入分裂后的子矿物点
                // 关键：使用调整后的数据值，保持饼状图的其他部分不变
                for (int i = 0; i < subMinerals.Count; i++)
                {
                    var subMineral = subMinerals[i];

                    DataPoint newPoint = new DataPoint(0, subMineral.value)
                    {
                        LegendText = subMineral.name,
                        Label = $"{subMineral.name}\n{subMineral.value:F1}%", // 显示矿物名称和占总矿物的百分比
                        Color = subMineral.color,
                        Tag = $"{mineralType}_split_{subMineral.name}",
                        ["Exploded"] = "false" // 不突出显示，保持原位
                    };

                    // 为左键分裂添加点状图案，区分于右键的条纹图案
                    newPoint.BackHatchStyle = ChartHatchStyle.SmallGrid;
                    newPoint.BackSecondaryColor = Color.LightGray;

                    // 在原位置插入新的子矿物点
                    int insertIndex = pointIndex + i;
                    series.Points.Insert(insertIndex, newPoint);

                    Console.WriteLine($"[DEBUG] 插入子矿物: {subMineral.name}, 调整后值: {subMineral.value:F1}%, 在类别中占比: {(subMineral.value / _originalPointInfo.Value * 100):F1}%");
                    LoggingService.Instance.Info($"插入子矿物: {subMineral.name}, 调整后值: {subMineral.value:F1}%, 在类别中占比: {(subMineral.value / _originalPointInfo.Value * 100):F1}%");
                }

                Console.WriteLine($"[DEBUG] 设置分裂状态");
                LoggingService.Instance.Info($"设置分裂状态");
                _isExpanded = true;
                _expandedType = mineralType;

                // 更新图表标题
                chartPie.Titles.Clear();
                chartPie.Titles.Add($"矿物成分详细显示 - {mineralType}已分裂 (深度: {currentDepth:F0}m)");

                // 强制重绘图表
                Console.WriteLine($"[DEBUG] 强制重绘图表");
                LoggingService.Instance.Info($"强制重绘图表");
                chartPie.Invalidate();

                Console.WriteLine($"===== 饼状图分裂完成: {mineralType} =====");
                System.Diagnostics.Debug.WriteLine($"===== 饼状图分裂完成: {mineralType} =====");
                LoggingService.Instance.Info($"===== 饼状图分裂完成: {mineralType} =====");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分裂饼状图出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"分裂饼状图出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 还原饼状图 - 将分裂的子矿物合并回原始状态
        /// </summary>
        private void RestorePieChart()
        {
            try
            {
                Console.WriteLine("===== 开始还原饼状图 =====");
                System.Diagnostics.Debug.WriteLine("===== 开始还原饼状图 =====");

                // 如果是分散状态，直接重新创建基本饼状图
                if (_isDispersed)
                {
                    System.Diagnostics.Debug.WriteLine("从分散状态还原");

                    // 重置分散状态
                    _isDispersed = false;
                    _dispersedType = "";

                    // 重新创建基本饼状图
                    if (_currentDepthIndex >= 0 && _currentDepthIndex < _depths.Count)
                    {
                        double currentDepth = _depths[_currentDepthIndex];

                        // 查找当前深度的数据行
                        DataRow currentRow = null;
                        foreach (DataRow row in _resultData.Rows)
                        {
                            if (row["顶深/m"] != DBNull.Value)
                            {
                                double rowDepth = Convert.ToDouble(row["顶深/m"]);
                                if (Math.Abs(rowDepth - currentDepth) < 0.1)
                                {
                                    currentRow = row;
                                    break;
                                }
                            }
                        }

                        if (currentRow != null)
                        {
                            // 清除现有系列
                            chartPie.Series.Clear();

                            // 重新创建基本饼状图
                            CreateBasicPieChart(currentRow);

                            // 更新标题
                            chartPie.Titles.Clear();
                            chartPie.Titles.Add($"脆性矿物 vs 塑性矿物比例 (深度: {currentDepth:F0}m)");
                        }
                    }

                    System.Diagnostics.Debug.WriteLine("分散状态还原完成");
                    return;
                }

                // 如果是分裂状态，还原分裂的饼状图
                if (_originalPointInfo == null || !_isExpanded)
                {
                    System.Diagnostics.Debug.WriteLine("没有需要还原的分裂状态");
                    return;
                }

                var series = chartPie.Series[0];

                Console.WriteLine($"[DEBUG] 开始还原分裂状态，原始索引: {_originalPointInfo.Index}");

                // 找到并移除所有分裂的子矿物点（从后往前删除以保持索引正确）
                List<int> indicesToRemove = new List<int>();
                for (int i = 0; i < series.Points.Count; i++)
                {
                    var point = series.Points[i];
                    string pointTag = point.Tag?.ToString();
                    if (pointTag != null && pointTag.Contains($"{_expandedType}_split_"))
                    {
                        indicesToRemove.Add(i);
                        Console.WriteLine($"[DEBUG] 找到需要移除的分裂点: 索引{i}, Tag: {pointTag}");
                    }
                }

                // 从后往前删除分裂的点
                for (int i = indicesToRemove.Count - 1; i >= 0; i--)
                {
                    int indexToRemove = indicesToRemove[i];
                    Console.WriteLine($"[DEBUG] 移除分裂点: 索引{indexToRemove}");
                    series.Points.RemoveAt(indexToRemove);
                }

                // 在原位置重新插入原始点
                DataPoint restoredPoint = new DataPoint(0, _originalPointInfo.Value)
                {
                    LegendText = _originalPointInfo.LegendText,
                    Label = _originalPointInfo.Label,
                    Color = _originalPointInfo.Color,
                    Tag = _originalPointInfo.Tag,
                    ["Exploded"] = "false"
                };

                // 确保插入位置不超出范围
                int insertIndex = Math.Min(_originalPointInfo.Index, series.Points.Count);
                series.Points.Insert(insertIndex, restoredPoint);
                Console.WriteLine($"[DEBUG] 在索引{insertIndex}处插入还原点: {_originalPointInfo.LegendText}");

                // 重置状态
                _isExpanded = false;
                _expandedType = "";
                _originalPointInfo = null;

                // 更新标题
                if (_currentDepthIndex >= 0 && _currentDepthIndex < _depths.Count)
                {
                    double currentDepth = _depths[_currentDepthIndex];
                    chartPie.Titles.Clear();
                    chartPie.Titles.Add($"脆性矿物 vs 塑性矿物比例 (深度: {currentDepth:F0}m)");
                }

                // 强制重绘图表
                chartPie.Invalidate();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"还原饼状图出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"还原饼状图出错: {ex.Message}");
            }
        }



        /// <summary>
        /// 分散饼状图 - 右键点击时清除面板重绘，只保留被选中扇形的基础数据
        /// </summary>
        private void DispersePieChart(string mineralType, int clickedIndex)
        {
            try
            {
                Console.WriteLine($"===== 开始分散饼状图: {mineralType} =====");
                System.Diagnostics.Debug.WriteLine($"===== 开始分散饼状图: {mineralType} =====");
                LoggingService.Instance.Info($"===== 开始分散饼状图: {mineralType} =====");

                Console.WriteLine($"[DEBUG] 深度检查: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                LoggingService.Instance.Info($"深度检查: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");

                if (_currentDepthIndex < 0 || _currentDepthIndex >= _depths.Count)
                {
                    Console.WriteLine($"[ERROR] 深度索引无效，退出分散: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                    LoggingService.Instance.Error($"深度索引无效，退出分散: _currentDepthIndex={_currentDepthIndex}, _depths.Count={_depths.Count}");
                    return;
                }

                double currentDepth = _depths[_currentDepthIndex];
                Console.WriteLine($"当前深度: {currentDepth}m");
                LoggingService.Instance.Info($"当前深度: {currentDepth}m");

                // 查找当前深度的数据行
                DataRow currentRow = null;
                foreach (DataRow row in _resultData.Rows)
                {
                    if (row["顶深/m"] != DBNull.Value)
                    {
                        double rowDepth = Convert.ToDouble(row["顶深/m"]);
                        if (Math.Abs(rowDepth - currentDepth) < 0.1)
                        {
                            currentRow = row;
                            break;
                        }
                    }
                }

                if (currentRow == null)
                {
                    Console.WriteLine($"[ERROR] 未找到当前深度的数据行，退出分散: 深度={currentDepth}m");
                    System.Diagnostics.Debug.WriteLine("未找到当前深度的数据行");
                    LoggingService.Instance.Error($"未找到当前深度的数据行，退出分散: 深度={currentDepth}m");
                    return;
                }

                Console.WriteLine($"[DEBUG] 找到数据行，开始分散处理");
                LoggingService.Instance.Info($"找到数据行，开始分散处理");

                Console.WriteLine($"[DEBUG] 当前图表系列数量: {chartPie.Series.Count}");
                LoggingService.Instance.Info($"当前图表系列数量: {chartPie.Series.Count}");

                // 清除现有系列
                Console.WriteLine($"[DEBUG] 清除现有系列");
                LoggingService.Instance.Info($"清除现有系列");
                chartPie.Series.Clear();
                Console.WriteLine($"[DEBUG] 系列已清除，当前系列数量: {chartPie.Series.Count}");
                LoggingService.Instance.Info($"系列已清除，当前系列数量: {chartPie.Series.Count}");

                // 创建新的饼状图系列
                Console.WriteLine($"[DEBUG] 创建新的饼状图系列");
                LoggingService.Instance.Info($"创建新的饼状图系列");
                Series pieSeries = new Series("DispersedMineralRatio")
                {
                    ChartType = SeriesChartType.Pie,
                    IsValueShownAsLabel = true,
                    LabelFormat = "#.#'%'"
                };

                // 设置饼状图样式 - 右键点击使用条纹图案
                pieSeries["PieLabelStyle"] = "Outside";
                pieSeries["PieLineColor"] = "Black";
                pieSeries["PieDrawingStyle"] = "Default";
                pieSeries["PieStartAngle"] = "0";
                Console.WriteLine($"[DEBUG] 饼状图系列样式已设置（右键分散模式）");
                LoggingService.Instance.Info($"饼状图系列样式已设置（右键分散模式）");

                // 右键分散显示：只保留被选中扇形的基础数据
                Console.WriteLine($"[DEBUG] 开始分散显示，只保留选中的矿物类型: {mineralType}");
                LoggingService.Instance.Info($"开始分散显示，只保留选中的矿物类型: {mineralType}");

                // 计算选中矿物类型的总值并收集具体矿物数据
                double selectedMineralTotal = 0;
                List<string> mineralsToShow = mineralType == "脆性矿物" ? _brittleMinerals : _ductileMinerals;
                List<(string name, double value, Color color)> subMinerals = new List<(string, double, Color)>();

                foreach (string mineral in mineralsToShow)
                {
                    if (_resultData.Columns.Contains(mineral) && currentRow[mineral] != DBNull.Value)
                    {
                        double value = Convert.ToDouble(currentRow[mineral]);
                        selectedMineralTotal += value;

                        if (value > 0)
                        {
                            Color mineralColor = _pieColors.ContainsKey(mineral) ? _pieColors[mineral] : GetRandomColor(mineral);
                            subMinerals.Add((mineral, value, mineralColor));
                        }
                    }
                }

                Console.WriteLine($"[DEBUG] {mineralType}总值: {selectedMineralTotal}，具体矿物数量: {subMinerals.Count}");
                LoggingService.Instance.Info($"{mineralType}总值: {selectedMineralTotal}，具体矿物数量: {subMinerals.Count}");

                if (selectedMineralTotal > 0 && subMinerals.Count > 0)
                {
                    // 添加每种具体矿物，显示其在该类别中的百分比
                    foreach (var subMineral in subMinerals)
                    {
                        double percentageInCategory = (subMineral.value / selectedMineralTotal) * 100;

                        DataPoint mineralPoint = new DataPoint(0, percentageInCategory)
                        {
                            LegendText = subMineral.name,
                            Label = $"{subMineral.name}\n{percentageInCategory:F1}%",
                            Color = subMineral.color,
                            Tag = $"{mineralType}_dispersed_{subMineral.name}",
                            ["Exploded"] = "false"
                        };

                        // 为右键点击添加条纹图案
                        mineralPoint.BackHatchStyle = ChartHatchStyle.DiagonalCross;
                        mineralPoint.BackSecondaryColor = Color.White;

                        pieSeries.Points.Add(mineralPoint);
                        Console.WriteLine($"[DEBUG] 添加具体矿物（条纹图案）: {subMineral.name} = {percentageInCategory:F1}%");
                        LoggingService.Instance.Info($"添加具体矿物（条纹图案）: {subMineral.name} = {percentageInCategory:F1}%");
                        System.Diagnostics.Debug.WriteLine($"添加具体矿物（条纹图案）: {subMineral.name} = {percentageInCategory:F1}%");
                    }
                }
                else
                {
                    Console.WriteLine($"[DEBUG] {mineralType}总值为0，跳过");
                    LoggingService.Instance.Info($"{mineralType}总值为0，跳过");
                }

                Console.WriteLine($"[DEBUG] 添加系列到图表，系列点数: {pieSeries.Points.Count}");
                LoggingService.Instance.Info($"添加系列到图表，系列点数: {pieSeries.Points.Count}");
                chartPie.Series.Add(pieSeries);
                Console.WriteLine($"[DEBUG] 系列已添加，图表系列数量: {chartPie.Series.Count}");
                LoggingService.Instance.Info($"系列已添加，图表系列数量: {chartPie.Series.Count}");

                // 更新标题
                Console.WriteLine($"[DEBUG] 更新图表标题");
                LoggingService.Instance.Info($"更新图表标题");
                chartPie.Titles.Clear();
                chartPie.Titles.Add($"{mineralType}分类视图 (深度: {currentDepth:F0}m)");
                Console.WriteLine($"[DEBUG] 图表标题已更新: {mineralType}分类视图 (深度: {currentDepth:F0}m)");
                LoggingService.Instance.Info($"图表标题已更新: {mineralType}分类视图 (深度: {currentDepth:F0}m)");

                // 标记为已分散状态
                Console.WriteLine($"[DEBUG] 设置分散状态");
                LoggingService.Instance.Info($"设置分散状态");
                _isDispersed = true;
                _dispersedType = mineralType;

                Console.WriteLine($"===== 饼状图分散完成: {mineralType} =====");
                System.Diagnostics.Debug.WriteLine($"===== 饼状图分散完成: {mineralType} =====");
                LoggingService.Instance.Info($"===== 饼状图分散完成: {mineralType} =====");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分散饼状图时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"分散饼状图时出错: {ex.Message}");
                MessageBox.Show($"分散饼状图时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 坐标轴控制事件

        /// <summary>
        /// X轴手动设置复选框状态改变事件
        /// </summary>
        private void ChkXAutoRange_CheckedChanged(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"===== X轴复选框状态改变: {chkXAutoRange.Checked} =====");
            UpdateXAxisControlsState();
        }



        /// <summary>
        /// 更新X轴控件状态
        /// </summary>
        private void UpdateXAxisControlsState()
        {
            bool enabled = chkXAutoRange.Checked;
            System.Diagnostics.Debug.WriteLine($"更新X轴控件状态: enabled={enabled}");

            numXMin.Enabled = enabled;
            numXMax.Enabled = enabled;
            numXInterval.Enabled = enabled;
            lblXMin.Enabled = enabled;
            lblXMax.Enabled = enabled;
            lblXInterval.Enabled = enabled;

            System.Diagnostics.Debug.WriteLine($"X轴控件状态更新完成: numXMin.Enabled={numXMin.Enabled}");
        }



        /// <summary>
        /// 应用设置按钮点击事件
        /// </summary>
        private void BtnApplySettings_Click(object sender, EventArgs e)
        {
            try
            {
                Console.WriteLine("===== 应用坐标轴设置 =====");
                System.Diagnostics.Debug.WriteLine("===== 应用坐标轴设置 =====");

                if (_stackedBarChart == null)
                {
                    Console.WriteLine("错误: _stackedBarChart为null");
                    System.Diagnostics.Debug.WriteLine("错误: _stackedBarChart为null");
                    MessageBox.Show("图表控件未初始化！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 重要说明：由于使用StackedBar（水平堆叠条形图），坐标轴含义如下：
                // - 图表的X轴 = 深度分类轴（显示深度值）
                // - 图表的Y轴 = 矿物含量数值轴（显示0-100%）
                // - 界面上的"X轴设置"应该对应矿物含量（0-100%）
                // - 界面上的"Y轴设置"应该对应深度范围
                //
                // 注意：由于StackedBar的特殊性，界面设置与图表轴的映射需要交换

                // 获取界面上的X轴设置（应该控制矿物含量 - 对应图表的Y轴）
                bool manualXAxis = chkXAutoRange.Checked;
                double xMin = (double)numXMin.Value;
                double xMax = (double)numXMax.Value;
                double xInterval = (double)numXInterval.Value;
                string xTitle = lblXUnit.Text;

                Console.WriteLine($"界面X轴设置(矿物含量): 手动={manualXAxis}, 范围={xMin}-{xMax}, 间隔={xInterval}, 标题={xTitle}");
                System.Diagnostics.Debug.WriteLine($"界面X轴设置(矿物含量): 手动={manualXAxis}, 范围={xMin}-{xMax}, 间隔={xInterval}, 标题={xTitle}");

                // 只使用X轴设置来控制矿物含量轴

                // 应用界面X轴设置到图表Y轴（矿物含量轴）
                if (manualXAxis)
                {
                    _stackedBarChart.ManualYAxisEnabled = true;
                    _stackedBarChart.YAxisMinimum = xMin;
                    _stackedBarChart.YAxisMaximum = xMax;
                    _stackedBarChart.YAxisInterval = xInterval;
                    _stackedBarChart.YAxisTitle = xTitle;
                    Console.WriteLine("界面X轴设置已应用到图表Y轴（矿物含量轴）");
                    System.Diagnostics.Debug.WriteLine("界面X轴设置已应用到图表Y轴（矿物含量轴）");
                }
                else
                {
                    _stackedBarChart.ManualYAxisEnabled = false;
                    Console.WriteLine("图表Y轴（矿物含量轴）设置为自动模式");
                    System.Diagnostics.Debug.WriteLine("图表Y轴（矿物含量轴）设置为自动模式");
                }

                // 深度轴保持自动模式
                _stackedBarChart.ManualXAxisEnabled = false;
                Console.WriteLine("图表X轴（深度轴）保持自动模式");
                System.Diagnostics.Debug.WriteLine("图表X轴（深度轴）保持自动模式");

                // 刷新图表
                _stackedBarChart.RefreshChart();
                Console.WriteLine("图表已刷新");
                System.Diagnostics.Debug.WriteLine("图表已刷新");

                MessageBox.Show("坐标轴设置已应用！", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Console.WriteLine("===== 坐标轴设置应用完成 =====");
                System.Diagnostics.Debug.WriteLine("===== 坐标轴设置应用完成 =====");
            }
            catch (Exception ex)
            {
                string errorMsg = $"应用坐标轴设置时出错: {ex.Message}";
                Console.WriteLine(errorMsg);
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                System.Diagnostics.Debug.WriteLine(errorMsg);
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show(errorMsg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置设置按钮点击事件
        /// </summary>
        private void BtnResetSettings_Click(object sender, EventArgs e)
        {
            try
            {
                Console.WriteLine("===== 重置坐标轴设置 =====");
                System.Diagnostics.Debug.WriteLine("===== 重置坐标轴设置 =====");

                // 重置X轴设置
                chkXAutoRange.Checked = false;
                numXMin.Value = 0;
                numXMax.Value = 100;
                numXInterval.Value = 20;

                Console.WriteLine("X轴重置为默认值");
                System.Diagnostics.Debug.WriteLine("X轴重置为默认值");

                // 如果图表存在，重置为自动模式
                if (_stackedBarChart != null)
                {
                    _stackedBarChart.ManualXAxisEnabled = false;
                    _stackedBarChart.ManualYAxisEnabled = false;
                    _stackedBarChart.RefreshChart();
                    Console.WriteLine("图表已重置为自动模式");
                    System.Diagnostics.Debug.WriteLine("图表已重置为自动模式");
                }

                MessageBox.Show("坐标轴设置已重置！", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Console.WriteLine("===== 坐标轴设置重置完成 =====");
                System.Diagnostics.Debug.WriteLine("===== 坐标轴设置重置完成 =====");
            }
            catch (Exception ex)
            {
                string errorMsg = $"重置坐标轴设置时出错: {ex.Message}";
                System.Diagnostics.Debug.WriteLine(errorMsg);
                MessageBox.Show(errorMsg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 搜索功能

        /// <summary>
        /// 初始化搜索控件
        /// </summary>
        private void InitializeSearchControls()
        {
            try
            {
                // 更新搜索下拉框中的列名
                UpdateSearchColumnComboBox();
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"初始化搜索控件时出错: {ex.Message}");
                MessageBox.Show($"初始化搜索控件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新搜索下拉框中的列名
        /// </summary>
        private void UpdateSearchColumnComboBox()
        {
            try
            {
                // 清空下拉框
                cboSearchColumn.Items.Clear();

                // 如果数据表为空，则返回
                if (_resultData == null || _resultData.Columns.Count == 0)
                    return;

                // 添加所有列名到下拉框
                foreach (DataColumn column in _resultData.Columns)
                {
                    cboSearchColumn.Items.Add(column.ColumnName);
                }

                // 如果有列，则默认选择第一列
                if (cboSearchColumn.Items.Count > 0)
                    cboSearchColumn.SelectedIndex = 0;

                LoggingService.Instance.Info($"已更新搜索下拉框，共 {cboSearchColumn.Items.Count} 个列");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新搜索下拉框时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (cboSearchColumn.SelectedItem == null)
                {
                    MessageBox.Show("请选择要搜索的列", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtMinValue.Text) || string.IsNullOrWhiteSpace(txtMaxValue.Text))
                {
                    MessageBox.Show("请输入最小值和最大值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 尝试解析最小值和最大值
                if (!double.TryParse(txtMinValue.Text, out double minValue) || !double.TryParse(txtMaxValue.Text, out double maxValue))
                {
                    MessageBox.Show("请输入有效的数值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确保最小值小于等于最大值
                if (minValue > maxValue)
                {
                    MessageBox.Show("最小值不能大于最大值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取选择的列名
                string columnName = cboSearchColumn.SelectedItem.ToString();

                // 创建筛选后的数据表
                DataTable filteredData = _resultData.Clone();

                // 根据条件筛选数据
                foreach (DataRow row in _resultData.Rows)
                {
                    // 跳过空值
                    if (row[columnName] == DBNull.Value)
                        continue;

                    // 尝试将值转换为double
                    if (double.TryParse(row[columnName].ToString(), out double value))
                    {
                        // 如果值在范围内，则添加到筛选后的数据表
                        if (value >= minValue && value <= maxValue)
                        {
                            filteredData.ImportRow(row);
                        }
                    }
                }

                // 如果没有符合条件的数据
                if (filteredData.Rows.Count == 0)
                {
                    MessageBox.Show("没有找到符合条件的数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 更新数据
                _resultData = filteredData;

                // 重新初始化深度数据
                InitializeDepthData();

                // 重新加载数据
                LoadData();

                // 显示搜索结果
                MessageBox.Show($"找到 {filteredData.Rows.Count} 条符合条件的数据", "搜索结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置数据按钮点击事件
        /// </summary>
        private void BtnResetData_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果原始数据为空，则返回
                if (_originalResultData == null || _originalResultData.Rows.Count == 0)
                {
                    MessageBox.Show("没有原始数据可还原", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 还原数据
                _resultData = _originalResultData.Copy();

                // 重新初始化深度数据
                InitializeDepthData();

                // 重新加载数据
                LoadData();

                // 清空搜索条件
                txtMinValue.Text = "";
                txtMaxValue.Text = "";

                // 显示还原成功消息
                MessageBox.Show("已还原原始数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"还原数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
