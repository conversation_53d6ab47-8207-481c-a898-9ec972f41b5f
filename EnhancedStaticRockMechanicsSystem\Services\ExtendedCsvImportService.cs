using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EnhancedStaticRockMechanicsSystem.Models;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 扩展的CSV导入服务
    /// </summary>
    public class ExtendedCsvImportService
    {
        /// <summary>
        /// 从CSV文件导入对比数据
        /// </summary>
        public async Task<ComparisonDataSet> ImportComparisonDataFromCsv(string filePath)
        {
            try
            {
                var fileInfo = ComparisonFileParser.ParseFileName(filePath);
                var dataSet = new ComparisonDataSet
                {
                    SystemName = fileInfo.SystemName,
                    DataSource = filePath,
                    ImportTime = DateTime.Now,
                    DataPoints = new List<BrittlenessDataPoint>()
                };

                using (var reader = new StreamReader(filePath, Encoding.UTF8))
                {
                    // 读取并解析CSV内容
                    var csvData = await ParseCsvContent(reader);
                    
                    // 智能列映射
                    var columnMapping = DetectColumnMapping(csvData.Headers);
                    
                    // 转换数据
                    foreach (var row in csvData.DataRows)
                    {
                        var dataPoint = ConvertRowToDataPoint(row, columnMapping, csvData.Headers);
                        if (dataPoint != null)
                        {
                            dataSet.DataPoints.Add(dataPoint);
                        }
                    }
                }

                LoggingService.Instance.Info($"成功从CSV导入 {dataSet.DataPoints.Count} 个数据点");
                return dataSet;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"CSV导入失败: {ex.Message}");
                throw;
            }
        }

        private async Task<CsvData> ParseCsvContent(StreamReader reader)
        {
            var csvData = new CsvData();
            var lines = new List<string>();
            
            string line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                // 跳过注释行和空行
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                    continue;
                    
                lines.Add(line);
            }

            if (lines.Count == 0)
                throw new InvalidDataException("CSV文件为空或只包含注释");

            // 解析表头
            csvData.Headers = ParseCsvLine(lines[0]);
            
            // 解析数据行
            for (int i = 1; i < lines.Count; i++)
            {
                var values = ParseCsvLine(lines[i]);
                if (values.Length == csvData.Headers.Length)
                {
                    csvData.DataRows.Add(values);
                }
            }

            return csvData;
        }

        private string[] ParseCsvLine(string line)
        {
            // 支持带引号的CSV解析
            var values = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;
            
            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];
                
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    values.Add(current.ToString().Trim());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }
            
            values.Add(current.ToString().Trim());
            return values.ToArray();
        }

        private ColumnMapping DetectColumnMapping(string[] headers)
        {
            var mapping = new ColumnMapping();
            
            for (int i = 0; i < headers.Length; i++)
            {
                string header = headers[i].ToLower().Trim();
                
                // 深度相关列
                if (header.Contains("深度") || header.Contains("depth"))
                {
                    if (header.Contains("顶") || header.Contains("top") || header.Contains("起"))
                        mapping.TopDepthIndex = i;
                    else if (header.Contains("底") || header.Contains("bottom") || header.Contains("止"))
                        mapping.BottomDepthIndex = i;
                    else
                        mapping.DepthIndex = i; // 单一深度列
                }
                
                // 脆性指数相关列
                if (header.Contains("脆性") || header.Contains("brittle") || 
                    header.Contains("指数") || header.Contains("index"))
                {
                    mapping.BrittlenessIndex = i;
                }
                
                // 系统名称列
                if (header.Contains("系统") || header.Contains("方法") || 
                    header.Contains("来源") || header.Contains("source"))
                {
                    mapping.SystemNameIndex = i;
                }
            }

            return mapping;
        }

        private BrittlenessDataPoint ConvertRowToDataPoint(string[] values, ColumnMapping mapping, string[] headers)
        {
            try
            {
                var dataPoint = new BrittlenessDataPoint();
                
                // 解析深度
                if (mapping.TopDepthIndex >= 0 && mapping.BottomDepthIndex >= 0)
                {
                    dataPoint.TopDepth = ParseDouble(values[mapping.TopDepthIndex]);
                    dataPoint.BottomDepth = ParseDouble(values[mapping.BottomDepthIndex]);
                }
                else if (mapping.DepthIndex >= 0)
                {
                    var depth = ParseDouble(values[mapping.DepthIndex]);
                    dataPoint.TopDepth = depth;
                    dataPoint.BottomDepth = depth;
                }
                
                // 解析脆性指数
                if (mapping.BrittlenessIndex >= 0)
                {
                    dataPoint.BrittleIndex = ParseDouble(values[mapping.BrittlenessIndex]);
                }
                
                // 验证数据有效性
                if (dataPoint.IsValid())
                {
                    dataPoint.GenerateGeoID();
                    return dataPoint;
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"转换数据行失败: {string.Join(",", values)}, 错误: {ex.Message}");
            }
            
            return null;
        }

        private double ParseDouble(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return 0;
                
            // 移除可能的单位和特殊字符
            value = value.Trim().Replace("m", "").Replace("%", "").Replace("，", "");
            
            if (double.TryParse(value, out double result))
                return result;
                
            return 0;
        }
    }

    /// <summary>
    /// CSV数据结构
    /// </summary>
    public class CsvData
    {
        public string[] Headers { get; set; }
        public List<string[]> DataRows { get; set; } = new List<string[]>();
    }

    /// <summary>
    /// 列映射结构
    /// </summary>
    public class ColumnMapping
    {
        public int TopDepthIndex { get; set; } = -1;
        public int BottomDepthIndex { get; set; } = -1;
        public int DepthIndex { get; set; } = -1;
        public int BrittlenessIndex { get; set; } = -1;
        public int SystemNameIndex { get; set; } = -1;
    }
}
