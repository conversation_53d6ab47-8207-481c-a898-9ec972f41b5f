#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI界面
功能：
1. 文件选择和PDF识别
2. 截图识别
3. 搜索结果显示
4. 下载管理
5. 设置配置
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
from typing import List, Dict
from datetime import datetime
from loguru import logger

try:
    from modules.pdf_reader import PDFReader
    from modules.ocr_reader import OCRReader
    from modules.citation_parser import CitationParser
    from modules.web_searcher import WebSearcher
    from modules.wos_searcher import WOSSearcher
    from modules.browser_helper import BrowserHelper
    from modules.auto_downloader import AutoDownloader
    from modules.downloader import Downloader
    from modules.matcher import LiteratureMatcher
    from modules.file_manager import FileManager
    from modules.config_manager import config_manager
    from config.settings import get_config, GUI_CONFIG
except ImportError as e:
    logger.error(f"GUI模块导入失败: {e}")
    raise


class MainWindow:
    """主窗口类"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_modules()
        self.create_widgets()
        self.setup_layout()
        
        # 存储识别结果
        self.citations = []
        self.search_results = []
        self.download_tasks = []
    
    def setup_window(self):
        """设置窗口属性"""
        config = GUI_CONFIG
        
        self.root.title("文献识别下载自动化程序")
        self.root.geometry(config['window_size'])
        self.root.resizable(True, True)
        
        # 设置字体
        self.font_family = config['font_family']
        self.font_size = config['font_size']
    
    def setup_variables(self):
        """设置变量"""
        # 从配置管理器加载设置
        self.download_dir = tk.StringVar(value=config_manager.get_download_dir())
        self.search_url = tk.StringVar(value="https://scholar.google.com/scholar?q=")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")

        # 浏览器设置变量
        browser_config = config_manager.get_browser_config()
        self.custom_browser_path = tk.StringVar(value=browser_config.get('custom_browser_path', ''))
        self.use_custom_browser = tk.BooleanVar(value=browser_config.get('use_custom_browser', False))
    
    def setup_modules(self):
        """初始化功能模块"""
        try:
            logger.info("开始初始化模块...")

            logger.info("初始化PDFReader...")
            self.pdf_reader = PDFReader()
            logger.info("PDFReader初始化完成")

            logger.info("初始化OCRReader...")
            self.ocr_reader = OCRReader()
            logger.info("OCRReader初始化完成")

            logger.info("初始化CitationParser...")
            self.citation_parser = CitationParser()
            logger.info("CitationParser初始化完成")

            logger.info("初始化WebSearcher...")
            self.web_searcher = WebSearcher()
            logger.info("WebSearcher初始化完成")

            logger.info("初始化BrowserHelper...")
            self.browser_helper = BrowserHelper()
            logger.info("BrowserHelper初始化完成")

            logger.info("初始化WOSSearcher...")
            self.wos_searcher = WOSSearcher()
            logger.info("WOSSearcher初始化完成")

            logger.info("初始化Downloader...")
            self.downloader = Downloader()
            logger.info("Downloader初始化完成")

            logger.info("初始化LiteratureMatcher...")
            self.matcher = LiteratureMatcher()
            logger.info("LiteratureMatcher初始化完成")

            logger.info("初始化FileManager...")
            self.file_manager = FileManager()
            logger.info("FileManager初始化完成")

            logger.info("所有模块初始化完成")
        except Exception as e:
            logger.error(f"模块初始化失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"模块初始化失败: {e}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 文献识别选项卡
        self.recognition_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.recognition_frame, text="文献识别")
        
        # 搜索下载选项卡
        self.download_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.download_frame, text="搜索下载")
        
        # 设置选项卡
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="设置")
        
        # 创建各选项卡内容
        self.create_recognition_widgets()
        self.create_download_widgets()
        self.create_settings_widgets()
        
        # 状态栏
        self.create_status_bar()
    
    def create_recognition_widgets(self):
        """创建文献识别界面"""
        # PDF识别区域
        pdf_group = ttk.LabelFrame(self.recognition_frame, text="PDF文件识别")
        
        ttk.Button(pdf_group, text="选择PDF文件", 
                  command=self.select_pdf_file).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(pdf_group, text="识别引用", 
                  command=self.recognize_pdf_citations).pack(side=tk.LEFT, padx=5)
        
        # 截图识别区域
        screenshot_group = ttk.LabelFrame(self.recognition_frame, text="截图识别")
        
        ttk.Button(screenshot_group, text="选择截图文件", 
                  command=self.select_screenshot_file).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(screenshot_group, text="识别引用", 
                  command=self.recognize_screenshot_citations).pack(side=tk.LEFT, padx=5)
        
        # 识别结果显示
        result_group = ttk.LabelFrame(self.recognition_frame, text="识别结果")
        
        # 创建表格显示识别结果
        columns = ('标题', '作者', '年份', '期刊', '置信度')
        self.citation_tree = ttk.Treeview(result_group, columns=columns, show='headings', height=10)

        # 设置列宽度
        column_widths = {'标题': 300, '作者': 200, '年份': 80, '期刊': 200, '置信度': 80}
        for col in columns:
            self.citation_tree.heading(col, text=col)
            self.citation_tree.column(col, width=column_widths[col], minwidth=50)
        
        # 滚动条
        citation_scrollbar = ttk.Scrollbar(result_group, orient=tk.VERTICAL, 
                                         command=self.citation_tree.yview)
        self.citation_tree.configure(yscrollcommand=citation_scrollbar.set)
        
        # 布局
        pdf_group.pack(fill=tk.X, padx=10, pady=5)
        screenshot_group.pack(fill=tk.X, padx=10, pady=5)
        result_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.citation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        citation_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_download_widgets(self):
        """创建搜索下载界面"""
        # Web of Science 搜索设置
        wos_group = ttk.LabelFrame(self.download_frame, text="Web of Science 搜索")

        # 搜索方式选择
        search_type_frame = ttk.Frame(wos_group)
        ttk.Label(search_type_frame, text="搜索方式:").pack(side=tk.LEFT, padx=5)

        self.search_type = tk.StringVar(value="selected")
        ttk.Radiobutton(search_type_frame, text="搜索选中文献", variable=self.search_type,
                       value="selected").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(search_type_frame, text="手动搜索", variable=self.search_type,
                       value="manual").pack(side=tk.LEFT, padx=5)

        search_type_frame.pack(fill=tk.X, padx=5, pady=2)

        # 手动搜索输入
        manual_frame = ttk.Frame(wos_group)
        ttk.Label(manual_frame, text="标题/关键词:").pack(side=tk.LEFT, padx=5)
        self.manual_search_entry = ttk.Entry(manual_frame, width=30)
        self.manual_search_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(manual_frame, text="作者:").pack(side=tk.LEFT, padx=5)
        self.author_search_entry = ttk.Entry(manual_frame, width=20)
        self.author_search_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(manual_frame, text="年份:").pack(side=tk.LEFT, padx=5)
        self.year_search_entry = ttk.Entry(manual_frame, width=8)
        self.year_search_entry.pack(side=tk.LEFT, padx=5)

        manual_frame.pack(fill=tk.X, padx=5, pady=2)

        # 搜索按钮
        button_frame = ttk.Frame(wos_group)
        ttk.Button(button_frame, text="智能搜索助手",
                  command=self.smart_search_helper).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="简单自动下载",
                  command=self.auto_download_citations).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="打开搜索页面",
                  command=self.open_search_pages).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空结果",
                  command=self.clear_search_results).pack(side=tk.LEFT, padx=5)

        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 已识别文献列表
        citation_group = ttk.LabelFrame(self.download_frame, text="已识别文献列表")

        # 选择控制按钮
        select_frame = ttk.Frame(citation_group)
        ttk.Button(select_frame, text="全选", command=self.select_all_citations).pack(side=tk.LEFT, padx=5)
        ttk.Button(select_frame, text="全不选", command=self.deselect_all_citations).pack(side=tk.LEFT, padx=5)
        ttk.Button(select_frame, text="反选", command=self.invert_citation_selection).pack(side=tk.LEFT, padx=5)

        # 显示选中数量
        self.selected_count_var = tk.StringVar(value="已选择: 0 / 0")
        ttk.Label(select_frame, textvariable=self.selected_count_var).pack(side=tk.RIGHT, padx=5)

        select_frame.pack(fill=tk.X, padx=5, pady=2)

        # 文献列表（带选择框）
        columns = ('选择', '编号', '标题', '作者', '年份', '期刊', '置信度')
        self.download_citation_tree = ttk.Treeview(citation_group, columns=columns, show='headings', height=10)

        # 设置列宽
        self.download_citation_tree.column('选择', width=50)
        self.download_citation_tree.column('编号', width=50)
        self.download_citation_tree.column('标题', width=300)
        self.download_citation_tree.column('作者', width=150)
        self.download_citation_tree.column('年份', width=60)
        self.download_citation_tree.column('期刊', width=150)
        self.download_citation_tree.column('置信度', width=80)

        for col in columns:
            self.download_citation_tree.heading(col, text=col)

        # 绑定点击事件来切换选择状态
        self.download_citation_tree.bind('<Button-1>', self.on_citation_click)
        self.download_citation_tree.bind('<Double-Button-1>', self.on_citation_double_click)

        download_citation_scrollbar = ttk.Scrollbar(citation_group, orient=tk.VERTICAL,
                                                  command=self.download_citation_tree.yview)
        self.download_citation_tree.configure(yscrollcommand=download_citation_scrollbar.set)

        # 搜索结果
        result_group = ttk.LabelFrame(self.download_frame, text="Web of Science 搜索结果")

        result_columns = ('标题', '作者', '年份', '期刊', 'DOI', '引用次数')
        self.search_tree = ttk.Treeview(result_group, columns=result_columns, show='headings', height=8)

        for col in result_columns:
            self.search_tree.heading(col, text=col)
            self.search_tree.column(col, width=120)

        search_scrollbar = ttk.Scrollbar(result_group, orient=tk.VERTICAL,
                                       command=self.search_tree.yview)
        self.search_tree.configure(yscrollcommand=search_scrollbar.set)
        
        # 下载控制
        download_group = ttk.LabelFrame(self.download_frame, text="下载管理")
        
        ttk.Button(download_group, text="下载选中文献", 
                  command=self.download_selected_papers).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(download_group, text="批量下载", 
                  command=self.batch_download_papers).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(download_group, variable=self.progress_var, 
                                          maximum=100)
        self.progress_bar.pack(side=tk.RIGHT, padx=5, fill=tk.X, expand=True)
        
        # 布局
        wos_group.pack(fill=tk.X, padx=10, pady=5)
        citation_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        result_group.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        download_group.pack(fill=tk.X, padx=10, pady=5)

        # 文献列表布局
        self.download_citation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        download_citation_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 搜索结果布局
        self.search_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        search_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_settings_widgets(self):
        """创建设置界面"""
        # 下载目录设置
        dir_group = ttk.LabelFrame(self.settings_frame, text="下载设置")

        ttk.Label(dir_group, text="下载目录:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(dir_group, textvariable=self.download_dir, width=40).pack(side=tk.LEFT, padx=5)
        ttk.Button(dir_group, text="浏览",
                  command=self.select_download_dir).pack(side=tk.LEFT, padx=5)

        dir_group.pack(fill=tk.X, padx=5, pady=5)

        # 浏览器设置
        browser_group = ttk.LabelFrame(self.settings_frame, text="浏览器设置")

        # 使用自定义浏览器复选框
        ttk.Checkbutton(browser_group, text="使用自定义浏览器路径",
                       variable=self.use_custom_browser,
                       command=lambda: self.toggle_custom_browser(log_change=True)).pack(anchor=tk.W, padx=5, pady=2)

        # 浏览器路径设置
        browser_path_frame = ttk.Frame(browser_group)
        ttk.Label(browser_path_frame, text="浏览器路径:").pack(side=tk.LEFT, padx=5)
        self.browser_path_entry = ttk.Entry(browser_path_frame, textvariable=self.custom_browser_path, width=50)
        self.browser_path_entry.pack(side=tk.LEFT, padx=5)
        self.browser_browse_btn = ttk.Button(browser_path_frame, text="浏览",
                                           command=self.select_browser_path)
        self.browser_browse_btn.pack(side=tk.LEFT, padx=5)
        browser_path_frame.pack(fill=tk.X, padx=5, pady=2)

        # 说明文字
        help_text = "提示：选择Chrome浏览器的exe文件，如 chrome.exe 或浏览器快捷方式"
        ttk.Label(browser_group, text=help_text, foreground="gray").pack(anchor=tk.W, padx=5, pady=2)

        browser_group.pack(fill=tk.X, padx=5, pady=5)

        # 日志显示
        log_group = ttk.LabelFrame(self.settings_frame, text="运行日志")

        self.log_text = scrolledtext.ScrolledText(log_group, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 布局
        log_group.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 初始化浏览器设置状态（在log_text创建之后）
        self.toggle_custom_browser(log_change=False)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.main_frame)
        
        ttk.Label(self.status_bar, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.notebook.pack(fill=tk.BOTH, expand=True)
    
    def select_pdf_file(self):
        """选择PDF文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.current_pdf_file = file_path
            self.status_var.set(f"已选择PDF文件: {Path(file_path).name}")
            self.log_message(f"选择PDF文件: {file_path}")
    
    def select_screenshot_file(self):
        """选择截图文件"""
        file_path = filedialog.askopenfilename(
            title="选择截图文件",
            filetypes=[
                ("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.current_screenshot_file = file_path
            self.status_var.set(f"已选择截图文件: {Path(file_path).name}")
            self.log_message(f"选择截图文件: {file_path}")
    
    def recognize_pdf_citations(self):
        """识别PDF中的文献引用"""
        if not hasattr(self, 'current_pdf_file'):
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        try:
            self.status_var.set("正在识别PDF中的文献引用...")
            self.root.update()

            # 提取引用
            citations = self.pdf_reader.extract_citations_from_pdf(self.current_pdf_file)

            # 解析引用
            parsed_citations = self.citation_parser.parse_multiple_citations(citations)

            self.citations = parsed_citations
            self.update_citation_display()
            self.update_download_citation_display()  # 同时更新搜索页面

            self.status_var.set(f"识别完成，找到 {len(parsed_citations)} 个文献引用")
            self.log_message(f"PDF识别完成，找到 {len(parsed_citations)} 个引用")

        except Exception as e:
            logger.error(f"PDF识别失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"PDF识别失败: {e}")
            self.status_var.set("识别失败")
    
    def recognize_screenshot_citations(self):
        """识别截图中的文献引用"""
        if not hasattr(self, 'current_screenshot_file'):
            messagebox.showwarning("警告", "请先选择截图文件")
            return
        
        try:
            self.status_var.set("正在识别截图中的文献引用...")
            self.root.update()
            
            # 提取引用
            citations = self.ocr_reader.extract_citations_from_image_file(self.current_screenshot_file)
            
            # 解析引用
            parsed_citations = self.citation_parser.parse_multiple_citations(citations)
            
            self.citations = parsed_citations
            self.update_citation_display()
            
            self.status_var.set(f"识别完成，找到 {len(parsed_citations)} 个文献引用")
            self.log_message(f"截图识别完成，找到 {len(parsed_citations)} 个引用")
            
        except Exception as e:
            logger.error(f"截图识别失败: {e}")
            messagebox.showerror("错误", f"截图识别失败: {e}")
            self.status_var.set("识别失败")
    
    def update_citation_display(self):
        """更新文献引用显示"""
        # 清空现有内容
        for item in self.citation_tree.get_children():
            self.citation_tree.delete(item)

        # 添加新内容
        for i, citation in enumerate(self.citations):
            # 获取引用编号
            ref_num = citation.get('reference_number', str(i+1))

            # 处理作者信息
            authors = citation.get('authors', '')
            if isinstance(authors, list):
                author_str = ', '.join(authors[:2])  # 只显示前两个作者
                if len(authors) > 2:
                    author_str += ' et al.'
            elif isinstance(authors, str):
                author_str = authors.strip() if authors else '未识别'
            else:
                author_str = '未识别'

            # 获取标题
            title = citation.get('title', '')
            if isinstance(title, str):
                title = title.strip()
            else:
                title = str(title) if title else ''

            if not title:
                # 如果没有标题，显示原始文本的前部分
                full_text = citation.get('full_match', '')
                if full_text:
                    title = f"[{ref_num}] {full_text[:60]}..."
                else:
                    title = f"[{ref_num}] 未识别标题"

            # 获取期刊信息
            journal = citation.get('journal', '')
            if isinstance(journal, str):
                journal = journal.strip()
            else:
                journal = str(journal) if journal else ''
            if not journal:
                journal = '未识别'

            # 获取年份
            year = citation.get('year', '')
            if isinstance(year, str):
                year = year.strip()
            else:
                year = str(year) if year else ''
            if not year:
                year = '未识别'

            # 计算置信度
            confidence = citation.get('confidence', 0)
            if confidence == 0:
                # 根据信息完整性估算置信度
                score = 0
                if citation.get('title'): score += 0.4
                if citation.get('authors'): score += 0.3
                if citation.get('year'): score += 0.2
                if citation.get('journal'): score += 0.1
                confidence = score

            # 插入到表格中
            self.citation_tree.insert('', tk.END, values=(
                title[:100] + '...' if len(title) > 100 else title,
                author_str[:50] + '...' if len(author_str) > 50 else author_str,
                year,
                journal[:40] + '...' if len(journal) > 40 else journal,
                f"{confidence:.2f}"
            ))

        # 在日志中显示识别统计
        total = len(self.citations)

        def safe_strip(value):
            """安全的strip函数，处理不同类型"""
            if isinstance(value, str):
                return value.strip()
            elif isinstance(value, list):
                return len(value) > 0
            else:
                return bool(value)

        with_authors = sum(1 for c in self.citations if safe_strip(c.get('authors', '')))
        with_titles = sum(1 for c in self.citations if safe_strip(c.get('title', '')))
        with_years = sum(1 for c in self.citations if safe_strip(c.get('year', '')))

        self.log_message(f"识别统计: 总数{total}, 有作者{with_authors}, 有标题{with_titles}, 有年份{with_years}")

    def update_download_citation_display(self):
        """更新搜索页面的文献列表"""
        # 清空现有内容
        for item in self.download_citation_tree.get_children():
            self.download_citation_tree.delete(item)

        # 添加新内容
        for i, citation in enumerate(self.citations):
            # 获取引用编号
            ref_num = citation.get('reference_number', str(i+1))

            # 处理作者信息
            authors = citation.get('authors', '')
            if isinstance(authors, list):
                author_str = ', '.join(authors[:2])  # 只显示前两个作者
                if len(authors) > 2:
                    author_str += ' et al.'
            elif isinstance(authors, str):
                author_str = authors.strip() if authors else '未识别'
            else:
                author_str = '未识别'

            # 获取标题
            title = citation.get('title', '')
            if isinstance(title, str):
                title = title.strip()
            else:
                title = str(title) if title else ''

            if not title:
                # 如果没有标题，显示原始文本的前部分
                full_text = citation.get('full_match', '')
                if full_text:
                    title = f"{full_text[:80]}..."
                else:
                    title = "未识别标题"

            # 获取期刊信息
            journal = citation.get('journal', '')
            if isinstance(journal, str):
                journal = journal.strip()
            else:
                journal = str(journal) if journal else ''
            if not journal:
                journal = '未识别'

            # 获取年份
            year = citation.get('year', '')
            if isinstance(year, str):
                year = year.strip()
            else:
                year = str(year) if year else ''
            if not year:
                year = '未识别'

            # 计算置信度
            confidence = citation.get('confidence', 0)
            if confidence == 0:
                # 根据信息完整性估算置信度
                score = 0
                if citation.get('title'): score += 0.4
                if citation.get('authors'): score += 0.3
                if citation.get('year'): score += 0.2
                if citation.get('journal'): score += 0.1
                confidence = score

            # 插入到表格中（默认选中）
            self.download_citation_tree.insert('', tk.END, values=(
                "☑",  # 默认选中
                f"[{ref_num}]",
                title[:60] + '...' if len(title) > 60 else title,
                author_str[:30] + '...' if len(author_str) > 30 else author_str,
                year,
                journal[:25] + '...' if len(journal) > 25 else journal,
                f"{confidence:.2f}"
            ))

        # 更新选中数量
        self.update_selected_count()
        self.log_message(f"搜索页面已更新，显示 {len(self.citations)} 个文献")
    
    def select_download_dir(self):
        """选择下载目录"""
        directory = filedialog.askdirectory(title="选择下载目录")
        if directory:
            self.download_dir.set(directory)
            config_manager.set_download_dir(directory)
            self.log_message(f"设置下载目录: {directory}")

    def select_browser_path(self):
        """选择浏览器路径"""
        filetypes = [
            ("可执行文件", "*.exe"),
            ("快捷方式", "*.lnk"),
            ("所有文件", "*.*")
        ]
        filepath = filedialog.askopenfilename(
            title="选择浏览器程序",
            filetypes=filetypes
        )
        if filepath:
            self.custom_browser_path.set(filepath)
            # 保存到配置
            config_manager.set_browser_config(filepath, self.use_custom_browser.get())
            self.log_message(f"设置浏览器路径: {filepath}")

    def toggle_custom_browser(self, log_change=True):
        """切换自定义浏览器设置"""
        use_custom = self.use_custom_browser.get()

        # 启用/禁用相关控件
        state = tk.NORMAL if use_custom else tk.DISABLED
        if hasattr(self, 'browser_path_entry'):
            self.browser_path_entry.config(state=state)
        if hasattr(self, 'browser_browse_btn'):
            self.browser_browse_btn.config(state=state)

        # 保存设置
        config_manager.set_browser_config(self.custom_browser_path.get(), use_custom)

        # 只在需要时记录日志
        if log_change:
            if use_custom:
                self.log_message("启用自定义浏览器路径")
            else:
                self.log_message("使用默认浏览器设置")
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 检查log_text是否存在
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        else:
            # 如果log_text还没创建，就打印到控制台
            print(log_entry.strip())
    
    def search_selected_citations(self):
        """在Web of Science中搜索选中的文献引用"""
        # 获取选中的文献
        selected_citations = self.get_selected_citations()
        if not selected_citations:
            messagebox.showwarning("警告", "请先选择要搜索的文献引用")
            return

        try:
            self.status_var.set("正在Web of Science中搜索...")
            self.root.update()

            search_results = []

            for citation in selected_citations:
                # 获取文献信息
                title = citation.get('title', '')
                authors = citation.get('authors', '')
                if isinstance(authors, list):
                    authors = ', '.join(authors[:2])

                # 显示搜索进度
                ref_num = citation.get('reference_number', '?')
                display_title = title[:50] + '...' if len(title) > 50 else title
                self.log_message(f"搜索 [{ref_num}]: {display_title}")

                # 在Web of Science中搜索
                if title and title != '未识别' and len(title) > 10:
                    results = self.wos_searcher.search_by_title(title, max_results=3)
                elif authors and authors != '未识别':
                    results = self.wos_searcher.search_by_author_and_keywords(authors, "", max_results=3)
                else:
                    self.log_message(f"跳过 [{ref_num}]: 信息不足")
                    continue

                if results:
                    self.log_message(f"找到 {len(results)} 个结果")
                    search_results.extend(results)
                else:
                    self.log_message(f"未找到结果")

            if search_results:
                self.show_search_results(search_results)
                self.status_var.set(f"搜索完成，找到 {len(search_results)} 个结果")
                self.log_message(f"Web of Science搜索完成，找到 {len(search_results)} 个结果")
            else:
                self.status_var.set("未找到搜索结果")
                self.log_message("Web of Science搜索未找到结果")
                messagebox.showinfo("提示", "未找到相关文献")

        except Exception as e:
            logger.error(f"Web of Science搜索失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {e}")
            self.status_var.set("搜索失败")

    def show_search_results(self, results: List[Dict[str, str]]):
        """显示搜索结果"""
        # 创建搜索结果窗口
        result_window = tk.Toplevel(self.root)
        result_window.title("Web of Science 搜索结果")
        result_window.geometry("1000x600")

        # 创建结果表格
        columns = ('标题', '作者', '期刊', '年份', 'DOI', '引用次数')
        result_tree = ttk.Treeview(result_window, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            result_tree.heading(col, text=col)
            result_tree.column(col, width=150)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_window, orient=tk.VERTICAL, command=result_tree.yview)
        result_tree.configure(yscrollcommand=scrollbar.set)

        # 填充数据
        for result in results:
            result_tree.insert('', tk.END, values=(
                result.get('title', '')[:80] + '...' if len(result.get('title', '')) > 80 else result.get('title', ''),
                result.get('authors', '')[:40] + '...' if len(result.get('authors', '')) > 40 else result.get('authors', ''),
                result.get('journal', '')[:30] + '...' if len(result.get('journal', '')) > 30 else result.get('journal', ''),
                result.get('year', ''),
                result.get('doi', ''),
                result.get('citation_count', '')
            ))

        # 布局
        result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加下载按钮
        button_frame = ttk.Frame(result_window)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        def download_selected():
            selected = result_tree.selection()
            if selected:
                messagebox.showinfo("提示", f"准备下载 {len(selected)} 篇文献")
                # 这里可以添加下载逻辑
            else:
                messagebox.showwarning("警告", "请选择要下载的文献")

        ttk.Button(button_frame, text="下载选中文献", command=download_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=result_window.destroy).pack(side=tk.RIGHT, padx=5)

    def search_in_browser(self):
        """在浏览器中搜索选中的文献"""
        search_type = self.search_type.get()

        if search_type == "selected":
            self.search_selected_in_browser()
        else:
            self.manual_search_in_browser()

    def search_selected_in_browser(self):
        """在浏览器中搜索选中的文献"""
        # 获取选中的文献
        selected_citations = self.get_selected_citations()
        if not selected_citations:
            messagebox.showwarning("警告", "请先选择要搜索的文献引用")
            return

        try:
            # 创建搜索URL列表
            search_urls = self.browser_helper.create_search_urls(selected_citations)

            if not search_urls:
                messagebox.showwarning("警告", "选中的文献信息不足，无法生成搜索")
                return

            # 显示搜索确认对话框
            message = f"将为 {len(search_urls)} 个文献在浏览器中打开搜索页面。\n\n"
            message += "这将使用您的校园网登录状态。\n"
            message += "是否继续？"

            if messagebox.askyesno("确认搜索", message):
                # 逐个打开搜索页面
                opened_count = 0
                for search_info in search_urls:
                    try:
                        import webbrowser
                        webbrowser.open(search_info['url'])
                        opened_count += 1

                        ref_num = search_info['ref_num']
                        search_type = search_info['search_type']
                        self.log_message(f"已打开 {ref_num} 的{search_type}页面")

                        # 避免同时打开太多页面
                        if opened_count >= 5:
                            remaining = len(search_urls) - opened_count
                            if remaining > 0:
                                if messagebox.askyesno("继续搜索", f"已打开5个搜索页面，还有{remaining}个。是否继续？"):
                                    continue
                                else:
                                    break

                    except Exception as e:
                        logger.error(f"打开搜索页面失败: {e}")
                        continue

                self.status_var.set(f"已在浏览器中打开 {opened_count} 个搜索页面")
                self.log_message(f"浏览器搜索完成，打开了 {opened_count} 个页面")

        except Exception as e:
            logger.error(f"浏览器搜索失败: {e}")
            messagebox.showerror("错误", f"浏览器搜索失败: {e}")

    def manual_search_in_browser(self):
        """手动浏览器搜索"""
        title_keywords = self.manual_search_entry.get().strip()
        authors = self.author_search_entry.get().strip()
        year = self.year_search_entry.get().strip()

        if not title_keywords and not authors:
            messagebox.showwarning("警告", "请输入标题/关键词或作者")
            return

        try:
            # 构建搜索查询
            query_parts = []

            if title_keywords:
                query_parts.append(f'TI="{title_keywords}"')

            if authors:
                query_parts.append(f'AU="{authors}"')

            if year and year.isdigit():
                query_parts.append(f'PY={year}')

            query = ' AND '.join(query_parts)

            # 在浏览器中打开搜索
            if self.browser_helper.open_wos_search(query):
                self.status_var.set("已在浏览器中打开搜索页面")
                self.log_message(f"手动搜索: {query}")
            else:
                messagebox.showerror("错误", "无法打开浏览器搜索")

        except Exception as e:
            logger.error(f"手动浏览器搜索失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {e}")

    def start_wos_search(self):
        """开始Web of Science自动搜索（实验性功能）"""
        search_type = self.search_type.get()

        if search_type == "selected":
            self.search_selected_citations()
        else:
            self.manual_wos_search()

    def manual_wos_search(self):
        """手动Web of Science搜索"""
        title_keywords = self.manual_search_entry.get().strip()
        authors = self.author_search_entry.get().strip()
        year = self.year_search_entry.get().strip()

        if not title_keywords and not authors:
            messagebox.showwarning("警告", "请输入标题/关键词或作者")
            return

        try:
            self.status_var.set("正在Web of Science中搜索...")
            self.root.update()

            if title_keywords:
                self.log_message(f"搜索标题/关键词: {title_keywords}")
                results = self.wos_searcher.search_by_title(title_keywords, max_results=10)
            else:
                self.log_message(f"搜索作者: {authors}")
                results = self.wos_searcher.search_by_author_and_keywords(authors, "", year, max_results=10)

            if results:
                self.show_search_results(results)
                self.status_var.set(f"搜索完成，找到 {len(results)} 个结果")
                self.log_message(f"手动搜索完成，找到 {len(results)} 个结果")
            else:
                self.status_var.set("未找到搜索结果")
                self.log_message("手动搜索未找到结果")
                messagebox.showinfo("提示", "未找到相关文献")

        except Exception as e:
            logger.error(f"手动搜索失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {e}")
            self.status_var.set("搜索失败")

    def clear_search_results(self):
        """清空搜索结果"""
        # 清空搜索结果表格
        for item in self.search_tree.get_children():
            self.search_tree.delete(item)

        self.log_message("已清空搜索结果")
        self.status_var.set("就绪")

    def select_all_citations(self):
        """全选文献"""
        for item in self.download_citation_tree.get_children():
            values = list(self.download_citation_tree.item(item)['values'])
            values[0] = "☑"  # 选择框列
            self.download_citation_tree.item(item, values=values)

        self.update_selected_count()
        self.log_message("已全选所有文献")

    def deselect_all_citations(self):
        """全不选文献"""
        for item in self.download_citation_tree.get_children():
            values = list(self.download_citation_tree.item(item)['values'])
            values[0] = "☐"  # 选择框列
            self.download_citation_tree.item(item, values=values)

        self.update_selected_count()
        self.log_message("已取消选择所有文献")

    def invert_citation_selection(self):
        """反选文献"""
        for item in self.download_citation_tree.get_children():
            values = list(self.download_citation_tree.item(item)['values'])
            values[0] = "☑" if values[0] == "☐" else "☐"
            self.download_citation_tree.item(item, values=values)

        self.update_selected_count()
        self.log_message("已反选文献")

    def on_citation_click(self, event):
        """处理文献列表单击事件 - 切换选择状态"""
        item = self.download_citation_tree.identify_row(event.y)
        if item:
            # 切换选择状态
            values = list(self.download_citation_tree.item(item)['values'])
            if len(values) > 0:
                values[0] = "☑" if values[0] == "☐" else "☐"
                self.download_citation_tree.item(item, values=values)
                self.update_selected_count()

                # 显示操作反馈
                ref_num = values[1] if len(values) > 1 else "?"
                action = "选中" if values[0] == "☑" else "取消选中"
                self.log_message(f"{action}文献 {ref_num}")

    def on_citation_double_click(self, event):
        """处理文献列表双击事件 - 显示详细信息"""
        item = self.download_citation_tree.identify_row(event.y)
        if item:
            values = self.download_citation_tree.item(item)['values']
            if len(values) > 2:
                # 显示文献详细信息
                ref_num = values[1]
                title = values[2]
                authors = values[3]
                year = values[4]
                journal = values[5]

                detail_info = f"文献详情 {ref_num}:\n\n"
                detail_info += f"标题: {title}\n"
                detail_info += f"作者: {authors}\n"
                detail_info += f"年份: {year}\n"
                detail_info += f"期刊: {journal}\n"

                messagebox.showinfo("文献详情", detail_info)

    def update_selected_count(self):
        """更新选中数量显示"""
        total = len(self.download_citation_tree.get_children())
        selected = 0

        for item in self.download_citation_tree.get_children():
            values = self.download_citation_tree.item(item)['values']
            if values and values[0] == "☑":
                selected += 1

        self.selected_count_var.set(f"已选择: {selected} / {total}")

    def get_selected_citations(self):
        """获取选中的文献"""
        selected_citations = []

        for i, item in enumerate(self.download_citation_tree.get_children()):
            values = self.download_citation_tree.item(item)['values']
            if values and values[0] == "☑":
                # 根据索引获取对应的文献数据
                if i < len(self.citations):
                    selected_citations.append(self.citations[i])

        return selected_citations

    def auto_download_citations(self):
        """简单自动下载"""
        selected_items = self.get_selected_citations()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要下载的文献")
            return

        # 获取下载目录
        download_dir = self.download_dir.get()
        if not download_dir:
            messagebox.showwarning("警告", "请先设置下载目录")
            return

        # 确认下载
        if not messagebox.askyesno("确认",
                                  f"将自动下载 {len(selected_items)} 篇文献的EndNote引用\n"
                                  f"下载目录: {download_dir}\n\n"
                                  "注意：请确保已登录Web of Science\n"
                                  "是否继续？"):
            return

        # 创建下载器
        try:
            from modules.simple_downloader import SimpleDownloader

            # 获取浏览器设置
            browser_path = None
            if self.use_custom_browser.get() and self.custom_browser_path.get():
                browser_path = self.custom_browser_path.get()
                self.log_message(f"使用自定义浏览器: {browser_path}")

            downloader = SimpleDownloader(download_dir, browser_path)

            # 开始下载
            self.status_var.set("正在自动下载文献引用...")
            self.root.update()

            results = downloader.batch_download_simple(selected_items)

            # 显示结果
            message = f"""下载完成！
总计: {results['total']} 篇
处理: {results['processed']} 篇
成功: {results['downloaded']} 篇
失败: {results['failed']} 篇
跳过: {results['skipped']} 篇"""

            messagebox.showinfo("下载完成", message)
            self.status_var.set("下载完成")

            # 关闭浏览器
            downloader.close()

        except Exception as e:
            logger.error(f"下载失败: {e}")
            messagebox.showerror("错误", f"下载失败: {str(e)}")
            self.status_var.set("下载失败")

    def open_search_pages(self):
        """打开搜索页面"""
        selected_items = self.get_selected_citations()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要搜索的文献")
            return

        try:
            from modules.simple_downloader import SimpleDownloader

            # 获取浏览器设置
            browser_path = None
            if self.use_custom_browser.get() and self.custom_browser_path.get():
                browser_path = self.custom_browser_path.get()
                self.log_message(f"使用自定义浏览器: {browser_path}")

            downloader = SimpleDownloader("", browser_path)

            self.status_var.set("正在打开浏览器搜索页面...")
            self.root.update()

            urls = downloader.open_urls_for_manual_download(selected_items)

            message = f"已在浏览器中打开 {len(urls)} 个搜索页面\n请手动下载EndNote引用"
            messagebox.showinfo("完成", message)
            self.status_var.set("搜索页面已打开")

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {str(e)}")
            self.status_var.set("搜索失败")

    def smart_search_helper(self):
        """智能搜索助手 - 全自动版本"""
        selected_items = self.get_selected_citations()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要搜索的文献")
            return

        # 获取下载目录
        download_dir = self.download_dir.get()
        if not download_dir:
            messagebox.showwarning("警告", "请先设置下载目录")
            return

        # 确认下载
        if not messagebox.askyesno("确认",
                                  f"智能搜索助手将自动搜索并下载 {len(selected_items)} 篇文献的EndNote引用\n"
                                  f"下载目录: {download_dir}\n\n"
                                  "注意：请确保已登录Web of Science\n"
                                  "整个过程完全自动化，无需手动操作\n"
                                  "是否继续？"):
            return

        try:
            from modules.smart_search_helper import SmartSearchHelper

            # 获取浏览器设置
            browser_path = None
            if self.use_custom_browser.get() and self.custom_browser_path.get():
                browser_path = self.custom_browser_path.get()
                self.log_message(f"使用自定义浏览器: {browser_path}")

            helper = SmartSearchHelper(download_dir, browser_path)

            self.status_var.set("正在启动智能搜索助手...")
            self.root.update()

            self.log_message(f"开始自动搜索下载 {len(selected_items)} 篇文献")

            # 执行自动搜索和下载
            results = helper.auto_search_and_download(selected_items)

            # 显示结果
            message = f"""智能搜索助手完成！
总计: {results['total']} 篇
处理: {results['processed']} 篇
成功: {results['downloaded']} 篇
失败: {results['failed']} 篇
跳过: {results['skipped']} 篇

文件保存在: {download_dir}"""

            messagebox.showinfo("完成", message)
            self.status_var.set(f"智能搜索完成: {results['downloaded']}/{results['total']}")
            self.log_message(f"智能搜索助手完成: 成功{results['downloaded']}篇, 失败{results['failed']}篇")

        except Exception as e:
            logger.error(f"智能搜索助手失败: {e}")
            messagebox.showerror("错误", f"智能搜索助手失败: {str(e)}")
            self.status_var.set("搜索失败")

    def download_selected_papers(self):
        """下载选中的文献"""
        # 这里实现下载逻辑
        messagebox.showinfo("提示", "下载功能开发中...")
    
    def batch_download_papers(self):
        """批量下载文献"""
        # 这里实现批量下载逻辑
        messagebox.showinfo("提示", "批量下载功能开发中...")


class LiteratureCitationApp:
    """文献引用提取应用"""

    def __init__(self):
        self.root = tk.Tk()
        self.main_window = MainWindow(self.root)

    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"程序运行出错: {e}")
            messagebox.showerror("错误", f"程序运行出错: {e}")


def main():
    """主函数"""
    app = LiteratureCitationApp()
    app.run()


if __name__ == "__main__":
    main()
