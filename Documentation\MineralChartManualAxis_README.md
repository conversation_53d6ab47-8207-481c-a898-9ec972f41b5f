# 矿物堆叠柱状图手动坐标轴设置功能

## 概述

`MineralStackedBarChartControl` 控件现在包含完整的可视化坐标轴控制面板，允许用户通过界面直接设置X轴和Y轴的参数。控件还修复了图例显示问题，确保图例项水平均分面板宽度。

## 新增功能

### 1. 可视化坐标轴控制面板

- 位于控件顶部的120像素高度控制面板
- 包含X轴和Y轴设置分组
- 实时预览和应用设置
- 一键重置功能

### 2. 优化的图例显示

- 图例项自动水平均分面板宽度
- 根据面板宽度动态计算最佳列数
- 自适应行高和面板高度
- 改进的文字和颜色块布局

## 功能特性

### 1. 手动X轴设置

- 自定义X轴最小值、最大值和间隔
- 自定义X轴标题
- 支持任意数值范围（不限于0-100%）

### 2. 手动Y轴设置

- 可以手动设置Y轴范围和间隔
- 支持使用深度数据自动填充Y轴范围
- 自定义Y轴标题

### 3. 自动/手动模式切换

- 可以随时在自动模式和手动模式之间切换
- 自动模式使用数据驱动的坐标轴设置
- 手动模式使用用户指定的参数

## API 参考

### 属性

#### X轴相关属性

```csharp
public bool ManualXAxisEnabled { get; set; }     // 是否启用手动X轴设置
public double XAxisMinimum { get; set; }         // X轴最小值
public double XAxisMaximum { get; set; }         // X轴最大值
public double XAxisInterval { get; set; }       // X轴间隔
public string XAxisTitle { get; set; }          // X轴标题
```

#### Y轴相关属性

```csharp
public bool ManualYAxisEnabled { get; set; }    // 是否启用手动Y轴设置
public double YAxisMinimum { get; set; }         // Y轴最小值
public double YAxisMaximum { get; set; }         // Y轴最大值
public double YAxisInterval { get; set; }       // Y轴间隔
public string YAxisTitle { get; set; }          // Y轴标题
```

### 方法

#### SetManualXAxis

设置手动X轴参数

```csharp
public void SetManualXAxis(double minimum, double maximum, double interval, string title = "矿物含量/%")
```

**参数：**

- `minimum`: X轴最小值
- `maximum`: X轴最大值
- `interval`: X轴间隔
- `title`: X轴标题（可选，默认为"矿物含量/%"）

**示例：**

```csharp
// 设置X轴范围为0-100%，每10%一个刻度
mineralChart.SetManualXAxis(0, 100, 10, "矿物含量百分比/%");
```

#### SetManualYAxisWithDepthData

设置手动Y轴参数，使用深度数据自动填充范围

```csharp
public void SetManualYAxisWithDepthData(string title = "深度/m", double? interval = null)
```

**参数：**

- `title`: Y轴标题（可选，默认为"深度/m"）
- `interval`: Y轴间隔（可选，如果不指定则自动计算）

**示例：**

```csharp
// 使用深度数据设置Y轴，间隔200m
mineralChart.SetManualYAxisWithDepthData("井深/m", 200);

// 使用深度数据设置Y轴，自动计算间隔
mineralChart.SetManualYAxisWithDepthData("深度/m");
```

#### DisableManualAxis

禁用手动坐标轴设置，恢复自动模式

```csharp
public void DisableManualAxis()
```

**示例：**

```csharp
// 恢复自动坐标轴模式
mineralChart.DisableManualAxis();
```

## 使用场景

### 场景1：标准化X轴显示

当需要在多个图表之间保持一致的X轴范围时：

```csharp
// 所有图表都使用相同的X轴设置
mineralChart.SetManualXAxis(0, 100, 20, "矿物含量/%");
```

### 场景2：自定义深度范围显示

当需要显示特定深度范围的数据时：

```csharp
// 手动设置Y轴显示4000-6000m范围
mineralChart.ManualYAxisEnabled = true;
mineralChart.YAxisMinimum = 4000;
mineralChart.YAxisMaximum = 6000;
mineralChart.YAxisInterval = 500;
mineralChart.YAxisTitle = "测量深度/m";
```

### 场景3：基于数据的Y轴优化

当需要基于实际数据范围优化Y轴显示时：

```csharp
// 使用数据驱动的Y轴设置，但指定特定间隔
mineralChart.SetManualYAxisWithDepthData("深度/m", 100);
```

## 注意事项

1. **数据兼容性**：手动设置的坐标轴范围应该与实际数据范围兼容
2. **性能考虑**：频繁切换手动/自动模式会触发图表重绘
3. **数据更新**：当数据更新时，手动设置的坐标轴参数会保持不变
4. **深度数据要求**：使用`SetManualYAxisWithDepthData`方法时，数据表中必须包含深度列

## 完整示例

参见 `Examples/MineralChartManualAxisExample.cs` 文件，其中包含了完整的使用示例。
