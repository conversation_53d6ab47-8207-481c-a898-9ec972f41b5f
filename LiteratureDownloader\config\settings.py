"""
程序配置文件
包含所有可配置的参数和设置
"""

import os
from pathlib import Path

# 程序基本信息
APP_NAME = "文献识别下载自动化程序"
APP_VERSION = "1.0.0"
APP_AUTHOR = "AI Assistant"

# 文件路径配置
BASE_DIR = Path(__file__).parent.parent
DOWNLOAD_DIR = BASE_DIR / "downloads"  # 默认下载目录
TEMP_DIR = BASE_DIR / "temp"  # 临时文件目录
LOG_DIR = BASE_DIR / "logs"  # 日志文件目录

# 确保目录存在
for directory in [DOWNLOAD_DIR, TEMP_DIR, LOG_DIR]:
    directory.mkdir(exist_ok=True)

# OCR配置
OCR_CONFIG = {
    'tesseract_cmd': r'C:\Program Files\Tesseract-OCR\tesseract.exe',  # Tesseract路径
    'language': 'eng',  # 支持英文（如果没有中文包就只用英文）
    'config': '--psm 6',  # 页面分割模式
}

# PDF处理配置
PDF_CONFIG = {
    'max_pages': 100,  # 最大处理页数
    'extract_images': False,  # 是否提取图片中的文字
}

# 网络请求配置
NETWORK_CONFIG = {
    'timeout': 30,  # 请求超时时间（秒）
    'max_retries': 3,  # 最大重试次数
    'delay_between_requests': 1,  # 请求间隔（秒）
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
}

# 文献匹配配置
MATCHING_CONFIG = {
    'similarity_threshold': 0.8,  # 相似度阈值
    'author_weight': 0.3,  # 作者匹配权重
    'title_weight': 0.5,   # 标题匹配权重
    'year_weight': 0.2,    # 年份匹配权重
}

# 支持的文件格式
SUPPORTED_FORMATS = {
    'pdf': ['.pdf'],
    'image': ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif'],
    'document': ['.pdf', '.doc', '.docx'],
}

# 常用学术网站配置
ACADEMIC_SITES = {
    'google_scholar': 'https://scholar.google.com/scholar?q=',
    'pubmed': 'https://pubmed.ncbi.nlm.nih.gov/?term=',
    'arxiv': 'https://arxiv.org/search/?query=',
    'ieee': 'https://ieeexplore.ieee.org/search/searchresult.jsp?queryText=',
    'springer': 'https://link.springer.com/search?query=',
    'sciencedirect': 'https://www.sciencedirect.com/search?qs=',
    'web_of_science': 'https://webofscience.clarivate.cn/wos/alldb/smart-search',
}

# Web of Science 特殊配置
WOS_CONFIG = {
    'base_url': 'https://webofscience.clarivate.cn',
    'search_url': 'https://webofscience.clarivate.cn/wos/alldb/smart-search',
    'search_api': 'https://webofscience.clarivate.cn/api/wosnlp/search',
    'selectors': {
        'search_box': 'input[data-ta="search-input"]',
        'search_button': 'button[data-ta="search-button"]',
        'result_items': '.search-results-item',
        'title': '.title-link',
        'authors': '.authors',
        'journal': '.journal-title',
        'year': '.year',
        'doi': '.doi-link',
        'pdf_link': '.pdf-link, .full-text-link',
        'citation_count': '.citation-count'
    },
    'wait_timeout': 10,
    'max_results': 50
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}',
    'rotation': '10 MB',
    'retention': '7 days',
}

# GUI配置
GUI_CONFIG = {
    'window_size': '1000x700',
    'theme': 'default',
    'font_family': 'Microsoft YaHei',
    'font_size': 10,
}

# 文件命名规则
NAMING_RULES = {
    'max_filename_length': 100,
    'invalid_chars': r'[<>:"/\\|?*]',
    'replacement_char': '_',
    'format_template': '{title}_{first_author}_{year}',
}

# 下载配置
DOWNLOAD_CONFIG = {
    'chunk_size': 8192,  # 下载块大小
    'max_file_size': 100 * 1024 * 1024,  # 最大文件大小 (100MB)
    'allowed_extensions': ['.pdf', '.doc', '.docx', '.txt'],
}

# 浏览器配置
BROWSER_CONFIG = {
    'custom_browser_path': '',  # 用户自定义浏览器路径
    'use_custom_browser': False,  # 是否使用自定义浏览器
    'browser_args': [
        '--remote-debugging-port=9222',
        '--disable-blink-features=AutomationControlled',
        '--disable-extensions',
        '--no-sandbox',
        '--disable-dev-shm-usage'
    ],
    'download_dir': str(DOWNLOAD_DIR),  # 默认下载目录
}

# 正则表达式模式（用于识别文献引用）
CITATION_PATTERNS = {
    'apa_style': r'([A-Z][a-zA-Z\s,&\.]+)\s*\((\d{4})\)\.\s*([^.]+)\.\s*([^.]+)\.',
    'mla_style': r'([A-Z][a-zA-Z\s,]+)\.\s*"([^"]+)"\.\s*([^,]+),\s*(\d{4})',
    'chicago_style': r'([A-Z][a-zA-Z\s,]+)\.\s*"([^"]+)"\.\s*([^.]+)\s*\((\d{4})\)',
    'ieee_style': r'\[(\d+)\]\s*([A-Z][a-zA-Z\s,&\.]+),\s*"([^"]+)",\s*([^,]+),\s*(\d{4})',
    'numbered_ref': r'\[(\d+)\]\s*([A-Z][^.]+\.)\s*([^.]+\.)\s*(\d{4})',
    'author_year': r'([A-Z][a-zA-Z\s,&\.]+)\s*\((\d{4})\)',
    'doi_pattern': r'(?:doi:|DOI:)\s*(10\.\d+\/[^\s,]+)',
    'isbn_pattern': r'ISBN[:\s]*(\d{3}-?\d{1,5}-?\d{1,7}-?\d{1,7}-?\d{1})',
    'url_pattern': r'https?://[^\s,]+',
    'journal_pattern': r'([A-Z][^.]+\.)\s*(\d{4})[;,]\s*(\d+)',
}

def get_config(section=None):
    """获取配置信息"""
    if section:
        return globals().get(f'{section.upper()}_CONFIG', {})
    return {
        'app': {'name': APP_NAME, 'version': APP_VERSION, 'author': APP_AUTHOR},
        'paths': {'base': BASE_DIR, 'download': DOWNLOAD_DIR, 'temp': TEMP_DIR, 'log': LOG_DIR},
        'ocr': OCR_CONFIG,
        'pdf': PDF_CONFIG,
        'network': NETWORK_CONFIG,
        'matching': MATCHING_CONFIG,
        'formats': SUPPORTED_FORMATS,
        'sites': ACADEMIC_SITES,
        'wos': WOS_CONFIG,
        'log': LOG_CONFIG,
        'gui': GUI_CONFIG,
        'naming': NAMING_RULES,
        'download': DOWNLOAD_CONFIG,
        'browser': BROWSER_CONFIG,
        'patterns': CITATION_PATTERNS,
    }
