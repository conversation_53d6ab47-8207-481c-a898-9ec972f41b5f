#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文献识别下载自动化程序 - 主程序入口
功能：
1. PDF文件识别文献引用
2. 截图OCR识别文献引用
3. 自动搜索和下载文献
4. 智能匹配和验证
5. 自动重命名和文件管理
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import tkinter as tk
    from tkinter import messagebox
    from loguru import logger

    from config.settings import get_config, LOG_DIR, LOG_CONFIG
    from gui.main_window import MainWindow

except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖包：pip install -r requirements.txt")
    input("按回车键退出...")
    sys.exit(1)


def setup_logging():
    """设置日志系统"""
    try:
        # 移除默认的日志处理器
        logger.remove()
        
        # 添加文件日志处理器
        log_file = LOG_DIR / "literature_downloader.log"
        logger.add(
            log_file,
            level=LOG_CONFIG['level'],
            format=LOG_CONFIG['format'],
            rotation=LOG_CONFIG['rotation'],
            retention=LOG_CONFIG['retention'],
            encoding='utf-8'
        )
        
        # 添加控制台日志处理器
        logger.add(
            sys.stderr,
            level=LOG_CONFIG['level'],
            format=LOG_CONFIG['format']
        )
        
        logger.info("日志系统初始化完成")
        
    except Exception as e:
        print(f"日志系统初始化失败: {e}")


def check_dependencies():
    """检查必要的依赖和环境"""
    missing_deps = []
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        logger.error("需要Python 3.7或更高版本")
        return False
    
    # 检查必要的包
    required_packages = [
        'tkinter', 'PIL', 'requests', 'bs4', 
        'PyPDF2', 'pdfplumber', 'pytesseract'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_deps.append(package)
    
    if missing_deps:
        logger.error(f"缺少必要的依赖包: {', '.join(missing_deps)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    # 检查Tesseract OCR
    try:
        import pytesseract
        from config.settings import OCR_CONFIG
        
        tesseract_path = OCR_CONFIG.get('tesseract_cmd')
        if tesseract_path and not os.path.exists(tesseract_path):
            logger.warning(f"Tesseract OCR路径不存在: {tesseract_path}")
            logger.info("请安装Tesseract OCR或在config/settings.py中配置正确路径")
    except Exception as e:
        logger.warning(f"Tesseract OCR检查失败: {e}")
    
    return True


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        logger.info("程序启动")
        
        # 检查依赖
        if not check_dependencies():
            messagebox.showerror("错误", "依赖检查失败，请查看日志文件获取详细信息")
            return
        
        # 创建主窗口
        logger.info("创建主窗口...")
        root = tk.Tk()
        logger.info("创建MainWindow...")
        app = MainWindow(root)
        logger.info("MainWindow创建完成")

        # 确保窗口显示在前台
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)

        # 设置窗口关闭事件
        def on_closing():
            logger.info("程序正在关闭")
            root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动GUI主循环
        logger.info("启动GUI界面")
        root.mainloop()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        messagebox.showerror("错误", f"程序运行出错: {e}")
    finally:
        logger.info("程序结束")


if __name__ == "__main__":
    main()
