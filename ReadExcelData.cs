using System;
using System.Data;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace BritSystem
{
    public class ExcelDataReader
    {
        public static void ReadAndDisplayExcelData()
        {
            try
            {
                Console.WriteLine("=== 读取矿物组分法数据 (100数据.xlsx) ===");
                ReadExcelFile(@"SampleData\100数据.xlsx");
                
                Console.WriteLine("\n=== 读取静态岩石力学参数法数据 (静态岩石力学参数数据_20250611.xlsx) ===");
                ReadExcelFile(@"SampleData\静态岩石力学参数数据_20250611.xlsx");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取Excel文件时出错: {ex.Message}");
            }
        }

        private static void ReadExcelFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"文件不存在: {filePath}");
                    return;
                }

                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook;
                    if (Path.GetExtension(filePath).ToLower() == ".xlsx")
                        workbook = new XSSFWorkbook(fs);
                    else
                        workbook = new HSSFWorkbook(fs);

                    ISheet sheet = workbook.GetSheetAt(0);
                    Console.WriteLine($"工作表名称: {sheet.SheetName}");
                    Console.WriteLine($"总行数: {sheet.PhysicalNumberOfRows}");

                    // 读取表头
                    IRow headerRow = sheet.GetRow(0);
                    if (headerRow != null)
                    {
                        Console.WriteLine("列名:");
                        for (int i = 0; i < headerRow.PhysicalNumberOfCells; i++)
                        {
                            ICell cell = headerRow.GetCell(i);
                            string cellValue = cell?.ToString() ?? "";
                            Console.WriteLine($"  列 {i}: {cellValue}");
                        }
                    }

                    // 读取前10行数据
                    Console.WriteLine("\n前10行数据:");
                    int maxRows = Math.Min(11, sheet.PhysicalNumberOfRows); // 包括表头
                    for (int i = 0; i < maxRows; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row != null)
                        {
                            Console.Write($"行 {i}: ");
                            for (int j = 0; j < row.PhysicalNumberOfCells; j++)
                            {
                                ICell cell = row.GetCell(j);
                                string cellValue = cell?.ToString() ?? "";
                                Console.Write($"{cellValue}\t");
                            }
                            Console.WriteLine();
                        }
                    }

                    // 分析深度和脆性指数数据范围
                    AnalyzeDataRange(sheet);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件 {filePath} 时出错: {ex.Message}");
            }
        }

        private static void AnalyzeDataRange(ISheet sheet)
        {
            try
            {
                Console.WriteLine("\n数据范围分析:");
                
                // 查找深度和脆性指数列
                IRow headerRow = sheet.GetRow(0);
                int depthColumn = -1;
                int brittlenessColumn = -1;
                
                if (headerRow != null)
                {
                    for (int i = 0; i < headerRow.PhysicalNumberOfCells; i++)
                    {
                        string cellValue = headerRow.GetCell(i)?.ToString()?.ToLower() ?? "";
                        if (cellValue.Contains("深度") || cellValue.Contains("depth"))
                        {
                            depthColumn = i;
                            Console.WriteLine($"找到深度列: 列 {i} ({headerRow.GetCell(i)})");
                        }
                        if (cellValue.Contains("脆性") || cellValue.Contains("brittleness") || cellValue.Contains("brit"))
                        {
                            brittlenessColumn = i;
                            Console.WriteLine($"找到脆性指数列: 列 {i} ({headerRow.GetCell(i)})");
                        }
                    }
                }

                if (depthColumn >= 0 && brittlenessColumn >= 0)
                {
                    double minDepth = double.MaxValue, maxDepth = double.MinValue;
                    double minBrittleness = double.MaxValue, maxBrittleness = double.MinValue;
                    int validDataCount = 0;

                    for (int i = 1; i < sheet.PhysicalNumberOfRows; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row != null)
                        {
                            ICell depthCell = row.GetCell(depthColumn);
                            ICell brittlenessCell = row.GetCell(brittlenessColumn);

                            if (depthCell != null && brittlenessCell != null)
                            {
                                if (double.TryParse(depthCell.ToString(), out double depth) &&
                                    double.TryParse(brittlenessCell.ToString(), out double brittleness))
                                {
                                    minDepth = Math.Min(minDepth, depth);
                                    maxDepth = Math.Max(maxDepth, depth);
                                    minBrittleness = Math.Min(minBrittleness, brittleness);
                                    maxBrittleness = Math.Max(maxBrittleness, brittleness);
                                    validDataCount++;
                                }
                            }
                        }
                    }

                    Console.WriteLine($"有效数据点数量: {validDataCount}");
                    Console.WriteLine($"深度范围: {minDepth:F2} - {maxDepth:F2}");
                    Console.WriteLine($"脆性指数范围: {minBrittleness:F2} - {maxBrittleness:F2}");
                }
                else
                {
                    Console.WriteLine("未找到深度或脆性指数列");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析数据范围时出错: {ex.Message}");
            }
        }
    }
}
