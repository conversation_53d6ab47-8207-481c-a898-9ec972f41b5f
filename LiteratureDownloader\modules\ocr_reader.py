#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR截图识别模块
功能：
1. 截取屏幕截图
2. 从图像中识别文本
3. 识别文献引用
4. 支持多种图像格式
5. 图像预处理优化识别效果
"""

import re
import io
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from loguru import logger

try:
    from PIL import Image, ImageGrab, ImageEnhance, ImageFilter
    import pytesseract
    import tkinter as tk
    from tkinter import filedialog, messagebox

    # 可选导入
    try:
        import cv2
        import numpy as np
        HAS_OPENCV = True
    except ImportError:
        HAS_OPENCV = False
        logger.warning("OpenCV未安装，某些高级图像处理功能将不可用")

except ImportError as e:
    logger.error(f"OCR模块导入失败: {e}")
    raise

from config.settings import OCR_CONFIG, CITATION_PATTERNS, SUPPORTED_FORMATS


class OCRReader:
    """OCR图像识别和处理类"""
    
    def __init__(self):
        self.ocr_config = OCR_CONFIG
        self.citation_patterns = CITATION_PATTERNS
        self.supported_formats = SUPPORTED_FORMATS['image']
        
        # 设置Tesseract路径
        if self.ocr_config.get('tesseract_cmd'):
            pytesseract.pytesseract.tesseract_cmd = self.ocr_config['tesseract_cmd']
    
    def capture_screenshot(self, bbox: Optional[Tuple[int, int, int, int]] = None) -> Image.Image:
        """截取屏幕截图"""
        try:
            if bbox:
                # 截取指定区域
                screenshot = ImageGrab.grab(bbox=bbox)
                logger.info(f"截取指定区域截图: {bbox}")
            else:
                # 截取全屏
                screenshot = ImageGrab.grab()
                logger.info("截取全屏截图")
            
            return screenshot
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    def load_image(self, image_path: str) -> Optional[Image.Image]:
        """加载图像文件"""
        try:
            if not Path(image_path).exists():
                logger.error(f"图像文件不存在: {image_path}")
                return None
            
            # 检查文件格式
            file_ext = Path(image_path).suffix.lower()
            if file_ext not in self.supported_formats:
                logger.error(f"不支持的图像格式: {file_ext}")
                return None
            
            image = Image.open(image_path)
            logger.info(f"成功加载图像: {image_path}, 尺寸: {image.size}")
            return image
            
        except Exception as e:
            logger.error(f"加载图像失败: {e}")
            return None
    
    def preprocess_image(self, image: Image.Image, enhance: bool = True) -> Image.Image:
        """图像预处理，提高OCR识别效果"""
        try:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')

            if enhance:
                # 增强对比度
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(1.5)

                # 增强锐度
                enhancer = ImageEnhance.Sharpness(image)
                image = enhancer.enhance(1.2)

                # 调整亮度
                enhancer = ImageEnhance.Brightness(image)
                image = enhancer.enhance(1.1)

            # 如果有OpenCV，进行高级处理
            if HAS_OPENCV:
                # 转换为OpenCV格式进行进一步处理
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                # 转换为灰度图
                gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

                # 高斯模糊去噪
                blurred = cv2.GaussianBlur(gray, (3, 3), 0)

                # 自适应阈值二值化
                binary = cv2.adaptiveThreshold(
                    blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY, 11, 2
                )

                # 形态学操作去除噪点
                kernel = np.ones((2, 2), np.uint8)
                cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

                # 转换回PIL格式
                processed_image = Image.fromarray(cleaned)
            else:
                # 简单的PIL处理
                processed_image = image.convert('L')  # 转为灰度
                processed_image = processed_image.filter(ImageFilter.SHARPEN)  # 锐化

            logger.info("图像预处理完成")
            return processed_image

        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return image
    
    def extract_text_from_image(self, image: Image.Image, preprocess: bool = True) -> str:
        """从图像中提取文本"""
        try:
            if preprocess:
                image = self.preprocess_image(image)
            
            # 使用Tesseract进行OCR识别
            text = pytesseract.image_to_string(
                image,
                lang=self.ocr_config['language'],
                config=self.ocr_config['config']
            )
            
            # 清理文本
            text = self.clean_text(text)
            
            logger.info(f"OCR识别完成，提取文本长度: {len(text)} 字符")
            return text
            
        except Exception as e:
            logger.error(f"OCR文本提取失败: {e}")
            return ""
    
    def clean_text(self, text: str) -> str:
        """清理OCR识别的文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\.\,\;\:\(\)\[\]\-\"\'\&]', '', text)
        
        # 修复常见的OCR错误
        replacements = {
            'l': 'I',  # 小写l误识别为大写I
            '0': 'O',  # 数字0误识别为字母O（在特定上下文中）
            '5': 'S',  # 数字5误识别为字母S（在特定上下文中）
        }
        
        # 这里可以添加更多的OCR错误修正规则
        
        return text.strip()
    
    def find_citations_in_text(self, text: str) -> List[Dict[str, str]]:
        """从文本中识别文献引用"""
        citations = []
        
        for pattern_name, pattern in self.citation_patterns.items():
            try:
                matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
                
                for match in matches:
                    citation = {
                        'pattern_type': pattern_name,
                        'full_match': match.group(0),
                        'groups': match.groups(),
                        'start_pos': match.start(),
                        'end_pos': match.end(),
                        'confidence': self.calculate_citation_confidence(match.group(0))
                    }
                    
                    # 根据不同的引用格式解析字段
                    if pattern_name == 'apa_style':
                        citation.update({
                            'authors': match.group(1).strip(),
                            'year': match.group(2).strip(),
                            'title': match.group(3).strip(),
                            'journal': match.group(4).strip()
                        })
                    elif pattern_name == 'doi_pattern':
                        citation.update({
                            'doi': match.group(1).strip()
                        })
                    elif pattern_name == 'isbn_pattern':
                        citation.update({
                            'isbn': match.group(1).strip()
                        })
                    
                    citations.append(citation)
                    
            except Exception as e:
                logger.warning(f"模式匹配失败 {pattern_name}: {e}")
                continue
        
        # 去重和排序
        unique_citations = self.deduplicate_citations(citations)
        
        logger.info(f"从图像文本中识别到 {len(unique_citations)} 个文献引用")
        return unique_citations
    
    def calculate_citation_confidence(self, citation_text: str) -> float:
        """计算文献引用的置信度"""
        confidence = 0.5  # 基础置信度
        
        # 检查是否包含年份
        if re.search(r'\b(19|20)\d{2}\b', citation_text):
            confidence += 0.2
        
        # 检查是否包含DOI
        if re.search(r'doi:', citation_text, re.IGNORECASE):
            confidence += 0.3
        
        # 检查是否包含期刊名称特征
        if re.search(r'\b(journal|proceedings|conference)\b', citation_text, re.IGNORECASE):
            confidence += 0.1
        
        # 检查长度合理性
        if 50 <= len(citation_text) <= 500:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def deduplicate_citations(self, citations: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """去除重复的文献引用"""
        unique_citations = []
        seen_citations = set()
        
        for citation in citations:
            # 创建唯一标识符
            citation_key = citation['full_match'].strip().lower()
            citation_key = re.sub(r'\s+', ' ', citation_key)
            
            if citation_key not in seen_citations:
                seen_citations.add(citation_key)
                unique_citations.append(citation)
        
        # 按置信度和位置排序
        unique_citations.sort(key=lambda x: (-x.get('confidence', 0), x['start_pos']))
        
        return unique_citations
    
    def extract_citations_from_screenshot(self, bbox: Optional[Tuple[int, int, int, int]] = None) -> List[Dict[str, str]]:
        """从截图中提取文献引用"""
        try:
            # 截取屏幕
            screenshot = self.capture_screenshot(bbox)
            if not screenshot:
                return []
            
            # 提取文本
            text = self.extract_text_from_image(screenshot)
            if not text:
                return []
            
            # 识别引用
            citations = self.find_citations_in_text(text)
            
            # 添加源信息
            for citation in citations:
                citation['source_type'] = 'screenshot'
                citation['bbox'] = bbox
            
            return citations
            
        except Exception as e:
            logger.error(f"从截图提取引用失败: {e}")
            return []
    
    def extract_citations_from_image_file(self, image_path: str) -> List[Dict[str, str]]:
        """从图像文件中提取文献引用"""
        try:
            # 加载图像
            image = self.load_image(image_path)
            if not image:
                return []
            
            # 提取文本
            text = self.extract_text_from_image(image)
            if not text:
                return []
            
            # 识别引用
            citations = self.find_citations_in_text(text)
            
            # 添加源信息
            for citation in citations:
                citation['source_type'] = 'image_file'
                citation['source_file'] = image_path
            
            return citations
            
        except Exception as e:
            logger.error(f"从图像文件提取引用失败: {e}")
            return []
    
    def select_screen_area(self) -> Optional[Tuple[int, int, int, int]]:
        """让用户选择屏幕区域进行截图"""
        try:
            # 创建一个临时的全屏窗口用于选择区域
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            # 这里可以实现一个区域选择工具
            # 为了简化，现在返回None表示全屏截图
            messagebox.showinfo("提示", "请手动截图并保存为图像文件，然后选择该文件进行识别")
            
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择截图文件",
                filetypes=[
                    ("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
                    ("所有文件", "*.*")
                ]
            )
            
            root.destroy()
            
            if file_path:
                # 返回文件路径而不是bbox
                return file_path
            
            return None
            
        except Exception as e:
            logger.error(f"选择屏幕区域失败: {e}")
            return None
