using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using BritSystem.Models;
using System.Diagnostics;

namespace BritSystem.Controls
{
    /// <summary>
    /// 矿物堆叠柱状图控件
    /// </summary>
    public class MineralStackedBarChartControl : UserControl
    {
        #region 私有字段

        private Chart _chart;
        private DataTable _resultData;
        private List<string> _brittleMinerals;
        private List<string> _ductileMinerals;
        private Dictionary<string, Color> _mineralColors;
        private TableLayoutPanel mainLayout;
        private TableLayoutPanel _legendPanel;

        // 图表缩放相关字段
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private const double MAX_ZOOM = 15.0;  // Y轴最大放大15倍
        private const double MAX_X_ZOOM = 3.0; // X轴最大放大3倍
        private const double MIN_ZOOM = 1.0;   // 最小不缩小
        private const double ZOOM_FACTOR = 1.2;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化矿物堆叠柱状图控件
        /// </summary>
        public MineralStackedBarChartControl()
        {
            InitializeComponent();
            InitializeDefaultColors();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取或设置结果数据
        /// </summary>
        public DataTable ResultData
        {
            get { return _resultData; }
            set
            {
                _resultData = value;
                if (_resultData != null)
                {
                    LogMessage($"结果数据已设置，行数: {_resultData.Rows.Count}");
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 获取或设置脆性矿物列表
        /// </summary>
        public List<string> BrittleMinerals
        {
            get { return _brittleMinerals; }
            set
            {
                _brittleMinerals = value ?? new List<string>(); // 防止null
                LogMessage($"脆性矿物列表已设置: {string.Join(", ", _brittleMinerals)}");
                if (_resultData != null && _ductileMinerals != null) // 双列表检查
                {
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 获取或设置塑性矿物列表
        /// </summary>
        public List<string> DuctileMinerals
        {
            get { return _ductileMinerals; }
            set
            {
                _ductileMinerals = value ?? new List<string>();
                LogMessage($"塑性矿物列表已设置: {string.Join(", ", _ductileMinerals)}");
                if (_resultData != null)
                {
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 获取或设置是否启用手动X轴设置
        /// </summary>
        public bool ManualXAxisEnabled { get; set; } = false;

        /// <summary>
        /// 获取或设置X轴最小值（手动模式）
        /// </summary>
        public double XAxisMinimum { get; set; } = 0;

        /// <summary>
        /// 获取或设置X轴最大值（手动模式）
        /// </summary>
        public double XAxisMaximum { get; set; } = 100;

        /// <summary>
        /// 获取或设置X轴间隔（手动模式）
        /// </summary>
        public double XAxisInterval { get; set; } = 20;

        /// <summary>
        /// 获取或设置X轴标题（手动模式）
        /// </summary>
        public string XAxisTitle { get; set; } = "矿物含量/%";

        /// <summary>
        /// 获取或设置是否启用手动Y轴设置
        /// </summary>
        public bool ManualYAxisEnabled { get; set; } = false;

        /// <summary>
        /// 获取或设置Y轴最小值（手动模式）
        /// </summary>
        public double YAxisMinimum { get; set; } = 0;

        /// <summary>
        /// 获取或设置Y轴最大值（手动模式）
        /// </summary>
        public double YAxisMaximum { get; set; } = 1000;

        /// <summary>
        /// 获取或设置Y轴间隔（手动模式）
        /// </summary>
        public double YAxisInterval { get; set; } = 100;

        /// <summary>
        /// 获取或设置Y轴标题（手动模式）
        /// </summary>
        public string YAxisTitle { get; set; } = "深度/m";

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置手动X轴参数
        /// </summary>
        /// <param name="minimum">最小值</param>
        /// <param name="maximum">最大值</param>
        /// <param name="interval">间隔</param>
        /// <param name="title">标题</param>
        public void SetManualXAxis(double minimum, double maximum, double interval, string title = "矿物含量/%")
        {
            ManualXAxisEnabled = true;
            XAxisMinimum = minimum;
            XAxisMaximum = maximum;
            XAxisInterval = interval;
            XAxisTitle = title;
            LogMessage($"手动X轴设置: {minimum}-{maximum}, 间隔:{interval}, 标题:{title}");

            if (_resultData != null)
            {
                UpdateChart();
            }
        }

        /// <summary>
        /// 设置手动Y轴参数（使用深度数据自动填充）
        /// </summary>
        /// <param name="title">Y轴标题</param>
        /// <param name="interval">Y轴间隔（可选，默认自动计算）</param>
        public void SetManualYAxisWithDepthData(string title = "深度/m", double? interval = null)
        {
            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                LogMessage("警告: 无法设置Y轴，数据为空");
                return;
            }

            // 获取深度数据
            List<double> depths = GetDepthData();
            if (depths.Count == 0)
            {
                LogMessage("警告: 无法设置Y轴，未找到深度数据");
                return;
            }

            ManualYAxisEnabled = true;
            YAxisMinimum = depths.Min();
            YAxisMaximum = depths.Max();
            YAxisTitle = title;

            if (interval.HasValue)
            {
                YAxisInterval = interval.Value;
            }
            else
            {
                // 自动计算间隔
                double range = YAxisMaximum - YAxisMinimum;
                YAxisInterval = Math.Max(100, range / 10);
            }

            LogMessage($"手动Y轴设置: {YAxisMinimum}-{YAxisMaximum}, 间隔:{YAxisInterval}, 标题:{YAxisTitle}");

            if (_resultData != null)
            {
                UpdateChart();
            }
        }

        /// <summary>
        /// 禁用手动坐标轴设置，恢复自动模式
        /// </summary>
        public void DisableManualAxis()
        {
            ManualXAxisEnabled = false;
            ManualYAxisEnabled = false;
            LogMessage("已禁用手动坐标轴设置，恢复自动模式");

            if (_resultData != null)
            {
                UpdateChart();
            }
        }



        /// <summary>
        /// 获取深度数据
        /// </summary>
        /// <returns>深度数据列表</returns>
        private List<double> GetDepthData()
        {
            List<double> depths = new List<double>();

            if (_resultData == null) return depths;

            try
            {
                depths = _resultData.Rows.Cast<DataRow>()
                    .Where(row => row.Table.Columns.Contains("顶深/m") && row["顶深/m"] != DBNull.Value)
                    .Select(row => Convert.ToDouble(row["顶深/m"]))
                    .Distinct()
                    .OrderBy(d => d)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogMessage($"处理深度数据时出错: {ex.Message}");
                // 如果没有顶深/m列，尝试其他可能的深度列名
                var depthColumns = new[] { "深度", "depth", "顶深", "井深", "md", "tvd" };
                foreach (var colName in depthColumns)
                {
                    if (_resultData.Columns.Contains(colName))
                    {
                        try
                        {
                            depths = _resultData.Rows.Cast<DataRow>()
                                .Where(row => row[colName] != DBNull.Value)
                                .Select(row => Convert.ToDouble(row[colName]))
                                .Distinct()
                                .OrderBy(d => d)
                                .ToList();
                            LogMessage($"使用列 '{colName}' 作为深度数据");
                            break;
                        }
                        catch
                        {
                            continue;
                        }
                    }
                }
            }

            return depths;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            // 初始化Chart控件
            _chart = new Chart();
            _chart.Dock = DockStyle.Fill;
            _chart.BackColor = Color.White;

            // 绑定鼠标事件
            _chart.MouseWheel += Chart_MouseWheel;
            _chart.MouseMove += Chart_MouseMove;
            _chart.MouseDown += Chart_MouseDown;
            _chart.MouseUp += Chart_MouseUp;

            mainLayout = new TableLayoutPanel();
            _legendPanel = new TableLayoutPanel();
            mainLayout.SuspendLayout();
            SuspendLayout();
            //
            // mainLayout - 修改为2行布局（移除坐标轴控制面板）
            //
            mainLayout.ColumnCount = 1;
            mainLayout.RowCount = 2;
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 80F));    // 图表区域
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 20F));    // 图例区域
            mainLayout.Controls.Add(_chart, 0, 0);
            mainLayout.Controls.Add(_legendPanel, 0, 1);
            mainLayout.Dock = DockStyle.Fill;
            mainLayout.Location = new Point(0, 0);
            mainLayout.Name = "mainLayout";
            mainLayout.TabIndex = 0;
            //
            // _legendPanel
            //
            _legendPanel.BackColor = Color.FromArgb(248, 248, 248);  // 灰白色背景
            _legendPanel.BorderStyle = BorderStyle.FixedSingle;
            _legendPanel.Padding = new Padding(10, 5, 10, 5);
            _legendPanel.AutoScroll = true;
            _legendPanel.Location = new Point(3, 93);
            _legendPanel.Name = "_legendPanel";
            _legendPanel.Size = new Size(194, 4);
            _legendPanel.TabIndex = 0;
            //
            // MineralStackedBarChartControl
            //
            Controls.Add(mainLayout);
            MinimumSize = new Size(1500, 1050);  // 增大到1.5倍 (1000*1.5, 700*1.5)
            Name = "MineralStackedBarChartControl";
            Size = new Size(3060, 1022);  // 增大到1.5倍 (2040*1.5, 681*1.5)
            mainLayout.ResumeLayout(false);
            ResumeLayout(false);
        }



        /// <summary>
        /// 初始化默认颜色
        /// </summary>
        private void InitializeDefaultColors()
        {
            _mineralColors = new Dictionary<string, Color>(StringComparer.OrdinalIgnoreCase)
            {
                { "黏土矿物总量", Color.FromArgb(153, 102, 51) },
                { "黏土矿物总量%", Color.FromArgb(153, 102, 51) }, // 添加带%的键
                { "黏土", Color.FromArgb(153, 102, 51) }, // 添加黏土键
                { "石英", Color.FromArgb(255, 51, 204) },
                { "石英%", Color.FromArgb(255, 51, 204) }, // 添加带%的键
                { "长石", Color.FromArgb(0, 204, 255) },
                { "长石%", Color.FromArgb(0, 204, 255) },
                { "方解石", Color.FromArgb(102, 255, 102) },
                { "方解石%", Color.FromArgb(102, 255, 102) },
                { "白云石", Color.FromArgb(102, 255, 102) },
                { "白云石%", Color.FromArgb(102, 255, 102) },
                { "碳酸盐矿物", Color.FromArgb(50, 180, 50) }, // 添加碳酸盐矿物键
                { "碳酸盐矿物%", Color.FromArgb(50, 180, 50) }, // 添加碳酸盐矿物%键
                { "铁矿", Color.FromArgb(128, 128, 128) },
                { "铁矿%", Color.FromArgb(128, 128, 128) },
                { "黑云母", Color.FromArgb(255, 255, 102) },
                { "黑云母%", Color.FromArgb(255, 255, 102) },
                { "角闪石", Color.FromArgb(102, 0, 102) },
                { "角闪石%", Color.FromArgb(102, 0, 102) }
            };

            LogMessage("默认矿物颜色已初始化");
        }

        /// <summary>
        /// 日志输出 - 不过滤，输出所有日志
        /// </summary>
        /// <param name="message">消息内容</param>
        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            string logEntry = $"[{timestamp}] MineralStackedBarChart: {message}";

            // 输出到VS调试输出窗口
            System.Diagnostics.Debug.WriteLine(logEntry);

            // 输出到VS输出窗口（通用）
            System.Diagnostics.Trace.WriteLine(logEntry);

            // 输出到控制台
            Console.WriteLine(logEntry);
        }



        /// <summary>
        /// 刷新图表
        /// </summary>
        public void RefreshChart()
        {
            LogMessage("刷新图表");
            UpdateChart();
        }

        /// <summary>
        /// 图例颜色方块点击事件
        /// </summary>
        private void ColorBox_Click(object sender, EventArgs e)
        {
            Panel colorBox = sender as Panel;
            if (colorBox != null && colorBox.Tag != null)
            {
                string mineral = colorBox.Tag.ToString();
                LogMessage($"点击了矿物颜色方块: {mineral}");

                // 显示颜色选择对话框
                ColorDialog colorDialog = new ColorDialog
                {
                    Color = colorBox.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    // 更新颜色
                    colorBox.BackColor = colorDialog.Color;
                    _mineralColors[mineral] = colorDialog.Color;
                    LogMessage($"更新矿物 '{mineral}' 颜色为: {colorDialog.Color}");

                    // 更新图表
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 更新图表
        /// </summary>
        private void UpdateChart()
        {
            LogMessage("===== 开始更新图表 =====");

            if (_chart == null)
            {
                LogMessage("错误: 图表控件未初始化");
                return;
            }

            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                LogMessage("错误: 结果数据为空或没有行");
                return;
            }

            if (_brittleMinerals == null || _ductileMinerals == null)
            {
                LogMessage("错误: 脆性或塑性矿物列表未设置");
                return;
            }

            // 清除现有元素
            _chart.Series.Clear();
            _chart.Titles.Clear();
            _chart.ChartAreas.Clear();
            _chart.Legends.Clear();
            LogMessage("已清除现有图表元素");

            // 添加标题
            _chart.Titles.Add(new Title("矿物含量分布", Docking.Top,
                new Font("Microsoft YaHei", 14, FontStyle.Bold), Color.Black));
            LogMessage("已添加图表标题");

            // 创建图表区域
            ChartArea chartArea = new ChartArea("MainArea");

            // 设置X轴为矿物含量百分比（水平轴）
            if (ManualXAxisEnabled)
            {
                // 使用手动设置的X轴参数
                chartArea.AxisX.Title = XAxisTitle;
                chartArea.AxisX.Minimum = XAxisMinimum;
                chartArea.AxisX.Maximum = XAxisMaximum;
                chartArea.AxisX.Interval = XAxisInterval;
                LogMessage($"使用手动X轴设置: {XAxisMinimum}-{XAxisMaximum}, 间隔:{XAxisInterval}");
            }
            else
            {
                // 使用默认的X轴设置
                chartArea.AxisX.Title = "矿物含量/%";
                chartArea.AxisX.Minimum = 0;
                chartArea.AxisX.Maximum = 100;
                chartArea.AxisX.Interval = 20;  // 每20%一个刻度（0, 20, 40, 60, 80, 100）
                LogMessage("使用默认X轴设置: 0-100%, 间隔:20%");
            }

            chartArea.AxisX.TitleFont = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisX.LabelStyle.Format = "F0";
            chartArea.AxisX.LabelStyle.Font = new Font("Microsoft YaHei", 9);
            chartArea.AxisX.IsMarginVisible = false;
            chartArea.AxisX.MajorGrid.Enabled = true;

            // 注意：这里不设置Y轴，Y轴将在后面正确设置为矿物含量轴

            chartArea.AxisY.TitleFont = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            chartArea.AxisY.IsReversed = false;  // 深度轴正常，小值在底部，大值在顶部
            chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisY.LabelStyle.Format = "F0";
            chartArea.AxisY.LabelStyle.Font = new Font("Microsoft YaHei", 9);
            chartArea.AxisY.IsMarginVisible = false;
            chartArea.AxisY.MajorGrid.Enabled = true;

            // 设置坐标轴位置和样式
            chartArea.Position = new ElementPosition(10, 5, 85, 85);
            chartArea.InnerPlotPosition = new ElementPosition(15, 5, 80, 90);

            // 移除图例（我们将使用自定义图例面板）
            _chart.Legends.Clear();

            _chart.ChartAreas.Add(chartArea);
            LogMessage("已创建图表区域");

            // 获取所有矿物列（使用原始列名）
            List<string> allMinerals = new List<string>();
            if (_brittleMinerals != null) allMinerals.AddRange(_brittleMinerals);
            if (_ductileMinerals != null) allMinerals.AddRange(_ductileMinerals);
            LogMessage($"所有矿物列表: {string.Join(", ", allMinerals)}");

            // 新增：获取数据表所有实际列名
            List<string> actualColumns = _resultData.Columns.Cast<DataColumn>()
                .Select(c => c.ColumnName)
                .ToList();
            LogMessage($"数据表实际列名: {string.Join(", ", actualColumns)}");

            // 处理深度数据
            List<double> depths = new List<double>();
            try
            {
                depths = _resultData.Rows.Cast<DataRow>()
                    .Where(row => row.Table.Columns.Contains("顶深/m") && row["顶深/m"] != DBNull.Value)
                    .Select(row => Convert.ToDouble(row["顶深/m"]))
                    .Distinct()
                    .OrderBy(d => d)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogMessage($"处理深度数据时出错: {ex.Message}");
                // 如果没有顶深/m列，尝试其他可能的深度列名
                var depthColumns = new[] { "深度", "depth", "顶深", "井深", "md", "tvd" };
                foreach (var colName in depthColumns)
                {
                    if (_resultData.Columns.Contains(colName))
                    {
                        try
                        {
                            depths = _resultData.Rows.Cast<DataRow>()
                                .Where(row => row[colName] != DBNull.Value)
                                .Select(row => Convert.ToDouble(row[colName]))
                                .Distinct()
                                .OrderBy(d => d)
                                .ToList();
                            LogMessage($"使用列 '{colName}' 作为深度数据");
                            break;
                        }
                        catch
                        {
                            continue;
                        }
                    }
                }
            }

            LogMessage($"找到 {depths.Count} 个深度点: {string.Join(", ", depths)}");

            // 只显示有数据的深度点
            if (depths.Count == 0)
            {
                LogMessage("警告: 未找到深度数据，添加默认深度1000");
                depths.Add(1000);
            }

            // 设置柱子宽度
            double pointWidth = 0.9; // 增加到0.9，使数据条更宽
            LogMessage($"柱子宽度设置为: {pointWidth}");

            // 遍历所有矿物（使用原始列名）
            foreach (string mineral in allMinerals)
            {
                // 提取纯净矿物名称用于显示（移除冒号及后续内容）
                string pureMineral = mineral.Contains(":")
                    ? mineral.Split(':')[0].Trim()
                    : mineral;

                LogMessage($"创建矿物系列 (显示名: {pureMineral}, 原始列名: {mineral})");

                // 增强：查找匹配的列名（多种匹配策略）
                string actualColumnName = actualColumns.FirstOrDefault(
                    col => col.Equals(mineral, StringComparison.OrdinalIgnoreCase) ||
                           col.Equals(pureMineral, StringComparison.OrdinalIgnoreCase) ||
                           col.Replace("%", "").Equals(mineral.Replace("%", ""), StringComparison.OrdinalIgnoreCase) ||
                           col.ToLower().Contains(pureMineral.ToLower().Replace("%", "")) ||
                           pureMineral.ToLower().Replace("%", "").Contains(col.ToLower().Replace("%", ""))
                );

                // 如果找不到对应列名，尝试使用原始列名（假设数据表使用了与属性不同的命名）
                if (string.IsNullOrEmpty(actualColumnName))
                {
                    LogMessage($"警告: 未找到与'{mineral}'匹配的列，尝试使用列名'{mineral}'本身");
                    // 判断是否需要继续使用该矿物
                    if (!actualColumns.Contains(mineral) && !actualColumns.Contains(pureMineral))
                    {
                        LogMessage($"错误: 数据表中找不到与'{mineral}'或'{pureMineral}'匹配的列，且列不存在");
                        continue; // 跳过这个矿物
                    }
                    actualColumnName = mineral; // 使用原始列名
                }

                LogMessage($"匹配到实际列名: {actualColumnName}");

                // 创建新系列
                Series series = new Series(pureMineral) // 使用纯矿物名作为系列名
                {
                    ChartType = SeriesChartType.StackedBar,  // 水平堆叠条形图（X轴=含量，Y轴=深度）
                    IsVisibleInLegend = false, // 不在默认图例中显示，使用自定义图例
                    BorderWidth = 1,  // 添加细边框
                    BorderColor = Color.White
                };

                LogMessage($"📊 创建系列: 名称='{pureMineral}', 图表类型='{series.ChartType}', 说明: 水平堆叠条形图");

                // 设置数据条宽度和间距
                series["PointWidth"] = "0.8";  // 数据条宽度
                series["PixelPointGapDepth"] = "2";  // 行间距

                // 设置颜色
                Color mineralColor = _mineralColors.ContainsKey(pureMineral)
                    ? _mineralColors[pureMineral]
                    : Color.FromArgb(
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256));

                series.Color = mineralColor;
                LogMessage($"设置矿物 '{pureMineral}' 颜色为: {mineralColor}");

                // 为每个深度添加数据点 - 确保所有系列都有相同的深度点
                LogMessage($"为矿物 '{pureMineral}' (实际列名: {actualColumnName}) 添加数据点...");

                // 为每个深度创建数据点，确保所有系列都有完整的深度序列
                for (int depthIndex = 0; depthIndex < depths.Count; depthIndex++)
                {
                    double depth = depths[depthIndex];
                    double value = 0;

                    // 查找匹配深度的行
                    DataRow[] depthRows = null;
                    try
                    {
                        if (_resultData.Columns.Contains("顶深/m"))
                        {
                            depthRows = _resultData.Select($"[顶深/m] = {depth}");
                        }
                        else
                        {
                            // 尝试其他深度列名
                            var depthColumns = new[] { "深度", "depth", "顶深", "井深", "md", "tvd" };
                            foreach (var colName in depthColumns)
                            {
                                if (_resultData.Columns.Contains(colName))
                                {
                                    depthRows = _resultData.Select($"[{colName}] = {depth}");
                                    break;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"查找深度 {depth} 的行时出错: {ex.Message}");
                        depthRows = new DataRow[0];
                    }

                    if (depthRows != null && depthRows.Length > 0)
                    {
                        // 尝试获取矿物含量值
                        if (depthRows[0].Table.Columns.Contains(actualColumnName) &&
                            depthRows[0][actualColumnName] != DBNull.Value)
                        {
                            try
                            {
                                value = Convert.ToDouble(depthRows[0][actualColumnName]);
                                LogMessage($"深度 {depth:F1}m 的矿物列 '{actualColumnName}' 含量: {value:F2}%");
                            }
                            catch (FormatException ex)
                            {
                                LogMessage($"警告: 列 '{actualColumnName}' 包含非数字值 '{depthRows[0][actualColumnName]}': {ex.Message}");
                                value = 0; // 使用0作为默认值
                            }
                        }
                        else
                        {
                            LogMessage($"警告: 深度 {depth:F1}m 找不到矿物列 '{actualColumnName}' 或值为空，使用0");
                            value = 0;
                        }
                    }
                    else
                    {
                        LogMessage($"警告: 深度 {depth:F1}m 没有匹配的数据行，使用0");
                        value = 0;
                    }

                    // 创建数据点 - 修正：对于StackedBar，X轴=深度分类，Y轴=矿物含量
                    DataPoint point = new DataPoint();

                    // 根据是否启用手动X轴设置来决定X坐标
                    double xValue;
                    if (ManualXAxisEnabled)
                    {
                        // 手动模式：使用实际深度值作为X坐标
                        xValue = depth;
                        LogMessage($"🔧 手动X轴模式: 使用实际深度值 {depth:F1}m 作为X坐标");
                    }
                    else
                    {
                        // 自动模式：使用深度索引作为X坐标
                        xValue = depthIndex;
                        LogMessage($"🔧 自动X轴模式: 使用深度索引 {depthIndex} 作为X坐标");
                    }

                    point.XValue = xValue; // X值根据模式选择
                    point.YValues = new double[] { value }; // Y值使用矿物含量百分比（0-100%）

                    LogMessage($"🔍 数据点详细信息: 矿物={pureMineral}, 深度={depth:F1}m(索引{depthIndex}), 含量={value:F2}%, 坐标(X={xValue}, Y={value})");

                    // 不显示数据标签
                    point.Label = "";
                    point.LabelForeColor = Color.Transparent;

                    // 添加悬停提示
                    point.ToolTip = $"深度: {depth:F0}m\n{pureMineral}: {value:F1}%";

                    // 添加数据点
                    LogMessage($"✅ 添加数据点: 深度={depth:F1}m(索引{depthIndex}), 含量={value:F2}% (坐标 X={xValue}, Y={value})");
                    series.Points.Add(point);
                }

                _chart.Series.Add(series);
                LogMessage($"✅ 完成矿物系列 '{pureMineral}' 的创建，共添加 {series.Points.Count} 个数据点");

                // 验证数据点范围
                if (series.Points.Count > 0)
                {
                    var xValues = series.Points.Select(p => p.XValue).ToArray();
                    var yValues = series.Points.Select(p => p.YValues[0]).ToArray();
                    LogMessage($"📊 系列 '{pureMineral}' 数据范围: X({xValues.Min():F1}-{xValues.Max():F1}), Y({yValues.Min():F1}-{yValues.Max():F1})");
                }
            }

            // 设置X轴为深度分类轴（对于StackedBar，X轴是分类轴）
            if (ManualXAxisEnabled)
            {
                // 使用手动设置的X轴参数（深度轴）
                chartArea.AxisX.Title = XAxisTitle;
                chartArea.AxisX.Minimum = XAxisMinimum;
                chartArea.AxisX.Maximum = XAxisMaximum;
                chartArea.AxisX.Interval = XAxisInterval;
                LogMessage($"🔧 使用手动X轴设置: {XAxisMinimum}-{XAxisMaximum}, 间隔:{XAxisInterval}");

                // 手动模式下不使用自定义标签，使用数值标签
                chartArea.AxisX.CustomLabels.Clear();
            }
            else if (depths.Count > 0)
            {
                // 使用默认的X轴设置（基于深度数据的分类轴）
                chartArea.AxisX.Minimum = -0.5;
                chartArea.AxisX.Maximum = depths.Count - 0.5;
                chartArea.AxisX.Interval = 1;
                chartArea.AxisX.Title = "深度 (m)";

                LogMessage($"🔧 X轴详细设置: Min={chartArea.AxisX.Minimum}, Max={chartArea.AxisX.Maximum}, Interval={chartArea.AxisX.Interval}, Title='{chartArea.AxisX.Title}'");

                // 设置自定义标签显示实际深度值 - 限制最多20个标签
                chartArea.AxisX.CustomLabels.Clear();
                int maxLabels = 20; // 最多显示20个标签
                int labelInterval = depths.Count <= maxLabels ? 1 : depths.Count / maxLabels;

                LogMessage($"🔧 深度标签设置: 总深度点={depths.Count}, 标签间隔={labelInterval}, 最大标签数={maxLabels}");

                for (int i = 0; i < depths.Count; i += labelInterval)
                {
                    chartArea.AxisX.CustomLabels.Add(i - 0.5, i + 0.5, $"{depths[i]:F0}m");
                    LogMessage($"🔧 X轴自定义标签[{i}]: 范围({i - 0.5}, {i + 0.5}) = '{depths[i]:F0}m'");
                }

                // 确保最后一个深度点也显示标签
                if (depths.Count > 1 && (depths.Count - 1) % labelInterval != 0)
                {
                    int lastIndex = depths.Count - 1;
                    chartArea.AxisX.CustomLabels.Add(lastIndex - 0.5, lastIndex + 0.5, $"{depths[lastIndex]:F0}m");
                    LogMessage($"🔧 X轴最后标签[{lastIndex}]: 范围({lastIndex - 0.5}, {lastIndex + 0.5}) = '{depths[lastIndex]:F0}m'");
                }

                LogMessage($"🔧 X轴设置完成，深度范围: {depths.Min():F0}m 到 {depths.Max():F0}m，共{depths.Count}个深度点");
            }

            chartArea.AxisX.LabelStyle.Angle = 0;
            chartArea.AxisX.MajorTickMark.Enabled = true;
            chartArea.AxisX.MajorTickMark.Size = 3;

            // 设置Y轴为矿物含量百分比（对于StackedBar，Y轴是数值轴）
            if (ManualYAxisEnabled)
            {
                // 使用手动设置的Y轴参数
                chartArea.AxisY.Title = YAxisTitle;
                chartArea.AxisY.Minimum = YAxisMinimum;
                chartArea.AxisY.Maximum = YAxisMaximum;
                chartArea.AxisY.Interval = YAxisInterval;
                LogMessage($"🔧 使用手动Y轴设置: {YAxisMinimum}-{YAxisMaximum}, 间隔:{YAxisInterval}");
            }
            else
            {
                // 使用默认的Y轴设置（矿物含量百分比）
                chartArea.AxisY.Minimum = 0;
                chartArea.AxisY.Maximum = 100;
                chartArea.AxisY.Interval = 20; // 每20%一个刻度：0%, 20%, 40%, 60%, 80%, 100%
                chartArea.AxisY.Title = "矿物含量 (%)";
                LogMessage("🔧 使用默认Y轴设置，矿物含量范围: 0% 到 100%，间隔: 20%");
            }

            chartArea.AxisY.LabelStyle.Angle = 0;
            chartArea.AxisY.MajorTickMark.Enabled = true;
            chartArea.AxisY.MajorTickMark.Size = 3;
            LogMessage($"🔧 Y轴详细设置: Min={chartArea.AxisY.Minimum}, Max={chartArea.AxisY.Maximum}, Interval={chartArea.AxisY.Interval}, Title='{chartArea.AxisY.Title}'");

            // 更新图例面板
            UpdateLegendPanel(allMinerals);

            // 最终验证
            LogMessage($"🔍 最终验证: 图表共有 {_chart.Series.Count} 个系列");
            int totalPoints = _chart.Series.Sum(s => s.Points.Count);
            LogMessage($"🔍 最终验证: 总共 {totalPoints} 个数据点");
            LogMessage($"🔍 最终验证: X轴范围 {chartArea.AxisX.Minimum}-{chartArea.AxisX.Maximum}");
            LogMessage($"🔍 最终验证: Y轴范围 {chartArea.AxisY.Minimum}-{chartArea.AxisY.Maximum}");

            LogMessage("===== 图表更新完成 =====");
        }

        /// <summary>
        /// 更新图例面板
        /// </summary>
        private void UpdateLegendPanel(List<string> minerals)
        {
            LogMessage($"更新图例面板，矿物数量: {minerals.Count}");

            _legendPanel.Controls.Clear();
            _legendPanel.ColumnStyles.Clear();
            _legendPanel.RowStyles.Clear();

            if (minerals.Count == 0)
            {
                LogMessage("没有矿物数据，跳过图例更新");
                return;
            }

            // 计算最佳列数 - 每行最多10个图例，水平均匀分布
            int maxColumnsPerRow = 10; // 每行最多10个图例
            int columns = Math.Min(minerals.Count, maxColumnsPerRow);

            _legendPanel.ColumnCount = columns;
            _legendPanel.RowCount = (int)Math.Ceiling((double)minerals.Count / columns);

            LogMessage($"图例布局: {columns}列 × {_legendPanel.RowCount}行 (最多每行{maxColumnsPerRow}个)");

            // 设置列宽 - 均分面板宽度
            for (int i = 0; i < columns; i++)
            {
                _legendPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f / columns));
            }

            // 设置行高 - 增大行高以确保文本显示
            float rowHeight = Math.Max(50F, 100F / _legendPanel.RowCount); // 最小50像素，最大100像素
            for (int i = 0; i < _legendPanel.RowCount; i++)
            {
                _legendPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, rowHeight));
            }

            // 添加图例项
            LogMessage("添加图例项...");
            for (int i = 0; i < minerals.Count; i++)
            {
                string mineral = minerals[i];
                LogMessage($"处理矿物图例: {mineral}");

                // 提取纯矿物列名（移除可能存在的数值部分）
                string pureMineral = mineral.Contains(":")
                    ? mineral.Split(':')[0].Trim()
                    : mineral;

                // 计算平均值
                double avgValue = 0;
                try
                {
                    var values = new List<double>();

                    foreach (DataRow dataRow in _resultData.Rows)
                    {
                        if (dataRow.Table.Columns.Contains(pureMineral) && dataRow[pureMineral] != DBNull.Value)
                        {
                            try
                            {
                                double val = Convert.ToDouble(dataRow[pureMineral]);
                                values.Add(val);
                            }
                            catch (FormatException ex)
                            {
                                LogMessage($"警告: 计算平均值时跳过非数字值 '{dataRow[pureMineral]}': {ex.Message}");
                            }
                        }
                    }

                    if (values.Count > 0)
                    {
                        avgValue = values.Average();
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"计算矿物 '{pureMineral}' 平均值时出错: {ex.Message}");
                }

                LogMessage($"矿物 '{pureMineral}' 平均含量: {avgValue:F2}%");

                Panel legendItem = new Panel
                {
                    Margin = new Padding(2, 2, 2, 2),
                    AutoSize = false,
                    Dock = DockStyle.Fill,
                    BackColor = Color.Transparent
                };

                Color mineralColor = Color.Black;
                if (_mineralColors.ContainsKey(pureMineral))
                {
                    mineralColor = _mineralColors[pureMineral];
                }
                else
                {
                    LogMessage($"警告: 矿物 '{pureMineral}' 未定义颜色，使用随机颜色");
                    mineralColor = Color.FromArgb(
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256));
                }

                Panel colorBox = new Panel
                {
                    BackColor = mineralColor,
                    Size = new Size(18, 18),
                    Location = new Point(5, (int)(rowHeight / 2) - 9), // 垂直居中
                    Tag = pureMineral,
                    Cursor = Cursors.Hand,
                    BorderStyle = BorderStyle.FixedSingle
                };
                colorBox.Click += ColorBox_Click;

                Label label = new Label
                {
                    Text = $"{pureMineral}: {avgValue:F0}%", // 使用纯净名称
                    AutoSize = false,
                    Dock = DockStyle.None,
                    Location = new Point(28, (int)(rowHeight / 2) - 9),  // 与颜色方块同一水平位置
                    Size = new Size(180, 18),  // 固定高度与颜色方块一致
                    Font = new Font("Microsoft YaHei", 8, FontStyle.Regular),  // 适中字体
                    ForeColor = Color.Black,
                    TextAlign = ContentAlignment.MiddleLeft,
                    BackColor = Color.Transparent,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top
                };

                legendItem.Controls.Add(colorBox);
                legendItem.Controls.Add(label);

                int row = i / columns;
                int col = i % columns;
                _legendPanel.Controls.Add(legendItem, col, row);

                LogMessage($"添加图例项: {mineral} 到位置 ({row}, {col})");
            }

            // 调整图例面板高度 - 根据行数和行高计算
            int calculatedHeight = (int)(_legendPanel.RowCount * rowHeight) + _legendPanel.Padding.Vertical + 20;
            int preferredHeight = Math.Max(calculatedHeight, 80);  // 最小80像素

            // 确保图例面板不会太高，但给予更多空间
            int maxHeight = this.Height / 3; // 最大不超过控件高度的1/3
            preferredHeight = Math.Min(preferredHeight, maxHeight);

            // 强制设置图例面板的Dock属性，确保占满宽度
            _legendPanel.Dock = DockStyle.Fill;
            _legendPanel.Height = preferredHeight;

            LogMessage($"图例面板高度设置为: {preferredHeight}px (行数: {_legendPanel.RowCount}, 行高: {rowHeight}px, 宽度: {_legendPanel.Width}px)");
        }

        #region 图表缩放和交互事件

        /// <summary>
        /// 图表鼠标滚轮事件 - 实现缩放功能
        /// </summary>
        private void Chart_MouseWheel(object sender, MouseEventArgs e)
        {
            try
            {
                if (_chart == null || _chart.ChartAreas.Count == 0)
                    return;

                var chartArea = _chart.ChartAreas[0];
                bool isZoomX = (ModifierKeys & Keys.Shift) == Keys.Shift; // Shift键缩放X轴
                double zoomFactor = e.Delta > 0 ? 1 / ZOOM_FACTOR : ZOOM_FACTOR; // 向上滚动缩小，向下滚动放大

                if (isZoomX)
                {
                    // X轴缩放
                    try
                    {
                        double newXZoom = currentXZoom * (e.Delta > 0 ? ZOOM_FACTOR : 1 / ZOOM_FACTOR);
                        newXZoom = Math.Min(Math.Max(newXZoom, MIN_ZOOM), MAX_X_ZOOM);

                        if (Math.Abs(newXZoom - currentXZoom) < 0.01) return;

                        // 获取鼠标位置对应的X轴值
                        double xValue = chartArea.AxisX.PixelPositionToValue(e.X);

                        // 计算新的显示范围
                        double currentRange = chartArea.AxisX.ScaleView.ViewMaximum - chartArea.AxisX.ScaleView.ViewMinimum;
                        double newRange = currentRange / (newXZoom / currentXZoom);
                        double newMin = xValue - (newRange / 2);
                        double newMax = xValue + (newRange / 2);

                        // 确保不超出数据范围
                        if (newMin < chartArea.AxisX.Minimum)
                        {
                            newMin = chartArea.AxisX.Minimum;
                            newMax = newMin + newRange;
                        }
                        if (newMax > chartArea.AxisX.Maximum)
                        {
                            newMax = chartArea.AxisX.Maximum;
                            newMin = newMax - newRange;
                        }

                        currentXZoom = newXZoom;
                        chartArea.AxisX.ScaleView.Zoom(newMin, newMax);
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"X轴缩放出错: {ex.Message}");
                        chartArea.AxisX.ScaleView.ZoomReset();
                    }
                }
                else
                {
                    // Y轴缩放
                    try
                    {
                        double newZoom = currentZoom * (e.Delta > 0 ? ZOOM_FACTOR : 1 / ZOOM_FACTOR);
                        newZoom = Math.Min(Math.Max(newZoom, MIN_ZOOM), MAX_ZOOM);

                        if (Math.Abs(newZoom - currentZoom) < 0.01) return;

                        // 获取鼠标位置对应的Y轴值
                        double yValue = chartArea.AxisY.PixelPositionToValue(e.Y);

                        // 计算新的显示范围
                        double currentRange = chartArea.AxisY.ScaleView.ViewMaximum - chartArea.AxisY.ScaleView.ViewMinimum;
                        double newRange = currentRange / (newZoom / currentZoom);
                        double newMin = yValue - (newRange / 2);
                        double newMax = yValue + (newRange / 2);

                        // 确保不超出数据范围
                        if (newMin < chartArea.AxisY.Minimum)
                        {
                            newMin = chartArea.AxisY.Minimum;
                            newMax = newMin + newRange;
                        }
                        if (newMax > chartArea.AxisY.Maximum)
                        {
                            newMax = chartArea.AxisY.Maximum;
                            newMin = newMax - newRange;
                        }

                        currentZoom = newZoom;
                        chartArea.AxisY.ScaleView.Zoom(newMin, newMax);
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"Y轴缩放出错: {ex.Message}");
                        chartArea.AxisY.ScaleView.ZoomReset();
                    }
                }

                // 强制重绘图表
                _chart.Invalidate();
            }
            catch (Exception ex)
            {
                LogMessage($"鼠标滚轮事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 图表鼠标移动事件
        /// </summary>
        private void Chart_MouseMove(object sender, MouseEventArgs e)
        {
            // 可以在这里添加鼠标移动时的处理逻辑
        }

        /// <summary>
        /// 图表鼠标按下事件
        /// </summary>
        private void Chart_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Middle)
            {
                // 中键重置视图
                ResetChartView();
            }
        }

        /// <summary>
        /// 图表鼠标释放事件
        /// </summary>
        private void Chart_MouseUp(object sender, MouseEventArgs e)
        {
            // 可以在这里添加鼠标释放时的处理逻辑
        }

        /// <summary>
        /// 重置图表视图
        /// </summary>
        private void ResetChartView()
        {
            try
            {
                if (_chart != null && _chart.ChartAreas.Count > 0)
                {
                    var chartArea = _chart.ChartAreas[0];
                    chartArea.AxisX.ScaleView.ZoomReset();
                    chartArea.AxisY.ScaleView.ZoomReset();
                    currentZoom = 1.0;
                    currentXZoom = 1.0;
                    _chart.Invalidate();
                    LogMessage("图表视图已重置");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"重置图表视图出错: {ex.Message}");
            }
        }

        #endregion
        #endregion
    }
}