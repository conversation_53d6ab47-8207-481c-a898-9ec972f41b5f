# 对比图分隔和调色功能实现说明

## 功能概述

为 `ComparisonChartForm`（对比图显示窗体）添加了以下新功能：

1. **分隔显示功能**：将两个系统的曲线图从中间隔开显示，类似于899.png图片中的效果
2. **还原显示功能**：恢复到原来的合并显示模式
3. **调色功能**：点击图例可以设置系列颜色

## 新增控件

### 按钮控件

- **btnSeparate**：分隔显示按钮
  - 文本："分隔显示"
  - 位置：(260, 12)
  - 功能：将两个系统的数据分别显示在左右两个独立的图表区域中

- **btnRestore**：还原显示按钮
  - 文本："还原显示"
  - 位置：(380, 12)
  - 功能：恢复到单一图表区域的对比显示模式
  - 初始状态：禁用

### 状态标记

- **isSeparated**：布尔变量，标记当前是否处于分隔状态

## 核心功能实现

### 1. 分隔显示功能 (`SeparateChartAreas()`)

**功能描述**：

- 创建两个独立的图表区域（左侧和右侧）
- 左侧显示矿物组分法数据
- 右侧显示静态岩石力学参数法数据
- 每个区域都有独立的坐标轴、标题和网格

**实现细节**：

```csharp
// 左侧图表区域（矿物组分法）
ChartArea leftArea = new ChartArea("LeftArea");
leftArea.Position = new ElementPosition(5, 10, 45, 80); // X, Y, Width, Height (百分比)

// 右侧图表区域（静态岩石力学参数法）
ChartArea rightArea = new ChartArea("RightArea");
rightArea.Position = new ElementPosition(50, 10, 45, 80);
```

**视觉效果**：

- 左侧区域：5%-50%的宽度，显示矿物组分法数据
- 右侧区域：50%-95%的宽度，显示静态岩石力学参数法数据
- 图例位置调整到右侧边缘

### 2. 还原显示功能 (`RestoreChartAreas()`)

**功能描述**：

- 恢复到单一图表区域
- 两个系统的数据在同一个图表中对比显示
- 重新计算坐标轴范围以适应所有数据

**实现细节**：

- 清除现有图表区域
- 创建单一的"ComparisonArea"图表区域
- 将所有系列重新分配到同一个区域
- 恢复图例的默认位置和停靠方式

### 3. 调色功能 (`ChartComparison_MouseClick()`)

**功能描述**：

- 检测鼠标点击是否在图例区域
- 弹出颜色选择对话框
- 更新对应系列的颜色
- 支持矿物组分法的标记颜色同步更新

**实现细节**：

```csharp
// 检查点击位置
HitTestResult result = chartComparison.HitTest(e.X, e.Y);
if (result.ChartElementType == ChartElementType.LegendItem)
{
    // 显示颜色选择对话框
    ColorDialog colorDialog = new ColorDialog();
    if (colorDialog.ShowDialog() == DialogResult.OK)
    {
        // 更新系列颜色
        targetSeries.Color = colorDialog.Color;
    }
}
```

## 辅助方法

### 1. `ConfigureChartArea(ChartArea chartArea, string title)`

- 统一配置图表区域的样式和属性
- 设置坐标轴标题、颜色、网格样式
- 启用缩放和滚动功能
- 添加图表标题

### 2. `RecalculateAxisRanges()`

- 重新计算所有图表区域的坐标轴范围
- 根据数据范围自动调整Y轴（深度）的最小值和最大值
- 添加10%的边距以提供更好的视觉效果
- 设置合适的间隔值

## 用户交互流程

### 分隔显示流程

1. 用户点击"分隔显示"按钮
2. 系统检查是否有至少两个系统的数据
3. 调用 `SeparateChartAreas()` 创建分隔视图
4. 禁用"分隔显示"按钮，启用"还原显示"按钮
5. 更新 `isSeparated` 状态为 `true`

### 还原显示流程

1. 用户点击"还原显示"按钮
2. 调用 `RestoreChartAreas()` 恢复合并视图
3. 启用"分隔显示"按钮，禁用"还原显示"按钮
4. 更新 `isSeparated` 状态为 `false`

### 调色流程

1. 用户点击图例中的某个系列项
2. 系统检测点击位置并识别对应的系列
3. 弹出颜色选择对话框
4. 用户选择新颜色并确认
5. 系统更新系列颜色并刷新图表

## 技术特点

### 1. 响应式布局

- 使用百分比定位确保在不同窗口大小下都能正确显示
- 图表区域自动适应窗口变化

### 2. 样式一致性

- 分隔模式下保持与原系统一致的样式
- 矿物组分法：蓝色线条 + 圆形数据点
- 静态岩石力学参数法：青色平滑曲线，无数据点

### 3. 用户体验优化

- 按钮状态智能切换，防止误操作
- 错误处理和用户提示
- 平滑的视觉过渡

## 问题修复

### 🔧 修复的问题

1. **标题错误修复**
   - **问题**：还原视图时报错 "Title cannot be positioned since the following chart area is missing: 'LeftArea'"
   - **原因**：分隔模式下创建的标题仍然绑定到已删除的图表区域
   - **解决方案**：在分隔和还原操作时清除所有现有标题 (`chartComparison.Titles.Clear()`)

2. **深度轴范围问题修复**
   - **问题**：分隔视图显示0-800m的固定范围，而不是基于实际数据
   - **原因**：使用了固定的坐标轴设置
   - **解决方案**：创建 `ConfigureChartAreaForSeparate()` 方法，根据实际数据范围设置坐标轴

3. **数据深度不一致问题修复**
   - **问题**：两个系统的数据深度范围可能不同（如700-800m vs 600-850m）
   - **解决方案**：实现智能深度范围统一算法
     - 计算每个系统的深度范围
     - 选择数据范围更大的系统作为基准
     - 统一应用到两个分隔的图表区域

### 🔍 新增算法

#### 统一深度范围算法 (`CalculateUnifiedDepthRange()`)

```csharp
// 1. 分别计算每个系统的深度范围
var mineralRange = CalculateSeriesDepthRange("矿物组分法");
var staticRange = CalculateSeriesDepthRange("静态岩石力学参数法");

// 2. 比较范围大小，选择更大的作为基准
double mineralSpan = mineralRange.max - mineralRange.min;
double staticSpan = staticRange.max - staticRange.min;

// 3. 应用统一范围到两个图表区域
if (mineralSpan >= staticSpan) {
    baseRange = mineralRange; // 使用矿物组分法的范围
} else {
    baseRange = staticRange;  // 使用静态岩石力学参数法的范围
}
```

**示例场景**：

- 矿物组分法数据：700-800m（范围100m）
- 静态岩石力学参数法数据：600-850m（范围250m）
- **结果**：两个分隔图表都使用600-850m的统一深度轴

## 编译状态

✅ **功能实现完成并编译成功**

- 0 个错误
- 392 个警告（主要是 nullable 相关警告，不影响功能）
- 所有问题已修复，新功能已集成到现有系统中

## 使用说明

1. **查看对比图**：在任一系统中点击"查看对比图"按钮
2. **分隔显示**：点击"分隔显示"按钮将两个系统的数据分开显示
3. **还原显示**：点击"还原显示"按钮恢复到合并对比模式
4. **调色功能**：点击右侧图例中的任意系列项，选择新颜色进行自定义
5. **保存图像**：点击"保存图像"按钮可以保存当前的对比图（包括分隔状态）

所有功能已完成实现并通过编译验证。
