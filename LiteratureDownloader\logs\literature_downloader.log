2025-07-02 16:30:34 | INFO | __main__:setup_logging:59 | 日志系统初始化完成
2025-07-02 16:30:34 | INFO | __main__:main:111 | 程序启动
2025-07-02 16:30:34 | WARNING | __main__:check_dependencies:98 | Tesseract OCR路径不存在: C:\Program Files\Tesseract-OCR\tesseract.exe
2025-07-02 16:30:34 | INFO | __main__:check_dependencies:99 | 请安装Tesseract OCR或在config/settings.py中配置正确路径
2025-07-02 16:30:34 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 16:30:34 | INFO | gui.main_window:setup_modules:79 | 所有模块初始化完成
2025-07-02 16:30:34 | INFO | __main__:main:130 | 启动GUI界面
2025-07-02 16:31:11 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 16:31:11 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 16:31:14 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 16:31:14 | INFO | modules.pdf_reader:find_citations:228 | 识别到 0 个文献引用
2025-07-02 16:31:14 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 0 个文献引用
2025-07-02 16:31:14 | ERROR | gui.main_window:recognize_pdf_citations:286 | PDF识别失败: module 'tkinter' has no attribute 'datetime'
2025-07-02 16:31:51 | INFO | __main__:on_closing:124 | 程序正在关闭
2025-07-02 16:31:51 | INFO | __main__:main:139 | 程序结束
2025-07-02 16:33:04 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 16:33:04 | INFO | __main__:main:112 | 程序启动
2025-07-02 16:33:04 | WARNING | __main__:check_dependencies:99 | Tesseract OCR路径不存在: C:\Program Files\Tesseract-OCR\tesseract.exe
2025-07-02 16:33:04 | INFO | __main__:check_dependencies:100 | 请安装Tesseract OCR或在config/settings.py中配置正确路径
2025-07-02 16:33:04 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 16:33:04 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 16:33:04 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 16:33:21 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 16:33:21 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 16:33:24 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 16:33:24 | INFO | modules.pdf_reader:find_citations:228 | 识别到 0 个文献引用
2025-07-02 16:33:24 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 0 个文献引用
2025-07-02 16:34:25 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/A physic-guided spatial temporal data ming.pdf
2025-07-02 16:34:25 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-20
2025-07-02 16:35:01 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 74548 字符
2025-07-02 16:35:01 | INFO | modules.pdf_reader:find_citations:228 | 识别到 0 个文献引用
2025-07-02 16:35:01 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 0 个文献引用
2025-07-02 16:36:09 | INFO | modules.ocr_reader:load_image:85 | 成功加载图像: C:/Users/<USER>/Desktop/截图.png, 尺寸: (1458, 888)
2025-07-02 16:36:09 | INFO | modules.ocr_reader:preprocess_image:140 | 图像预处理完成
2025-07-02 16:36:09 | ERROR | modules.ocr_reader:extract_text_from_image:167 | OCR文本提取失败: C:\Program Files\Tesseract-OCR\tesseract.exe is not installed or it's not in your PATH. See README file for more information.
2025-07-02 16:36:09 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 0 个文献引用
2025-07-02 16:58:04 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 16:58:04 | INFO | __main__:main:112 | 程序启动
2025-07-02 16:58:04 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 16:58:04 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 16:58:04 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 16:58:19 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 16:58:19 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 16:58:22 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 16:58:23 | INFO | modules.pdf_reader:find_citations:228 | 识别到 7 个文献引用
2025-07-02 16:58:23 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 7 个文献引用
2025-07-02 17:12:31 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 17:12:31 | INFO | __main__:main:112 | 程序启动
2025-07-02 17:12:31 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 17:12:31 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 17:12:31 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 17:12:42 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 17:12:42 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:12:45 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:12:45 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:12:46 | INFO | modules.pdf_reader:find_citations:235 | 识别到 89 个文献引用
2025-07-02 17:12:46 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 89 个文献引用
2025-07-02 17:13:27 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 17:13:27 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:13:30 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:13:30 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:13:31 | INFO | modules.pdf_reader:find_citations:235 | 识别到 89 个文献引用
2025-07-02 17:13:31 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 89 个文献引用
2025-07-02 17:14:20 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: F:/1-work/2025/2025-6/6-22/BritSystem/LiteratureDownloader/paper.pdf
2025-07-02 17:14:20 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:14:23 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:14:23 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:14:23 | INFO | modules.pdf_reader:find_citations:235 | 识别到 89 个文献引用
2025-07-02 17:14:23 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 89 个文献引用
2025-07-02 17:14:36 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 17:14:36 | INFO | __main__:main:112 | 程序启动
2025-07-02 17:14:36 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 17:14:36 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 17:14:36 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 17:15:11 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: F:/1-work/2025/2025-6/6-22/BritSystem/LiteratureDownloader/paper.pdf
2025-07-02 17:15:11 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:15:14 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:15:14 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:15:15 | INFO | modules.pdf_reader:find_citations:235 | 识别到 89 个文献引用
2025-07-02 17:15:15 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 89 个文献引用
2025-07-02 17:15:48 | INFO | modules.ocr_reader:load_image:85 | 成功加载图像: C:/Users/<USER>/Desktop/截图.png, 尺寸: (1458, 888)
2025-07-02 17:15:48 | INFO | modules.ocr_reader:preprocess_image:140 | 图像预处理完成
2025-07-02 17:15:49 | INFO | modules.ocr_reader:extract_text_from_image:163 | OCR识别完成，提取文本长度: 3506 字符
2025-07-02 17:15:49 | INFO | modules.ocr_reader:find_citations_in_text:236 | 从图像文本中识别到 0 个文献引用
2025-07-02 17:15:49 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 0 个文献引用
2025-07-02 17:23:24 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 17:23:24 | INFO | __main__:main:112 | 程序启动
2025-07-02 17:23:24 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 17:23:24 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 17:23:24 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 17:23:32 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 17:23:32 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:23:35 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:23:35 | INFO | modules.pdf_reader:extract_references_section:257 | 找到References部分，开始位置: 70151
2025-07-02 17:23:35 | INFO | modules.pdf_reader:extract_references_section:288 | 提取的References部分长度: 9460 字符
2025-07-02 17:23:35 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:23:36 | INFO | modules.pdf_reader:find_citations:235 | 识别到 59 个文献引用
2025-07-02 17:23:36 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 59 个文献引用
2025-07-02 17:23:53 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1111.pdf
2025-07-02 17:23:53 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-8
2025-07-02 17:23:54 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 21734 字符
2025-07-02 17:23:54 | INFO | modules.pdf_reader:extract_references_section:257 | 找到References部分，开始位置: 18627
2025-07-02 17:23:54 | INFO | modules.pdf_reader:extract_references_section:288 | 提取的References部分长度: 3095 字符
2025-07-02 17:23:54 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:23:54 | INFO | modules.pdf_reader:find_citations:235 | 识别到 40 个文献引用
2025-07-02 17:23:54 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 40 个文献引用
2025-07-02 17:24:26 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 17:24:26 | INFO | __main__:main:112 | 程序启动
2025-07-02 17:24:26 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 17:24:26 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 17:24:26 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 17:24:34 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 17:24:34 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:24:37 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:24:37 | INFO | modules.pdf_reader:extract_references_section:257 | 找到References部分，开始位置: 70151
2025-07-02 17:24:37 | INFO | modules.pdf_reader:extract_references_section:288 | 提取的References部分长度: 9460 字符
2025-07-02 17:24:37 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:24:38 | INFO | modules.pdf_reader:find_citations:235 | 识别到 59 个文献引用
2025-07-02 17:24:38 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 59 个文献引用
2025-07-02 17:24:50 | INFO | __main__:on_closing:125 | 程序正在关闭
2025-07-02 17:24:50 | INFO | __main__:main:140 | 程序结束
2025-07-02 17:39:34 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 17:39:34 | INFO | __main__:main:112 | 程序启动
2025-07-02 17:39:34 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 17:39:34 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 17:39:34 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 17:39:42 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 17:39:42 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 17:39:45 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 17:39:45 | INFO | modules.pdf_reader:extract_references_section:245 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 17:39:45 | INFO | modules.pdf_reader:extract_references_section:260 | 找到References标题，绝对位置: 70162
2025-07-02 17:39:45 | INFO | modules.pdf_reader:extract_references_section:301 | 提取的References部分长度: 9461 字符
2025-07-02 17:39:45 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 17:39:45 | INFO | modules.pdf_reader:parse_references_section:314 | 在References部分找到 52 个编号引用
2025-07-02 17:39:45 | INFO | modules.pdf_reader:parse_references_section:332 | 成功解析 52 个有效引用
2025-07-02 17:39:46 | INFO | modules.pdf_reader:find_citations:235 | 识别到 59 个文献引用
2025-07-02 17:39:46 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 59 个文献引用
2025-07-02 17:44:55 | INFO | __main__:on_closing:125 | 程序正在关闭
2025-07-02 17:44:55 | INFO | __main__:main:140 | 程序结束
2025-07-02 18:22:50 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 18:22:50 | INFO | __main__:main:112 | 程序启动
2025-07-02 18:22:50 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 18:22:50 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 18:22:50 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 18:22:58 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 18:22:58 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 18:23:01 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 18:23:01 | INFO | modules.pdf_reader:extract_references_section:245 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 18:23:01 | INFO | modules.pdf_reader:extract_references_section:260 | 找到References标题，绝对位置: 70162
2025-07-02 18:23:01 | INFO | modules.pdf_reader:extract_references_section:301 | 提取的References部分长度: 9461 字符
2025-07-02 18:23:01 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 18:23:01 | INFO | modules.pdf_reader:parse_references_section:314 | 在References部分找到 52 个编号引用
2025-07-02 18:23:01 | INFO | modules.pdf_reader:parse_references_section:332 | 成功解析 52 个有效引用
2025-07-02 18:23:02 | INFO | modules.pdf_reader:find_citations:235 | 识别到 59 个文献引用
2025-07-02 18:23:02 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 59 个文献引用
2025-07-02 18:23:02 | ERROR | gui.main_window:recognize_pdf_citations:289 | PDF识别失败: 'list' object has no attribute 'strip'
2025-07-02 18:30:24 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 18:30:24 | INFO | __main__:main:112 | 程序启动
2025-07-02 18:30:24 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 18:30:24 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 18:30:24 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 18:30:31 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 18:30:31 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 18:30:34 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 18:30:34 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 18:30:34 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-02 18:30:34 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-02 18:30:34 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 18:30:34 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-02 18:30:34 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-02 18:30:34 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-02 18:30:34 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 52 个文献引用
2025-07-02 18:30:34 | ERROR | gui.main_window:recognize_pdf_citations:289 | PDF识别失败: 'list' object has no attribute 'strip'
2025-07-02 18:30:56 | INFO | __main__:on_closing:125 | 程序正在关闭
2025-07-02 18:30:56 | INFO | __main__:main:140 | 程序结束
2025-07-02 18:37:50 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 18:37:50 | INFO | __main__:main:112 | 程序启动
2025-07-02 18:37:50 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 18:37:50 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 18:37:50 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 18:37:59 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 18:37:59 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 18:38:02 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 18:38:02 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 18:38:02 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-02 18:38:02 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-02 18:38:02 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 18:38:02 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-02 18:38:02 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-02 18:38:02 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-02 18:38:02 | INFO | modules.citation_parser:parse_multiple_citations:467 | 成功解析 52 个文献引用
2025-07-02 18:39:03 | INFO | __main__:on_closing:125 | 程序正在关闭
2025-07-02 18:39:04 | INFO | __main__:main:140 | 程序结束
2025-07-02 19:00:58 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:00:58 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:00:58 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:00:58 | INFO | gui.main_window:setup_modules:80 | 所有模块初始化完成
2025-07-02 19:00:58 | INFO | __main__:main:131 | 启动GUI界面
2025-07-02 19:01:06 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 19:01:06 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 19:01:09 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 19:01:09 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 19:01:09 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-02 19:01:09 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-02 19:01:09 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 19:01:09 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-02 19:01:09 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-02 19:01:09 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-02 19:01:09 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-02 19:11:41 | INFO | __main__:on_closing:125 | 程序正在关闭
2025-07-02 19:11:41 | INFO | __main__:main:140 | 程序结束
2025-07-02 19:24:43 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:24:43 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:24:43 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:25:21 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:25:21 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:25:21 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:26:55 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:26:55 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:26:55 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:39:37 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:39:37 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:39:37 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:42:15 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:42:15 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:42:15 | INFO | __main__:main:120 | 创建主窗口...
2025-07-02 19:42:15 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-02 19:42:15 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:48:07 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:48:07 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:48:07 | INFO | __main__:main:120 | 创建主窗口...
2025-07-02 19:48:07 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:73 | 开始初始化模块...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:75 | 初始化PDFReader...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:77 | PDFReader初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:79 | 初始化OCRReader...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:81 | OCRReader初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:83 | 初始化CitationParser...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:85 | CitationParser初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:87 | 初始化WebSearcher...
2025-07-02 19:48:07 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:89 | WebSearcher初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:91 | 初始化WOSSearcher...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:93 | WOSSearcher初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:95 | 初始化Downloader...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:97 | Downloader初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:99 | 初始化LiteratureMatcher...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:101 | LiteratureMatcher初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:103 | 初始化FileManager...
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:105 | FileManager初始化完成
2025-07-02 19:48:07 | INFO | gui.main_window:setup_modules:107 | 所有模块初始化完成
2025-07-02 19:48:07 | ERROR | __main__:main:145 | 程序运行出错: name 'search_group' is not defined
2025-07-02 19:48:10 | INFO | __main__:main:148 | 程序结束
2025-07-02 19:48:16 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:48:16 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:48:16 | INFO | __main__:main:120 | 创建主窗口...
2025-07-02 19:48:16 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:73 | 开始初始化模块...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:75 | 初始化PDFReader...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:77 | PDFReader初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:79 | 初始化OCRReader...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:81 | OCRReader初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:83 | 初始化CitationParser...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:85 | CitationParser初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:87 | 初始化WebSearcher...
2025-07-02 19:48:16 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:89 | WebSearcher初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:91 | 初始化WOSSearcher...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:93 | WOSSearcher初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:95 | 初始化Downloader...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:97 | Downloader初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:99 | 初始化LiteratureMatcher...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:101 | LiteratureMatcher初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:103 | 初始化FileManager...
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:105 | FileManager初始化完成
2025-07-02 19:48:16 | INFO | gui.main_window:setup_modules:107 | 所有模块初始化完成
2025-07-02 19:48:16 | ERROR | __main__:main:145 | 程序运行出错: name 'search_group' is not defined
2025-07-02 19:48:23 | INFO | __main__:main:148 | 程序结束
2025-07-02 19:52:54 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 19:52:54 | INFO | __main__:main:112 | 程序启动
2025-07-02 19:52:54 | INFO | __main__:main:120 | 创建主窗口...
2025-07-02 19:52:54 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:73 | 开始初始化模块...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:75 | 初始化PDFReader...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:77 | PDFReader初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:79 | 初始化OCRReader...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:81 | OCRReader初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:83 | 初始化CitationParser...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:85 | CitationParser初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:87 | 初始化WebSearcher...
2025-07-02 19:52:54 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:89 | WebSearcher初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:91 | 初始化WOSSearcher...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:93 | WOSSearcher初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:95 | 初始化Downloader...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:97 | Downloader初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:99 | 初始化LiteratureMatcher...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:101 | LiteratureMatcher初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:103 | 初始化FileManager...
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:105 | FileManager初始化完成
2025-07-02 19:52:54 | INFO | gui.main_window:setup_modules:107 | 所有模块初始化完成
2025-07-02 19:52:55 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-02 19:52:55 | INFO | __main__:main:139 | 启动GUI界面
2025-07-02 19:53:02 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 19:53:02 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 19:53:05 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 19:53:05 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 19:53:05 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-02 19:53:05 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-02 19:53:05 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 19:53:05 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-02 19:53:05 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-02 19:53:05 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-02 19:53:05 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-02 19:53:22 | INFO | modules.wos_searcher:_ensure_driver_initialized:82 | 首次使用，正在初始化WebDriver...
2025-07-02 20:01:13 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 20:01:13 | INFO | __main__:main:112 | 程序启动
2025-07-02 20:01:13 | INFO | __main__:main:120 | 创建主窗口...
2025-07-02 20:01:13 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:73 | 开始初始化模块...
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:75 | 初始化PDFReader...
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:77 | PDFReader初始化完成
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:79 | 初始化OCRReader...
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:81 | OCRReader初始化完成
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:83 | 初始化CitationParser...
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:85 | CitationParser初始化完成
2025-07-02 20:01:13 | INFO | gui.main_window:setup_modules:87 | 初始化WebSearcher...
2025-07-02 20:01:13 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:89 | WebSearcher初始化完成
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:91 | 初始化WOSSearcher...
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:93 | WOSSearcher初始化完成
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:95 | 初始化Downloader...
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:97 | Downloader初始化完成
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:99 | 初始化LiteratureMatcher...
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:101 | LiteratureMatcher初始化完成
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:103 | 初始化FileManager...
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:105 | FileManager初始化完成
2025-07-02 20:01:14 | INFO | gui.main_window:setup_modules:107 | 所有模块初始化完成
2025-07-02 20:01:14 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-02 20:01:14 | INFO | __main__:main:139 | 启动GUI界面
2025-07-02 20:01:21 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 20:01:21 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 20:01:25 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 20:01:25 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 20:01:25 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-02 20:01:25 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-02 20:01:25 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 20:01:25 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-02 20:01:25 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-02 20:01:25 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-02 20:01:25 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-02 20:02:00 | INFO | modules.wos_searcher:_ensure_driver_initialized:82 | 首次使用，正在初始化WebDriver...
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:setup_webdriver:74 | WebDriver初始化失败: Could not reach host. Are you offline?
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:setup_webdriver:75 | 请确保已安装Chrome浏览器和ChromeDriver
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:setup_webdriver:76 | 可以运行: pip install webdriver-manager
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:03:25 | ERROR | modules.wos_searcher:search_by_title:91 | WebDriver初始化失败
2025-07-02 20:16:33 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-02 20:16:33 | INFO | __main__:main:112 | 程序启动
2025-07-02 20:16:33 | INFO | __main__:main:120 | 创建主窗口...
2025-07-02 20:16:33 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:74 | 开始初始化模块...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:76 | 初始化PDFReader...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:78 | PDFReader初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:80 | 初始化OCRReader...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:82 | OCRReader初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:84 | 初始化CitationParser...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:86 | CitationParser初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:88 | 初始化WebSearcher...
2025-07-02 20:16:33 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:90 | WebSearcher初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:92 | 初始化BrowserHelper...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:94 | BrowserHelper初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:96 | 初始化WOSSearcher...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:98 | WOSSearcher初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:100 | 初始化Downloader...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:102 | Downloader初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:104 | 初始化LiteratureMatcher...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:106 | LiteratureMatcher初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:108 | 初始化FileManager...
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:110 | FileManager初始化完成
2025-07-02 20:16:33 | INFO | gui.main_window:setup_modules:112 | 所有模块初始化完成
2025-07-02 20:16:33 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-02 20:16:33 | INFO | __main__:main:139 | 启动GUI界面
2025-07-02 20:16:43 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-02 20:16:43 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-02 20:16:46 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-02 20:16:46 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-02 20:16:46 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-02 20:16:46 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-02 20:16:46 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-02 20:16:46 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-02 20:16:46 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-02 20:16:46 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-02 20:16:46 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-02 20:17:30 | INFO | __main__:on_closing:133 | 程序正在关闭
2025-07-02 20:17:30 | INFO | __main__:main:148 | 程序结束
2025-07-04 16:21:33 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 16:21:33 | INFO | __main__:main:112 | 程序启动
2025-07-04 16:21:33 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 16:21:34 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:75 | 开始初始化模块...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:77 | 初始化PDFReader...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:79 | PDFReader初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:81 | 初始化OCRReader...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:83 | OCRReader初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:85 | 初始化CitationParser...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:87 | CitationParser初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:89 | 初始化WebSearcher...
2025-07-04 16:21:34 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:91 | WebSearcher初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:93 | 初始化BrowserHelper...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:95 | BrowserHelper初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:97 | 初始化WOSSearcher...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:99 | WOSSearcher初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:101 | 初始化Downloader...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:103 | Downloader初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:105 | 初始化LiteratureMatcher...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:107 | LiteratureMatcher初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:109 | 初始化FileManager...
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:111 | FileManager初始化完成
2025-07-04 16:21:34 | INFO | gui.main_window:setup_modules:113 | 所有模块初始化完成
2025-07-04 16:21:34 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 16:21:34 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 16:21:56 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 16:21:56 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 16:22:00 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 16:22:00 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 16:22:00 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 16:22:00 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 16:22:00 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 16:22:00 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 16:22:00 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 16:22:00 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 16:22:00 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 16:22:16 | INFO | modules.auto_downloader:batch_download:335 | 开始批量下载，有效文献: 1 个
2025-07-04 16:22:16 | INFO | modules.auto_downloader:batch_download:342 | 处理第 1 批，共 1 个文献
2025-07-04 16:23:42 | ERROR | modules.auto_downloader:setup_driver:83 | WebDriver创建失败: Could not reach host. Are you offline?
2025-07-04 16:23:42 | ERROR | modules.auto_downloader:batch_download:351 | 无法创建第 1 个WebDriver实例
2025-07-04 16:23:42 | ERROR | modules.auto_downloader:batch_download:354 | 无法创建任何WebDriver实例
2025-07-04 16:23:42 | INFO | modules.auto_downloader:batch_download:390 | 批量下载完成: 总计1, 处理0, 下载0, 跳过0, 失败0
2025-07-04 16:24:29 | INFO | modules.auto_downloader:batch_download:335 | 开始批量下载，有效文献: 1 个
2025-07-04 16:24:29 | INFO | modules.auto_downloader:batch_download:342 | 处理第 1 批，共 1 个文献
2025-07-04 16:25:54 | ERROR | modules.auto_downloader:setup_driver:83 | WebDriver创建失败: Could not reach host. Are you offline?
2025-07-04 16:25:54 | ERROR | modules.auto_downloader:batch_download:351 | 无法创建第 1 个WebDriver实例
2025-07-04 16:25:54 | ERROR | modules.auto_downloader:batch_download:354 | 无法创建任何WebDriver实例
2025-07-04 16:25:54 | INFO | modules.auto_downloader:batch_download:390 | 批量下载完成: 总计1, 处理0, 下载0, 跳过0, 失败0
2025-07-04 16:43:50 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 16:43:50 | INFO | __main__:main:112 | 程序启动
2025-07-04 16:43:50 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 16:43:50 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:75 | 开始初始化模块...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:77 | 初始化PDFReader...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:79 | PDFReader初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:81 | 初始化OCRReader...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:83 | OCRReader初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:85 | 初始化CitationParser...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:87 | CitationParser初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:89 | 初始化WebSearcher...
2025-07-04 16:43:50 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:91 | WebSearcher初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:93 | 初始化BrowserHelper...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:95 | BrowserHelper初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:97 | 初始化WOSSearcher...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:99 | WOSSearcher初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:101 | 初始化Downloader...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:103 | Downloader初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:105 | 初始化LiteratureMatcher...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:107 | LiteratureMatcher初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:109 | 初始化FileManager...
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:111 | FileManager初始化完成
2025-07-04 16:43:50 | INFO | gui.main_window:setup_modules:113 | 所有模块初始化完成
2025-07-04 16:43:50 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 16:43:50 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 16:43:57 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 16:43:57 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 16:44:00 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 16:44:00 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 16:44:00 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 16:44:00 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 16:44:00 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 16:44:00 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 16:44:00 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 16:44:00 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 16:44:00 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 16:45:16 | WARNING | modules.simple_downloader:connect_to_existing_browser:43 | 无法连接到现有浏览器: Message: session not created: cannot connect to chrome at 127.0.0.1:9222
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff7f8636f95+76917]
	GetHandleVerifier [0x0x7ff7f8636ff0+77008]
	(No symbol) [0x0x7ff7f83e9c1c]
	(No symbol) [0x0x7ff7f83dada1]
	(No symbol) [0x0x7ff7f842cfd2]
	(No symbol) [0x0x7ff7f8421d99]
	(No symbol) [0x0x7ff7f8475e9e]
	(No symbol) [0x0x7ff7f8475630]
	(No symbol) [0x0x7ff7f8468243]
	(No symbol) [0x0x7ff7f8431431]
	(No symbol) [0x0x7ff7f84321c3]
	GetHandleVerifier [0x0x7ff7f890d2cd+3051437]
	GetHandleVerifier [0x0x7ff7f8907923+3028483]
	GetHandleVerifier [0x0x7ff7f89258bd+3151261]
	GetHandleVerifier [0x0x7ff7f865185e+185662]
	GetHandleVerifier [0x0x7ff7f865971f+218111]
	GetHandleVerifier [0x0x7ff7f863fb14+112628]
	GetHandleVerifier [0x0x7ff7f863fcc9+113065]
	GetHandleVerifier [0x0x7ff7f8626c98+10616]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 16:45:22 | INFO | modules.simple_downloader:create_new_browser:62 | 创建新浏览器成功
2025-07-04 16:45:40 | INFO | modules.simple_downloader:open_wos_and_search:97 | 搜索文献 [1] - 标题: TI="The spatial distribution and influencing factors of urban cultural and entertainment facilities in beijing"
2025-07-04 16:45:51 | ERROR | modules.simple_downloader:open_wos_and_search:115 | 搜索失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff7f8636f95+76917]
	GetHandleVerifier [0x0x7ff7f8636ff0+77008]
	(No symbol) [0x0x7ff7f83e9dea]
	(No symbol) [0x0x7ff7f8440256]
	(No symbol) [0x0x7ff7f844050c]
	(No symbol) [0x0x7ff7f8493887]
	(No symbol) [0x0x7ff7f84684af]
	(No symbol) [0x0x7ff7f849065c]
	(No symbol) [0x0x7ff7f8468243]
	(No symbol) [0x0x7ff7f8431431]
	(No symbol) [0x0x7ff7f84321c3]
	GetHandleVerifier [0x0x7ff7f890d2cd+3051437]
	GetHandleVerifier [0x0x7ff7f8907923+3028483]
	GetHandleVerifier [0x0x7ff7f89258bd+3151261]
	GetHandleVerifier [0x0x7ff7f865185e+185662]
	GetHandleVerifier [0x0x7ff7f865971f+218111]
	GetHandleVerifier [0x0x7ff7f863fb14+112628]
	GetHandleVerifier [0x0x7ff7f863fcc9+113065]
	GetHandleVerifier [0x0x7ff7f8626c98+10616]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 16:45:51 | WARNING | modules.simple_downloader:batch_download_simple:252 | 文献 [1] 搜索失败
2025-07-04 16:57:53 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 16:57:53 | INFO | __main__:main:112 | 程序启动
2025-07-04 16:57:53 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 16:57:53 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 16:57:53 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 16:57:53 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 16:57:53 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 16:57:53 | ERROR | __main__:main:145 | 程序运行出错: 'MainWindow' object has no attribute 'log_text'
2025-07-04 16:57:55 | INFO | __main__:main:148 | 程序结束
2025-07-04 16:58:18 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 16:58:18 | INFO | __main__:main:112 | 程序启动
2025-07-04 16:58:18 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 16:58:18 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 16:58:18 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 16:58:18 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 16:58:18 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 16:58:18 | ERROR | __main__:main:145 | 程序运行出错: 'MainWindow' object has no attribute 'log_text'
2025-07-04 16:58:38 | INFO | __main__:main:148 | 程序结束
2025-07-04 17:53:47 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 17:53:47 | INFO | __main__:main:112 | 程序启动
2025-07-04 17:53:47 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 17:53:47 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 17:53:47 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 17:53:47 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 17:53:47 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 17:53:47 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 17:53:47 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 17:53:54 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 17:53:54 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 17:53:57 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 17:53:57 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 17:53:57 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 17:53:57 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 17:53:57 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 17:53:57 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 17:53:57 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 17:53:57 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 17:53:57 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 17:54:04 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 17:54:20 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 17:56:14 | WARNING | modules.simple_downloader:connect_to_existing_browser:45 | 无法连接到现有浏览器: Message: session not created: cannot connect to chrome at 127.0.0.1:9222
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b6cada1]
	(No symbol) [0x0x7ff68b71cfd2]
	(No symbol) [0x0x7ff68b711d99]
	(No symbol) [0x0x7ff68b765e9e]
	(No symbol) [0x0x7ff68b765630]
	(No symbol) [0x0x7ff68b758243]
	(No symbol) [0x0x7ff68b721431]
	(No symbol) [0x0x7ff68b7221c3]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	GetHandleVerifier [0x0x7ff68b92fb14+112628]
	GetHandleVerifier [0x0x7ff68b92fcc9+113065]
	GetHandleVerifier [0x0x7ff68b916c98+10616]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 17:56:18 | INFO | modules.simple_downloader:create_new_browser:68 | 使用自定义浏览器: C:/Program Files (x86)/Google/Chrome/Application/chrome.exe
2025-07-04 17:56:20 | INFO | modules.simple_downloader:create_new_browser:71 | 创建新浏览器成功
2025-07-04 17:56:26 | INFO | modules.simple_downloader:open_wos_and_search:106 | 搜索文献 [1] - 标题: TI="The spatial distribution and influencing factors of urban cultural and entertainment facilities in beijing"
2025-07-04 17:56:37 | ERROR | modules.simple_downloader:open_wos_and_search:124 | 搜索失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9dea]
	(No symbol) [0x0x7ff68b730256]
	(No symbol) [0x0x7ff68b73050c]
	(No symbol) [0x0x7ff68b783887]
	(No symbol) [0x0x7ff68b7584af]
	(No symbol) [0x0x7ff68b78065c]
	(No symbol) [0x0x7ff68b758243]
	(No symbol) [0x0x7ff68b721431]
	(No symbol) [0x0x7ff68b7221c3]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	GetHandleVerifier [0x0x7ff68b92fb14+112628]
	GetHandleVerifier [0x0x7ff68b92fcc9+113065]
	GetHandleVerifier [0x0x7ff68b916c98+10616]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 17:56:37 | WARNING | modules.simple_downloader:batch_download_simple:261 | 文献 [1] 搜索失败
2025-07-04 18:03:45 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 18:03:45 | INFO | __main__:main:112 | 程序启动
2025-07-04 18:03:45 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 18:03:45 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 18:03:45 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 18:03:45 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 18:03:45 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 18:03:45 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 18:03:45 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 18:03:56 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 18:03:56 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 18:03:59 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 18:03:59 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 18:03:59 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 18:03:59 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 18:03:59 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 18:03:59 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 18:03:59 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 18:03:59 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 18:03:59 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 18:04:06 | INFO | modules.smart_search_helper:create_browser:46 | 使用自定义浏览器: C:/Program Files (x86)/Google/Chrome/Application/chrome.exe
2025-07-04 18:04:07 | INFO | modules.smart_search_helper:create_browser:53 | 浏览器创建成功
2025-07-04 18:11:56 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 18:11:56 | INFO | __main__:main:112 | 程序启动
2025-07-04 18:11:56 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 18:11:56 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 18:11:56 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 18:11:56 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 18:11:56 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 18:11:56 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 18:11:57 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 18:12:02 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 18:12:02 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 18:12:05 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 18:12:05 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 18:12:05 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 18:12:05 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 18:12:05 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 18:12:05 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 18:12:05 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 18:12:05 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 18:12:05 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 18:12:39 | INFO | modules.smart_search_helper:create_browser:56 | 使用自定义浏览器: C:/Program Files (x86)/Google/Chrome/Application/chrome.exe
2025-07-04 18:12:41 | INFO | modules.smart_search_helper:create_browser:63 | 浏览器创建成功
2025-07-04 18:12:41 | INFO | modules.smart_search_helper:auto_search_and_download:86 | 开始处理文献 [1]
2025-07-04 18:12:46 | INFO | modules.smart_search_helper:_search_literature:146 | 文献 [1] 搜索查询: spatial distribution influencing factors
2025-07-04 18:12:53 | INFO | modules.smart_search_helper:_fill_search_box:221 | 成功填写搜索框: input[type="text"]
2025-07-04 18:12:58 | WARNING | modules.smart_search_helper:_has_search_results:274 | 未找到搜索结果
2025-07-04 18:12:58 | WARNING | modules.smart_search_helper:auto_search_and_download:107 | 文献 [1] 搜索失败
2025-07-04 18:13:00 | INFO | modules.smart_search_helper:auto_search_and_download:86 | 开始处理文献 [2]
2025-07-04 18:13:05 | INFO | modules.smart_search_helper:_search_literature:146 | 文献 [2] 搜索查询: social media medium promote
2025-07-04 18:13:11 | INFO | modules.smart_search_helper:_fill_search_box:221 | 成功填写搜索框: input[type="text"]
2025-07-04 18:13:16 | WARNING | modules.smart_search_helper:_has_search_results:274 | 未找到搜索结果
2025-07-04 18:13:16 | WARNING | modules.smart_search_helper:auto_search_and_download:107 | 文献 [2] 搜索失败
2025-07-04 18:13:51 | INFO | __main__:on_closing:133 | 程序正在关闭
2025-07-04 18:13:51 | INFO | __main__:main:148 | 程序结束
2025-07-04 18:22:36 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 18:22:36 | INFO | __main__:main:112 | 程序启动
2025-07-04 18:22:36 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 18:22:36 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 18:22:36 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 18:22:36 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 18:22:36 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 18:22:36 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 18:22:36 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 18:22:43 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 18:22:43 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 18:22:46 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 18:22:46 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 18:22:46 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 18:22:46 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 18:22:46 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 18:22:46 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 18:22:46 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 18:22:46 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 18:22:46 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 18:22:59 | INFO | modules.smart_search_helper:create_browser:70 | 使用自定义浏览器: C:/Program Files (x86)/Google/Chrome/Application/chrome.exe
2025-07-04 18:23:01 | ERROR | modules.captcha_handler:<module>:21 | 依赖库导入失败: No module named 'pyautogui'
2025-07-04 18:23:01 | INFO | modules.smart_search_helper:create_browser:86 | 验证码处理器初始化成功
2025-07-04 18:23:01 | INFO | modules.smart_search_helper:create_browser:90 | 浏览器创建成功
2025-07-04 18:23:01 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [1]
2025-07-04 18:23:08 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [1] 搜索查询: spatial distribution influencing factors
2025-07-04 18:23:14 | INFO | modules.smart_search_helper:_fill_search_box:258 | 成功填写搜索框: input[type="text"]
2025-07-04 18:23:19 | WARNING | modules.smart_search_helper:_has_search_results:311 | 未找到搜索结果
2025-07-04 18:23:19 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [1] 搜索失败
2025-07-04 18:23:21 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [2]
2025-07-04 18:23:26 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [2] 搜索查询: social media medium promote
2025-07-04 18:23:32 | INFO | modules.smart_search_helper:_fill_search_box:258 | 成功填写搜索框: input[type="text"]
2025-07-04 18:23:37 | WARNING | modules.smart_search_helper:_has_search_results:311 | 未找到搜索结果
2025-07-04 18:23:37 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [2] 搜索失败
2025-07-04 18:23:49 | INFO | __main__:on_closing:133 | 程序正在关闭
2025-07-04 18:23:49 | INFO | __main__:main:148 | 程序结束
2025-07-04 18:23:55 | INFO | __main__:setup_logging:60 | 日志系统初始化完成
2025-07-04 18:23:55 | INFO | __main__:main:112 | 程序启动
2025-07-04 18:23:55 | INFO | __main__:main:120 | 创建主窗口...
2025-07-04 18:23:55 | INFO | __main__:main:122 | 创建MainWindow...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:82 | 开始初始化模块...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:84 | 初始化PDFReader...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:86 | PDFReader初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:88 | 初始化OCRReader...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:90 | OCRReader初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:92 | 初始化CitationParser...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:94 | CitationParser初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:96 | 初始化WebSearcher...
2025-07-04 18:23:55 | INFO | modules.web_searcher:setup_webdriver:60 | WebDriver配置完成（未启动）
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:98 | WebSearcher初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:100 | 初始化BrowserHelper...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:102 | BrowserHelper初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:104 | 初始化WOSSearcher...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:106 | WOSSearcher初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:108 | 初始化Downloader...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:110 | Downloader初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:112 | 初始化LiteratureMatcher...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:114 | LiteratureMatcher初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:116 | 初始化FileManager...
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:118 | FileManager初始化完成
2025-07-04 18:23:55 | INFO | gui.main_window:setup_modules:120 | 所有模块初始化完成
2025-07-04 18:23:55 | INFO | modules.config_manager:save_config:63 | 配置保存成功
2025-07-04 18:23:55 | INFO | __main__:main:124 | MainWindow创建完成
2025-07-04 18:23:55 | INFO | __main__:main:139 | 启动GUI界面
2025-07-04 18:24:02 | INFO | modules.pdf_reader:extract_text:150 | 开始提取PDF文本: C:/Users/<USER>/Desktop/数据挖掘2025文章，每人一篇，可自己找/1-s2.0-S2405844025011399-main.pdf
2025-07-04 18:24:02 | INFO | modules.pdf_reader:extract_text_pdfplumber:84 | 使用pdfplumber提取PDF文本，页面范围: 0-18
2025-07-04 18:24:05 | INFO | modules.pdf_reader:extract_text:169 | 成功提取文本，长度: 79623 字符
2025-07-04 18:24:05 | INFO | modules.pdf_reader:extract_references_section:252 | 在文档最后30%部分查找References (从位置 55736 开始)
2025-07-04 18:24:05 | INFO | modules.pdf_reader:extract_references_section:267 | 找到References标题，绝对位置: 70162
2025-07-04 18:24:05 | INFO | modules.pdf_reader:extract_references_section:308 | 提取的References部分长度: 9461 字符
2025-07-04 18:24:05 | INFO | modules.pdf_reader:find_citations:182 | 找到References部分，专门分析该部分
2025-07-04 18:24:05 | INFO | modules.pdf_reader:parse_references_section:321 | 在References部分找到 52 个编号引用
2025-07-04 18:24:05 | INFO | modules.pdf_reader:parse_references_section:339 | 成功解析 52 个有效引用
2025-07-04 18:24:05 | INFO | modules.pdf_reader:find_citations:187 | References部分识别到 52 个引用，跳过全文搜索
2025-07-04 18:24:05 | INFO | modules.citation_parser:parse_multiple_citations:496 | 成功解析 52 个文献引用
2025-07-04 18:24:09 | INFO | modules.smart_search_helper:create_browser:70 | 使用自定义浏览器: C:/Program Files (x86)/Google/Chrome/Application/chrome.exe
2025-07-04 18:24:11 | ERROR | modules.captcha_handler:<module>:21 | 依赖库导入失败: No module named 'pyautogui'
2025-07-04 18:24:11 | INFO | modules.smart_search_helper:create_browser:86 | 验证码处理器初始化成功
2025-07-04 18:24:11 | INFO | modules.smart_search_helper:create_browser:90 | 浏览器创建成功
2025-07-04 18:24:11 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [1]
2025-07-04 18:24:17 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [1] 搜索查询: spatial distribution influencing factors
2025-07-04 18:24:23 | INFO | modules.smart_search_helper:_fill_search_box:258 | 成功填写搜索框: input[type="text"]
2025-07-04 18:24:29 | WARNING | modules.smart_search_helper:_has_search_results:311 | 未找到搜索结果
2025-07-04 18:24:29 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [1] 搜索失败
2025-07-04 18:24:31 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [2]
2025-07-04 18:24:35 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [2] 搜索查询: social media medium promote
2025-07-04 18:24:42 | INFO | modules.smart_search_helper:_fill_search_box:258 | 成功填写搜索框: input[type="text"]
2025-07-04 18:24:47 | WARNING | modules.smart_search_helper:_has_search_results:311 | 未找到搜索结果
2025-07-04 18:24:47 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [2] 搜索失败
2025-07-04 18:24:49 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [3]
2025-07-04 18:24:54 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [3] 搜索查询: activate inner drive cultural
2025-07-04 18:25:01 | INFO | modules.smart_search_helper:_fill_search_box:258 | 成功填写搜索框: input[type="text"]
2025-07-04 18:25:06 | WARNING | modules.smart_search_helper:_has_search_results:311 | 未找到搜索结果
2025-07-04 18:25:06 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [3] 搜索失败
2025-07-04 18:25:08 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [4]
2025-07-04 18:25:08 | INFO | modules.smart_search_helper:auto_search_and_download:118 | 跳过文献 [4]: 信息不足
2025-07-04 18:25:08 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [5]
2025-07-04 18:25:13 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [5] 搜索查询: china social sciences press
2025-07-04 18:25:19 | INFO | modules.smart_search_helper:_fill_search_box:258 | 成功填写搜索框: input[type="text"]
2025-07-04 18:25:24 | WARNING | modules.smart_search_helper:_has_search_results:311 | 未找到搜索结果
2025-07-04 18:25:24 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [5] 搜索失败
2025-07-04 18:25:26 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [6]
2025-07-04 18:25:30 | INFO | modules.smart_search_helper:_search_literature:181 | 文献 [6] 搜索查询: china social sciences press
2025-07-04 18:25:30 | ERROR | modules.smart_search_helper:_fill_search_box:264 | 未找到可用的搜索框
2025-07-04 18:25:30 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [6] 搜索失败
2025-07-04 18:25:32 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [7]
2025-07-04 18:25:32 | ERROR | modules.smart_search_helper:_search_literature:194 | 搜索文献 [7] 失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b72055f]
	(No symbol) [0x0x7ff68b758332]
	(No symbol) [0x0x7ff68b752e53]
	(No symbol) [0x0x7ff68b751f19]
	(No symbol) [0x0x7ff68b6a4b05]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	(No symbol) [0x0x7ff68b6a3b00]
	GetHandleVerifier [0x0x7ff68bd15f38+4201496]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 18:25:32 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [7] 搜索失败
2025-07-04 18:25:34 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [8]
2025-07-04 18:25:34 | ERROR | modules.smart_search_helper:_search_literature:194 | 搜索文献 [8] 失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b72055f]
	(No symbol) [0x0x7ff68b758332]
	(No symbol) [0x0x7ff68b752e53]
	(No symbol) [0x0x7ff68b751f19]
	(No symbol) [0x0x7ff68b6a4b05]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	(No symbol) [0x0x7ff68b6a3b00]
	GetHandleVerifier [0x0x7ff68bd15f38+4201496]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 18:25:34 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [8] 搜索失败
2025-07-04 18:25:36 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [9]
2025-07-04 18:25:36 | ERROR | modules.smart_search_helper:_search_literature:194 | 搜索文献 [9] 失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b72055f]
	(No symbol) [0x0x7ff68b758332]
	(No symbol) [0x0x7ff68b752e53]
	(No symbol) [0x0x7ff68b751f19]
	(No symbol) [0x0x7ff68b6a4b05]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	(No symbol) [0x0x7ff68b6a3b00]
	GetHandleVerifier [0x0x7ff68bd15f38+4201496]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 18:25:36 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [9] 搜索失败
2025-07-04 18:25:38 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [10]
2025-07-04 18:25:38 | ERROR | modules.smart_search_helper:_search_literature:194 | 搜索文献 [10] 失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b72055f]
	(No symbol) [0x0x7ff68b758332]
	(No symbol) [0x0x7ff68b752e53]
	(No symbol) [0x0x7ff68b751f19]
	(No symbol) [0x0x7ff68b6a4b05]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	(No symbol) [0x0x7ff68b6a3b00]
	GetHandleVerifier [0x0x7ff68bd15f38+4201496]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 18:25:38 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [10] 搜索失败
2025-07-04 18:25:40 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [11]
2025-07-04 18:25:40 | ERROR | modules.smart_search_helper:_search_literature:194 | 搜索文献 [11] 失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b72055f]
	(No symbol) [0x0x7ff68b758332]
	(No symbol) [0x0x7ff68b752e53]
	(No symbol) [0x0x7ff68b751f19]
	(No symbol) [0x0x7ff68b6a4b05]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	(No symbol) [0x0x7ff68b6a3b00]
	GetHandleVerifier [0x0x7ff68bd15f38+4201496]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 18:25:40 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [11] 搜索失败
2025-07-04 18:25:42 | INFO | modules.smart_search_helper:auto_search_and_download:113 | 开始处理文献 [12]
2025-07-04 18:25:42 | ERROR | modules.smart_search_helper:_search_literature:194 | 搜索文献 [12] 失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff68b926f95+76917]
	GetHandleVerifier [0x0x7ff68b926ff0+77008]
	(No symbol) [0x0x7ff68b6d9c1c]
	(No symbol) [0x0x7ff68b72055f]
	(No symbol) [0x0x7ff68b758332]
	(No symbol) [0x0x7ff68b752e53]
	(No symbol) [0x0x7ff68b751f19]
	(No symbol) [0x0x7ff68b6a4b05]
	GetHandleVerifier [0x0x7ff68bbfd2cd+3051437]
	GetHandleVerifier [0x0x7ff68bbf7923+3028483]
	GetHandleVerifier [0x0x7ff68bc158bd+3151261]
	GetHandleVerifier [0x0x7ff68b94185e+185662]
	GetHandleVerifier [0x0x7ff68b94971f+218111]
	(No symbol) [0x0x7ff68b6a3b00]
	GetHandleVerifier [0x0x7ff68bd15f38+4201496]
	BaseThreadInitThunk [0x0x7ffbdb0de8d7+23]
	RtlUserThreadStart [0x0x7ffbdc1dc34c+44]

2025-07-04 18:25:42 | WARNING | modules.smart_search_helper:auto_search_and_download:134 | 文献 [12] 搜索失败
