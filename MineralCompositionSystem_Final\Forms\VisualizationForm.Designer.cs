namespace BritSystem
{
    partial class VisualizationForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            tabPage1 = new TabPage();
            chartPie = new System.Windows.Forms.DataVisualization.Charting.Chart();
            panelDepthControl = new Panel();
            trackBarDepth = new TrackBar();
            lblCurrentDepth = new Label();
            lblDepth = new Label();
            tabPage2 = new TabPage();
            panelStackedChart = new Panel();
            panelAxisControls = new Panel();
            btnResetSettings = new Button();
            btnApplySettings = new Button();
            grpXAxis = new GroupBox();
            lblSearchColumn = new Label();
            cboSearchColumn = new ComboBox();
            lblMinValue = new Label();
            txtMinValue = new TextBox();
            lblMaxValue = new Label();
            txtMaxValue = new TextBox();
            btnSearch = new Button();
            btnResetData = new Button();
            lblXUnit = new Label();
            lblXInterval = new Label();
            numXInterval = new NumericUpDown();
            lblXMax = new Label();
            numXMax = new NumericUpDown();
            lblXMin = new Label();
            numXMin = new NumericUpDown();
            chkXAutoRange = new CheckBox();
            tabControl1.SuspendLayout();
            tabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)chartPie).BeginInit();
            panelDepthControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarDepth).BeginInit();
            tabPage2.SuspendLayout();
            panelAxisControls.SuspendLayout();
            grpXAxis.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numXInterval).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numXMax).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numXMin).BeginInit();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(tabPage1);
            tabControl1.Controls.Add(tabPage2);
            tabControl1.Dock = DockStyle.Fill;
            tabControl1.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Margin = new Padding(4, 4, 4, 4);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(1925, 1350);
            tabControl1.TabIndex = 0;
            // 
            // tabPage1
            //
            tabPage1.BackColor = Color.White;
            tabPage1.Controls.Add(chartPie);
            tabPage1.Controls.Add(panelDepthControl);
            tabPage1.Location = new Point(4, 36);
            tabPage1.Margin = new Padding(4, 4, 4, 4);
            tabPage1.Name = "tabPage1";
            tabPage1.Padding = new Padding(4, 4, 4, 4);
            tabPage1.Size = new Size(1917, 1310);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "脆性/塑性矿物比例";
            //
            // chartPie
            //
            chartPie.BackColor = Color.White;
            chartPie.Dock = DockStyle.Fill;
            chartPie.Location = new Point(4, 127);
            chartPie.Margin = new Padding(4, 4, 4, 4);
            chartPie.Name = "chartPie";
            chartPie.Size = new Size(1909, 1179);
            chartPie.TabIndex = 1;
            // 
            // panelDepthControl
            // 
            panelDepthControl.BackColor = Color.FromArgb(240, 240, 240);
            panelDepthControl.BorderStyle = BorderStyle.FixedSingle;
            panelDepthControl.Controls.Add(trackBarDepth);
            panelDepthControl.Controls.Add(lblCurrentDepth);
            panelDepthControl.Controls.Add(lblDepth);
            panelDepthControl.Dock = DockStyle.Top;
            panelDepthControl.Location = new Point(4, 4);
            panelDepthControl.Margin = new Padding(4, 4, 4, 4);
            panelDepthControl.Name = "panelDepthControl";
            panelDepthControl.Size = new Size(1909, 119);
            panelDepthControl.TabIndex = 0;
            // 
            // trackBarDepth
            // 
            trackBarDepth.LargeChange = 10;
            trackBarDepth.Location = new Point(165, 15);
            trackBarDepth.Margin = new Padding(4, 4, 4, 4);
            trackBarDepth.Maximum = 100;
            trackBarDepth.Name = "trackBarDepth";
            trackBarDepth.Size = new Size(550, 69);
            trackBarDepth.TabIndex = 2;
            trackBarDepth.TickFrequency = 10;
            // 
            // lblCurrentDepth
            // 
            lblCurrentDepth.AutoSize = true;
            lblCurrentDepth.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblCurrentDepth.ForeColor = Color.Blue;
            lblCurrentDepth.Location = new Point(28, 68);
            lblCurrentDepth.Margin = new Padding(4, 0, 4, 0);
            lblCurrentDepth.Name = "lblCurrentDepth";
            lblCurrentDepth.Size = new Size(119, 24);
            lblCurrentDepth.TabIndex = 1;
            lblCurrentDepth.Text = "当前深度: 0m";
            // 
            // lblDepth
            // 
            lblDepth.AutoSize = true;
            lblDepth.Font = new Font("微软雅黑", 10F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblDepth.Location = new Point(28, 22);
            lblDepth.Margin = new Padding(4, 0, 4, 0);
            lblDepth.Name = "lblDepth";
            lblDepth.Size = new Size(98, 27);
            lblDepth.TabIndex = 0;
            lblDepth.Text = "选择深度:";
            // 
            // tabPage2
            // 
            tabPage2.BackColor = Color.White;
            tabPage2.Controls.Add(panelStackedChart);
            tabPage2.Controls.Add(panelAxisControls);
            tabPage2.Location = new Point(4, 36);
            tabPage2.Margin = new Padding(4, 4, 4, 4);
            tabPage2.Name = "tabPage2";
            tabPage2.Padding = new Padding(4, 4, 4, 4);
            tabPage2.Size = new Size(1917, 1310);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "矿物含量分布";
            // 
            // panelStackedChart
            // 
            panelStackedChart.BackColor = Color.White;
            panelStackedChart.Dock = DockStyle.Fill;
            panelStackedChart.Location = new Point(4, 153);
            panelStackedChart.Margin = new Padding(4, 4, 4, 4);
            panelStackedChart.Name = "panelStackedChart";
            panelStackedChart.Size = new Size(1909, 1153);
            panelStackedChart.TabIndex = 0;
            // 
            // panelAxisControls
            // 
            panelAxisControls.BackColor = Color.FromArgb(240, 240, 240);
            panelAxisControls.BorderStyle = BorderStyle.FixedSingle;
            panelAxisControls.Controls.Add(btnResetSettings);
            panelAxisControls.Controls.Add(btnApplySettings);
            panelAxisControls.Controls.Add(grpXAxis);
            panelAxisControls.Controls.Add(lblSearchColumn);
            panelAxisControls.Controls.Add(cboSearchColumn);
            panelAxisControls.Controls.Add(lblMinValue);
            panelAxisControls.Controls.Add(txtMinValue);
            panelAxisControls.Controls.Add(lblMaxValue);
            panelAxisControls.Controls.Add(txtMaxValue);
            panelAxisControls.Controls.Add(btnSearch);
            panelAxisControls.Controls.Add(btnResetData);
            panelAxisControls.Dock = DockStyle.Top;
            panelAxisControls.Location = new Point(4, 4);
            panelAxisControls.Margin = new Padding(4, 4, 4, 4);
            panelAxisControls.Name = "panelAxisControls";
            panelAxisControls.Size = new Size(1909, 149);
            panelAxisControls.TabIndex = 1;
            // 
            // btnResetSettings
            // 
            btnResetSettings.BackColor = Color.FromArgb(108, 117, 125);
            btnResetSettings.FlatAppearance.BorderSize = 0;
            btnResetSettings.FlatStyle = FlatStyle.Flat;
            btnResetSettings.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnResetSettings.ForeColor = Color.White;
            btnResetSettings.Location = new Point(1210, 90);
            btnResetSettings.Margin = new Padding(4, 4, 4, 4);
            btnResetSettings.Name = "btnResetSettings";
            btnResetSettings.Size = new Size(110, 45);
            btnResetSettings.TabIndex = 3;
            btnResetSettings.Text = "重置";
            btnResetSettings.UseVisualStyleBackColor = false;
            btnResetSettings.Click += BtnResetSettings_Click;
            // 
            // btnApplySettings
            // 
            btnApplySettings.BackColor = Color.FromArgb(0, 122, 204);
            btnApplySettings.FlatAppearance.BorderSize = 0;
            btnApplySettings.FlatStyle = FlatStyle.Flat;
            btnApplySettings.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnApplySettings.ForeColor = Color.White;
            btnApplySettings.Location = new Point(1210, 38);
            btnApplySettings.Margin = new Padding(4, 4, 4, 4);
            btnApplySettings.Name = "btnApplySettings";
            btnApplySettings.Size = new Size(110, 45);
            btnApplySettings.TabIndex = 2;
            btnApplySettings.Text = "应用设置";
            btnApplySettings.UseVisualStyleBackColor = false;
            btnApplySettings.Click += BtnApplySettings_Click;
            //
            // lblSearchColumn
            //
            lblSearchColumn.AutoSize = true;
            lblSearchColumn.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblSearchColumn.Location = new Point(605, 25);
            lblSearchColumn.Margin = new Padding(4, 0, 4, 0);
            lblSearchColumn.Name = "lblSearchColumn";
            lblSearchColumn.Size = new Size(64, 24);
            lblSearchColumn.TabIndex = 4;
            lblSearchColumn.Text = "搜索列:";
            //
            // cboSearchColumn
            //
            cboSearchColumn.DropDownStyle = ComboBoxStyle.DropDownList;
            cboSearchColumn.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            cboSearchColumn.FormattingEnabled = true;
            cboSearchColumn.Location = new Point(677, 22);
            cboSearchColumn.Margin = new Padding(4, 4, 4, 4);
            cboSearchColumn.Name = "cboSearchColumn";
            cboSearchColumn.Size = new Size(150, 32);
            cboSearchColumn.TabIndex = 5;
            //
            // lblMinValue
            //
            lblMinValue.AutoSize = true;
            lblMinValue.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblMinValue.Location = new Point(605, 65);
            lblMinValue.Margin = new Padding(4, 0, 4, 0);
            lblMinValue.Name = "lblMinValue";
            lblMinValue.Size = new Size(64, 24);
            lblMinValue.TabIndex = 6;
            lblMinValue.Text = "最小值:";
            //
            // txtMinValue
            //
            txtMinValue.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtMinValue.Location = new Point(677, 62);
            txtMinValue.Margin = new Padding(4, 4, 4, 4);
            txtMinValue.Name = "txtMinValue";
            txtMinValue.Size = new Size(100, 31);
            txtMinValue.TabIndex = 7;
            //
            // lblMaxValue
            //
            lblMaxValue.AutoSize = true;
            lblMaxValue.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblMaxValue.Location = new Point(605, 105);
            lblMaxValue.Margin = new Padding(4, 0, 4, 0);
            lblMaxValue.Name = "lblMaxValue";
            lblMaxValue.Size = new Size(64, 24);
            lblMaxValue.TabIndex = 8;
            lblMaxValue.Text = "最大值:";
            //
            // txtMaxValue
            //
            txtMaxValue.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtMaxValue.Location = new Point(677, 102);
            txtMaxValue.Margin = new Padding(4, 4, 4, 4);
            txtMaxValue.Name = "txtMaxValue";
            txtMaxValue.Size = new Size(100, 31);
            txtMaxValue.TabIndex = 9;
            //
            // btnSearch
            //
            btnSearch.BackColor = Color.FromArgb(0, 122, 204);
            btnSearch.FlatAppearance.BorderSize = 0;
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnSearch.ForeColor = Color.White;
            btnSearch.Location = new Point(850, 22);
            btnSearch.Margin = new Padding(4, 4, 4, 4);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(80, 32);
            btnSearch.TabIndex = 10;
            btnSearch.Text = "搜索";
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += BtnSearch_Click;
            //
            // btnResetData
            //
            btnResetData.BackColor = Color.FromArgb(108, 117, 125);
            btnResetData.FlatAppearance.BorderSize = 0;
            btnResetData.FlatStyle = FlatStyle.Flat;
            btnResetData.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnResetData.ForeColor = Color.White;
            btnResetData.Location = new Point(850, 62);
            btnResetData.Margin = new Padding(4, 4, 4, 4);
            btnResetData.Name = "btnResetData";
            btnResetData.Size = new Size(80, 32);
            btnResetData.TabIndex = 11;
            btnResetData.Text = "重置";
            btnResetData.UseVisualStyleBackColor = false;
            btnResetData.Click += BtnResetData_Click;
            // 
            // grpXAxis
            // 
            grpXAxis.Controls.Add(lblXUnit);
            grpXAxis.Controls.Add(lblXInterval);
            grpXAxis.Controls.Add(numXInterval);
            grpXAxis.Controls.Add(lblXMax);
            grpXAxis.Controls.Add(numXMax);
            grpXAxis.Controls.Add(lblXMin);
            grpXAxis.Controls.Add(numXMin);
            grpXAxis.Controls.Add(chkXAutoRange);
            grpXAxis.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            grpXAxis.Location = new Point(14, 15);
            grpXAxis.Margin = new Padding(4, 4, 4, 4);
            grpXAxis.Name = "grpXAxis";
            grpXAxis.Padding = new Padding(4, 4, 4, 4);
            grpXAxis.Size = new Size(578, 120);
            grpXAxis.TabIndex = 0;
            grpXAxis.TabStop = false;
            grpXAxis.Text = "X轴设置";
            // 
            // lblXUnit
            // 
            lblXUnit.AutoSize = true;
            lblXUnit.Location = new Point(426, 78);
            lblXUnit.Margin = new Padding(4, 0, 4, 0);
            lblXUnit.Name = "lblXUnit";
            lblXUnit.Size = new Size(106, 24);
            lblXUnit.TabIndex = 7;
            lblXUnit.Text = "矿物含量/%";
            // 
            // lblXInterval
            // 
            lblXInterval.AutoSize = true;
            lblXInterval.Location = new Point(289, 78);
            lblXInterval.Margin = new Padding(4, 0, 4, 0);
            lblXInterval.Name = "lblXInterval";
            lblXInterval.Size = new Size(46, 24);
            lblXInterval.TabIndex = 6;
            lblXInterval.Text = "间距";
            // 
            // numXInterval
            // 
            numXInterval.DecimalPlaces = 1;
            numXInterval.Location = new Point(337, 75);
            numXInterval.Margin = new Padding(4, 4, 4, 4);
            numXInterval.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numXInterval.Minimum = new decimal(new int[] { 1, 0, 0, 65536 });
            numXInterval.Name = "numXInterval";
            numXInterval.Size = new Size(82, 31);
            numXInterval.TabIndex = 5;
            numXInterval.Value = new decimal(new int[] { 20, 0, 0, 0 });
            // 
            // lblXMax
            // 
            lblXMax.AutoSize = true;
            lblXMax.Location = new Point(151, 78);
            lblXMax.Margin = new Padding(4, 0, 4, 0);
            lblXMax.Name = "lblXMax";
            lblXMax.Size = new Size(46, 24);
            lblXMax.TabIndex = 4;
            lblXMax.Text = "最大";
            // 
            // numXMax
            // 
            numXMax.DecimalPlaces = 1;
            numXMax.Location = new Point(199, 75);
            numXMax.Margin = new Padding(4, 4, 4, 4);
            numXMax.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numXMax.Name = "numXMax";
            numXMax.Size = new Size(82, 31);
            numXMax.TabIndex = 3;
            numXMax.Value = new decimal(new int[] { 100, 0, 0, 0 });
            // 
            // lblXMin
            // 
            lblXMin.AutoSize = true;
            lblXMin.Location = new Point(14, 78);
            lblXMin.Margin = new Padding(4, 0, 4, 0);
            lblXMin.Name = "lblXMin";
            lblXMin.Size = new Size(46, 24);
            lblXMin.TabIndex = 2;
            lblXMin.Text = "最小";
            // 
            // numXMin
            // 
            numXMin.DecimalPlaces = 1;
            numXMin.Location = new Point(62, 75);
            numXMin.Margin = new Padding(4, 4, 4, 4);
            numXMin.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numXMin.Name = "numXMin";
            numXMin.Size = new Size(82, 31);
            numXMin.TabIndex = 1;
            // 
            // chkXAutoRange
            // 
            chkXAutoRange.AutoSize = true;
            chkXAutoRange.Checked = true;
            chkXAutoRange.CheckState = CheckState.Checked;
            chkXAutoRange.Location = new Point(14, 38);
            chkXAutoRange.Margin = new Padding(4, 4, 4, 4);
            chkXAutoRange.Name = "chkXAutoRange";
            chkXAutoRange.Size = new Size(108, 28);
            chkXAutoRange.TabIndex = 0;
            chkXAutoRange.Text = "手动设置";
            chkXAutoRange.UseVisualStyleBackColor = true;
            // 
            // VisualizationForm
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1925, 1350);
            Controls.Add(tabControl1);
            ForeColor = Color.Black;
            Margin = new Padding(4, 4, 4, 4);
            Name = "VisualizationForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "脆性指数可视化分析";
            WindowState = FormWindowState.Maximized;
            Load += VisualizationForm_Load;
            tabControl1.ResumeLayout(false);
            tabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)chartPie).EndInit();
            panelDepthControl.ResumeLayout(false);
            panelDepthControl.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarDepth).EndInit();
            tabPage2.ResumeLayout(false);
            panelAxisControls.ResumeLayout(false);
            panelAxisControls.PerformLayout();
            grpXAxis.ResumeLayout(false);
            grpXAxis.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numXInterval).EndInit();
            ((System.ComponentModel.ISupportInitialize)numXMax).EndInit();
            ((System.ComponentModel.ISupportInitialize)numXMin).EndInit();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.Panel panelDepthControl;
        private System.Windows.Forms.Label lblDepth;
        private System.Windows.Forms.Label lblCurrentDepth;
        private System.Windows.Forms.TrackBar trackBarDepth;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartPie;
        private System.Windows.Forms.Panel panelStackedChart;
        private System.Windows.Forms.Panel panelAxisControls;
        private System.Windows.Forms.GroupBox grpXAxis;
        private System.Windows.Forms.CheckBox chkXAutoRange;
        private System.Windows.Forms.NumericUpDown numXMin;
        private System.Windows.Forms.NumericUpDown numXMax;
        private System.Windows.Forms.NumericUpDown numXInterval;
        private System.Windows.Forms.Label lblXMin;
        private System.Windows.Forms.Label lblXMax;
        private System.Windows.Forms.Label lblXInterval;
        private System.Windows.Forms.Label lblXUnit;
        private System.Windows.Forms.Button btnApplySettings;
        private System.Windows.Forms.Button btnResetSettings;
        private System.Windows.Forms.Label lblSearchColumn;
        private System.Windows.Forms.ComboBox cboSearchColumn;
        private System.Windows.Forms.Label lblMinValue;
        private System.Windows.Forms.TextBox txtMinValue;
        private System.Windows.Forms.Label lblMaxValue;
        private System.Windows.Forms.TextBox txtMaxValue;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.Button btnResetData;
    }
}
