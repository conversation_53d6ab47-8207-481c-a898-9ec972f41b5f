using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace BritSystem
{
    /// <summary>
    /// 测试VisualizationForm的简单程序
    /// </summary>
    public partial class TestVisualizationForm : Form
    {
        public TestVisualizationForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 创建测试按钮
            Button btnTest = new Button
            {
                Text = "测试VisualizationForm",
                Size = new System.Drawing.Size(200, 50),
                Location = new System.Drawing.Point(50, 50)
            };
            btnTest.Click += BtnTest_Click;

            // 窗体设置
            this.Text = "VisualizationForm测试";
            this.Size = new System.Drawing.Size(300, 200);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Controls.Add(btnTest);

            this.ResumeLayout(false);
        }

        private void BtnTest_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建测试数据
                DataTable testData = CreateTestData();

                // 创建矿物列表
                List<string> brittleMinerals = new List<string> { "石英", "长石", "方解石" };
                List<string> ductileMinerals = new List<string> { "黏土" };

                // 创建并显示VisualizationForm
                VisualizationForm visualForm = new VisualizationForm(testData, brittleMinerals, ductileMinerals);
                visualForm.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable CreateTestData()
        {
            DataTable data = new DataTable();

            // 添加列
            data.Columns.Add("GeoID", typeof(string));
            data.Columns.Add("顶深/m", typeof(double));
            data.Columns.Add("底深/m", typeof(double));
            data.Columns.Add("石英", typeof(double));
            data.Columns.Add("长石", typeof(double));
            data.Columns.Add("方解石", typeof(double));
            data.Columns.Add("黏土", typeof(double));
            data.Columns.Add("脆性指数", typeof(double));

            // 添加测试数据
            Random random = new Random();
            for (int i = 0; i < 20; i++)
            {
                double depth = 4700 + i * 50; // 深度从4700m到5650m
                DataRow row = data.NewRow();
                row["GeoID"] = $"Test_{i + 1:D3}";
                row["顶深/m"] = depth;
                row["底深/m"] = depth + 50;

                // 生成随机矿物含量（确保总和为100%）
                double quartz = random.NextDouble() * 40 + 20;    // 20-60%
                double feldspar = random.NextDouble() * 30 + 10;  // 10-40%
                double calcite = random.NextDouble() * 20 + 5;    // 5-25%
                double clay = 100 - quartz - feldspar - calcite;  // 剩余部分

                row["石英"] = Math.Round(quartz, 2);
                row["长石"] = Math.Round(feldspar, 2);
                row["方解石"] = Math.Round(calcite, 2);
                row["黏土"] = Math.Round(clay, 2);
                row["脆性指数"] = Math.Round((quartz + feldspar + calcite), 2);

                data.Rows.Add(row);
            }

            return data;
        }

        // 启用Main方法进行测试
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestVisualizationForm());
        }
    }
}
