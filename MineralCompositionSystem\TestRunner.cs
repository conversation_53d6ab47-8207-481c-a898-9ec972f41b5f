using System;
using System.Threading.Tasks;
using MineralCompositionSystem.Tests;

namespace MineralCompositionSystem
{
    /// <summary>
    /// 测试运行器 - 用于验证对比图数据导出功能
    /// </summary>
    public class TestRunner
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("=== 对比图数据导出功能测试 ===");
            Console.WriteLine($"测试开始时间: {DateTime.Now}");
            Console.WriteLine();

            try
            {
                // 测试导出功能
                await ComparisonDataExportTest.TestExportFunctionality();
                
                Console.WriteLine();
                Console.WriteLine("=== 测试完成 ===");
                
                // 验证导出的文件
                string testOutputPath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                    "TestExport");
                    
                if (System.IO.Directory.Exists(testOutputPath))
                {
                    ComparisonDataExportTest.ValidateExportedFiles(testOutputPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出现错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 程序入口点（用于独立测试）
        /// </summary>
        public static async Task Main(string[] args)
        {
            await RunAllTests();
        }
    }
}
