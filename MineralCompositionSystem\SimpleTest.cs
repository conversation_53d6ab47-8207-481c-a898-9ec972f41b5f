using System;
using System.Collections.Generic;
using System.Data;

namespace SimpleTest
{
    /// <summary>
    /// 简化版本的BrittlenessCalculator用于测试
    /// </summary>
    public class SimpleBrittlenessCalculator
    {
        private DataTable _sourceData;
        private List<string> _brittleColumns;
        private List<string> _ductileColumns;
        private int _topDepthColumnIndex;
        private int _bottomDepthColumnIndex;

        public SimpleBrittlenessCalculator(DataTable sourceData, List<string> brittleColumns,
            List<string> ductileColumns, int topDepthColumnIndex, int bottomDepthColumnIndex)
        {
            _sourceData = sourceData;
            _brittleColumns = brittleColumns;
            _ductileColumns = ductileColumns;
            _topDepthColumnIndex = topDepthColumnIndex;
            _bottomDepthColumnIndex = bottomDepthColumnIndex;
        }

        public DataTable Calculate()
        {
            DataTable result = new DataTable();

            // 添加基本列
            result.Columns.Add("GeoID", typeof(string));
            result.Columns.Add("顶深/m", typeof(double));
            result.Columns.Add("底深/m", typeof(double));

            // 添加矿物列（使用用户友好名称）
            foreach (string brittleColumn in _brittleColumns)
            {
                string friendlyName = ExtractUserFriendlyName(brittleColumn);
                if (!result.Columns.Contains(friendlyName))
                {
                    result.Columns.Add(friendlyName, typeof(double));
                }
            }

            foreach (string ductileColumn in _ductileColumns)
            {
                string friendlyName = ExtractUserFriendlyName(ductileColumn);
                if (!result.Columns.Contains(friendlyName))
                {
                    result.Columns.Add(friendlyName, typeof(double));
                }
            }

            // 添加脆性指数列
            result.Columns.Add("脆性指数", typeof(double));

            // 处理数据行
            for (int i = 0; i < _sourceData.Rows.Count; i++)
            {
                DataRow sourceRow = _sourceData.Rows[i];
                DataRow resultRow = result.NewRow();

                // 设置基本信息
                resultRow["GeoID"] = $"Sample_{i + 1}";
                resultRow["顶深/m"] = sourceRow[_topDepthColumnIndex];
                resultRow["底深/m"] = sourceRow[_bottomDepthColumnIndex];

                // 复制矿物数据
                double totalBrittle = 0;
                double totalDuctile = 0;

                foreach (string brittleColumn in _brittleColumns)
                {
                    string friendlyName = ExtractUserFriendlyName(brittleColumn);
                    string actualColumnName = ExtractActualColumnName(brittleColumn);

                    if (_sourceData.Columns.Contains(actualColumnName))
                    {
                        double value = Convert.ToDouble(sourceRow[actualColumnName]);
                        resultRow[friendlyName] = value;
                        totalBrittle += value;
                    }
                }

                foreach (string ductileColumn in _ductileColumns)
                {
                    string friendlyName = ExtractUserFriendlyName(ductileColumn);
                    string actualColumnName = ExtractActualColumnName(ductileColumn);

                    if (_sourceData.Columns.Contains(actualColumnName))
                    {
                        double value = Convert.ToDouble(sourceRow[actualColumnName]);
                        resultRow[friendlyName] = value;
                        totalDuctile += value;
                    }
                }

                // 计算脆性指数
                double brittlenessIndex = 0;
                if (totalBrittle + totalDuctile > 0)
                {
                    brittlenessIndex = totalBrittle / (totalBrittle + totalDuctile);
                }
                resultRow["脆性指数"] = brittlenessIndex;

                result.Rows.Add(resultRow);
            }

            return result;
        }

        private string ExtractUserFriendlyName(string selectedColumn)
        {
            if (selectedColumn.Contains(":"))
            {
                return selectedColumn.Split(':')[0].Trim();
            }
            return selectedColumn;
        }

        private string ExtractActualColumnName(string selectedColumn)
        {
            string userFriendlyName = ExtractUserFriendlyName(selectedColumn);

            // 在源数据中查找匹配的列名
            foreach (DataColumn column in _sourceData.Columns)
            {
                if (column.ColumnName.Equals(userFriendlyName, StringComparison.OrdinalIgnoreCase))
                {
                    return column.ColumnName;
                }
            }

            return userFriendlyName;
        }
    }

    /// <summary>
    /// 简单的脆性指数计算器测试
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 脆性指数计算器测试 ===");
            Console.WriteLine("测试时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            Console.WriteLine();

            try
            {
                TestUserFriendlyColumnNames();
                TestBasicCalculation();
                TestColumnNameCompatibility();

                Console.WriteLine();
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("=== 测试结束 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试用户友好列名映射功能
        /// </summary>
        private static void TestUserFriendlyColumnNames()
        {
            Console.WriteLine("开始测试用户友好列名映射功能...");

            // 创建测试数据表
            DataTable sourceData = new DataTable();
            sourceData.Columns.Add("顶深", typeof(double));
            sourceData.Columns.Add("底深", typeof(double));
            sourceData.Columns.Add("石英", typeof(double));
            sourceData.Columns.Add("长石", typeof(double));
            sourceData.Columns.Add("黏土", typeof(double));

            // 添加测试数据
            DataRow row1 = sourceData.NewRow();
            row1["顶深"] = 100.0;
            row1["底深"] = 110.0;
            row1["石英"] = 25.5;
            row1["长石"] = 15.2;
            row1["黏土"] = 30.8;
            sourceData.Rows.Add(row1);

            DataRow row2 = sourceData.NewRow();
            row2["顶深"] = 110.0;
            row2["底深"] = 120.0;
            row2["石英"] = 30.2;
            row2["长石"] = 18.5;
            row2["黏土"] = 25.3;
            sourceData.Rows.Add(row2);

            // 模拟用户选择的列名
            List<string> brittleColumns = new List<string>
            {
                "石英: 25.5",
                "长石: 15.2"
            };

            List<string> ductileColumns = new List<string>
            {
                "黏土: 30.8"
            };

            // 创建计算器实例
            SimpleBrittlenessCalculator calculator = new SimpleBrittlenessCalculator(
                sourceData,
                brittleColumns,
                ductileColumns,
                0, // 顶深列索引
                1  // 底深列索引
            );

            // 执行计算
            DataTable result = calculator.Calculate();

            // 验证结果
            Console.WriteLine("计算完成，验证结果...");
            Console.WriteLine($"结果表列数: {result.Columns.Count}");
            Console.WriteLine($"结果表行数: {result.Rows.Count}");

            // 打印所有列名
            Console.WriteLine("结果表列名:");
            foreach (DataColumn column in result.Columns)
            {
                Console.WriteLine($"  - {column.ColumnName}");
            }

            // 验证是否包含用户友好的列名
            bool hasQuartzColumn = result.Columns.Contains("石英");
            bool hasFeldsparColumn = result.Columns.Contains("长石");
            bool hasClayColumn = result.Columns.Contains("黏土");
            bool hasGeoIDColumn = result.Columns.Contains("GeoID");
            bool hasBrittlenessColumn = result.Columns.Contains("脆性指数");

            Console.WriteLine($"包含'石英'列: {hasQuartzColumn}");
            Console.WriteLine($"包含'长石'列: {hasFeldsparColumn}");
            Console.WriteLine($"包含'黏土'列: {hasClayColumn}");
            Console.WriteLine($"包含'GeoID'列: {hasGeoIDColumn}");
            Console.WriteLine($"包含'脆性指数'列: {hasBrittlenessColumn}");

            // 验证数据是否正确复制
            if (result.Rows.Count > 0)
            {
                DataRow firstRow = result.Rows[0];
                if (hasQuartzColumn)
                {
                    double quartzValue = Convert.ToDouble(firstRow["石英"]);
                    Console.WriteLine($"第一行石英值: {quartzValue}");
                    Console.WriteLine($"石英值是否正确: {quartzValue == 25.5}");
                }

                if (hasBrittlenessColumn)
                {
                    double brittlenessValue = Convert.ToDouble(firstRow["脆性指数"]);
                    Console.WriteLine($"第一行脆性指数: {brittlenessValue:F4}");

                    // 计算期望值: (石英 + 长石) / (石英 + 长石 + 黏土) = (25.5 + 15.2) / (25.5 + 15.2 + 30.8)
                    double expectedValue = (25.5 + 15.2) / (25.5 + 15.2 + 30.8);
                    Console.WriteLine($"期望脆性指数: {expectedValue:F4}");
                    Console.WriteLine($"脆性指数计算是否正确: {Math.Abs(brittlenessValue - expectedValue) < 0.0001}");
                }
            }

            // 测试结果
            bool testPassed = hasQuartzColumn && hasFeldsparColumn && hasClayColumn &&
                             hasGeoIDColumn && hasBrittlenessColumn;

            Console.WriteLine($"\n测试结果: {(testPassed ? "通过" : "失败")}");

            if (testPassed)
            {
                Console.WriteLine("✓ 用户友好列名映射功能正常工作");
            }
            else
            {
                Console.WriteLine("✗ 用户友好列名映射功能存在问题");
            }
        }

        /// <summary>
        /// 测试基本计算功能
        /// </summary>
        private static void TestBasicCalculation()
        {
            Console.WriteLine("\n开始测试基本计算功能...");

            // 创建简单的测试数据
            DataTable sourceData = new DataTable();
            sourceData.Columns.Add("顶深", typeof(double));
            sourceData.Columns.Add("底深", typeof(double));
            sourceData.Columns.Add("石英", typeof(double));
            sourceData.Columns.Add("黏土", typeof(double));

            // 添加测试数据
            DataRow row = sourceData.NewRow();
            row["顶深"] = 100.0;
            row["底深"] = 110.0;
            row["石英"] = 60.0; // 脆性矿物
            row["黏土"] = 40.0; // 韧性矿物
            sourceData.Rows.Add(row);

            List<string> brittleColumns = new List<string> { "石英: 60.0" };
            List<string> ductileColumns = new List<string> { "黏土: 40.0" };

            SimpleBrittlenessCalculator calculator = new SimpleBrittlenessCalculator(
                sourceData,
                brittleColumns,
                ductileColumns,
                0, // 顶深列索引
                1  // 底深列索引
            );

            DataTable result = calculator.Calculate();

            // 验证脆性指数计算
            if (result.Rows.Count > 0 && result.Columns.Contains("脆性指数"))
            {
                double brittlenessIndex = Convert.ToDouble(result.Rows[0]["脆性指数"]);
                double expectedIndex = 60.0 / (60.0 + 40.0); // 0.6

                Console.WriteLine($"计算的脆性指数: {brittlenessIndex}");
                Console.WriteLine($"期望的脆性指数: {expectedIndex}");
                Console.WriteLine($"计算是否正确: {Math.Abs(brittlenessIndex - expectedIndex) < 0.001}");
            }
            else
            {
                Console.WriteLine("✗ 未找到脆性指数列或数据");
            }

            Console.WriteLine("✓ 基本计算功能测试完成");
        }

        /// <summary>
        /// 测试列名兼容性功能
        /// </summary>
        private static void TestColumnNameCompatibility()
        {
            Console.WriteLine("\n开始测试列名兼容性功能...");

            // 创建测试数据表
            DataTable sourceData = new DataTable();
            sourceData.Columns.Add("顶深", typeof(double));
            sourceData.Columns.Add("底深", typeof(double));
            sourceData.Columns.Add("石英", typeof(double));
            sourceData.Columns.Add("长石", typeof(double));
            sourceData.Columns.Add("黏土", typeof(double));

            // 添加测试数据
            DataRow row = sourceData.NewRow();
            row["顶深"] = 100.0;
            row["底深"] = 110.0;
            row["石英"] = 30.0;
            row["长石"] = 20.0;
            row["黏土"] = 50.0;
            sourceData.Rows.Add(row);

            // 模拟用户选择的列名（包含数值）
            List<string> brittleColumns = new List<string>
            {
                "石英: 30.0",
                "长石: 20.0"
            };

            List<string> ductileColumns = new List<string>
            {
                "黏土: 50.0"
            };

            SimpleBrittlenessCalculator calculator = new SimpleBrittlenessCalculator(
                sourceData,
                brittleColumns,
                ductileColumns,
                0, // 顶深列索引
                1  // 底深列索引
            );

            DataTable result = calculator.Calculate();

            // 验证生成的列名
            Console.WriteLine("验证生成的列名...");

            // 检查是否生成了正确的用户友好列名
            bool hasQuartzColumn = result.Columns.Contains("石英");
            bool hasFeldsparColumn = result.Columns.Contains("长石");
            bool hasClayColumn = result.Columns.Contains("黏土");

            Console.WriteLine($"包含'石英'列: {hasQuartzColumn}");
            Console.WriteLine($"包含'长石'列: {hasFeldsparColumn}");
            Console.WriteLine($"包含'黏土'列: {hasClayColumn}");

            // 验证数据是否正确
            if (result.Rows.Count > 0)
            {
                DataRow resultRow = result.Rows[0];

                if (hasQuartzColumn)
                {
                    double quartzValue = Convert.ToDouble(resultRow["石英"]);
                    Console.WriteLine($"石英值: {quartzValue} (期望: 30.0)");
                    Console.WriteLine($"石英值正确: {quartzValue == 30.0}");
                }

                if (hasFeldsparColumn)
                {
                    double feldsparValue = Convert.ToDouble(resultRow["长石"]);
                    Console.WriteLine($"长石值: {feldsparValue} (期望: 20.0)");
                    Console.WriteLine($"长石值正确: {feldsparValue == 20.0}");
                }

                if (hasClayColumn)
                {
                    double clayValue = Convert.ToDouble(resultRow["黏土"]);
                    Console.WriteLine($"黏土值: {clayValue} (期望: 50.0)");
                    Console.WriteLine($"黏土值正确: {clayValue == 50.0}");
                }

                // 验证脆性指数计算
                if (result.Columns.Contains("脆性指数"))
                {
                    double brittlenessIndex = Convert.ToDouble(resultRow["脆性指数"]);
                    double expectedIndex = (30.0 + 20.0) / (30.0 + 20.0 + 50.0); // 0.5
                    Console.WriteLine($"脆性指数: {brittlenessIndex:F4} (期望: {expectedIndex:F4})");
                    Console.WriteLine($"脆性指数正确: {Math.Abs(brittlenessIndex - expectedIndex) < 0.0001}");
                }
            }

            // 测试结果
            bool testPassed = hasQuartzColumn && hasFeldsparColumn && hasClayColumn;
            Console.WriteLine($"\n列名兼容性测试结果: {(testPassed ? "通过" : "失败")}");

            if (testPassed)
            {
                Console.WriteLine("✓ 列名兼容性功能正常工作");
                Console.WriteLine("✓ BrittlenessCalculator能够正确生成用户友好的列名");
                Console.WriteLine("✓ 生成的列名与VisualizationForm兼容");
            }
            else
            {
                Console.WriteLine("✗ 列名兼容性功能存在问题");
            }
        }
    }
}
