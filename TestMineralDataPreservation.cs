using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using BritSystem.Core;

namespace BritSystem
{
    /// <summary>
    /// 测试矿物成分数据保留功能
    /// </summary>
    public partial class TestMineralDataPreservation : Form
    {
        private Button btnTestCalculation;
        private DataGridView dgvTestResult;
        private Label lblTestStatus;

        public TestMineralDataPreservation()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 创建测试按钮
            this.btnTestCalculation = new Button();
            this.btnTestCalculation.Text = "测试脆性指数计算（保留矿物数据）";
            this.btnTestCalculation.Size = new Size(300, 50);
            this.btnTestCalculation.Location = new Point(20, 20);
            this.btnTestCalculation.Click += BtnTestCalculation_Click;

            // 创建状态标签
            this.lblTestStatus = new Label();
            this.lblTestStatus.Text = "点击按钮开始测试...";
            this.lblTestStatus.Size = new Size(600, 30);
            this.lblTestStatus.Location = new Point(20, 80);
            this.lblTestStatus.ForeColor = Color.Blue;

            // 创建结果表格
            this.dgvTestResult = new DataGridView();
            this.dgvTestResult.Location = new Point(20, 120);
            this.dgvTestResult.Size = new Size(760, 400);
            this.dgvTestResult.AutoGenerateColumns = true;
            this.dgvTestResult.AllowUserToAddRows = false;
            this.dgvTestResult.AllowUserToDeleteRows = false;
            this.dgvTestResult.ReadOnly = true;
            this.dgvTestResult.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // 窗体设置
            this.Text = "矿物成分数据保留测试";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Controls.Add(this.btnTestCalculation);
            this.Controls.Add(this.lblTestStatus);
            this.Controls.Add(this.dgvTestResult);

            this.ResumeLayout(false);
        }

        private void BtnTestCalculation_Click(object sender, EventArgs e)
        {
            try
            {
                lblTestStatus.Text = "正在创建测试数据...";
                lblTestStatus.ForeColor = Color.Blue;
                Application.DoEvents();

                // 创建测试数据
                DataTable testData = CreateTestData();

                lblTestStatus.Text = "正在计算脆性指数...";
                Application.DoEvents();

                // 定义矿物列
                List<string> brittleColumns = new List<string> { "石英", "长石", "方解石" };
                List<string> ductileColumns = new List<string> { "黏土", "伊利石" };

                // 创建脆性指数计算器
                BrittlenessCalculator calculator = new BrittlenessCalculator(
                    testData,
                    brittleColumns,
                    ductileColumns,
                    1, // 顶深列索引
                    2  // 底深列索引
                );

                // 计算脆性指数
                DataTable result = calculator.Calculate();

                lblTestStatus.Text = "计算完成，正在验证结果...";
                Application.DoEvents();

                // 验证结果
                bool validationPassed = ValidateResult(result, brittleColumns, ductileColumns);

                // 显示结果
                dgvTestResult.DataSource = result;
                dgvTestResult.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                if (validationPassed)
                {
                    lblTestStatus.Text = $"✅ 测试通过！结果包含 {result.Rows.Count} 行数据，所有矿物成分列都已保留。";
                    lblTestStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblTestStatus.Text = "❌ 测试失败！矿物成分列未正确保留。";
                    lblTestStatus.ForeColor = Color.Red;
                }

                // 显示详细信息
                ShowDetailedInfo(result, brittleColumns, ductileColumns);
            }
            catch (Exception ex)
            {
                lblTestStatus.Text = $"❌ 测试出错: {ex.Message}";
                lblTestStatus.ForeColor = Color.Red;
                MessageBox.Show($"测试时出错: {ex.Message}\n\n详细信息:\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable CreateTestData()
        {
            DataTable data = new DataTable();

            // 添加列
            data.Columns.Add("GeoID", typeof(string));
            data.Columns.Add("顶深/m", typeof(double));
            data.Columns.Add("底深/m", typeof(double));
            data.Columns.Add("石英", typeof(double));
            data.Columns.Add("长石", typeof(double));
            data.Columns.Add("方解石", typeof(double));
            data.Columns.Add("黏土", typeof(double));
            data.Columns.Add("伊利石", typeof(double));

            // 添加测试数据
            Random random = new Random();
            for (int i = 0; i < 5; i++)
            {
                double depth = 4700 + i * 50; // 深度从4700m到4900m
                DataRow row = data.NewRow();
                row["GeoID"] = $"Test_{i + 1:D3}";
                row["顶深/m"] = depth;
                row["底深/m"] = depth + 50;

                // 生成随机矿物含量
                double quartz = random.NextDouble() * 30 + 20;    // 20-50%
                double feldspar = random.NextDouble() * 20 + 10;  // 10-30%
                double calcite = random.NextDouble() * 15 + 5;    // 5-20%
                double clay = random.NextDouble() * 20 + 10;      // 10-30%
                double illite = random.NextDouble() * 15 + 5;     // 5-20%

                row["石英"] = Math.Round(quartz, 2);
                row["长石"] = Math.Round(feldspar, 2);
                row["方解石"] = Math.Round(calcite, 2);
                row["黏土"] = Math.Round(clay, 2);
                row["伊利石"] = Math.Round(illite, 2);

                data.Rows.Add(row);
            }

            return data;
        }

        private bool ValidateResult(DataTable result, List<string> brittleColumns, List<string> ductileColumns)
        {
            // 检查基本列是否存在
            string[] requiredColumns = { "GeoID", "顶深/m", "底深/m", "脆性指数", "脆性矿物总量", "塑性矿物总量" };
            foreach (string col in requiredColumns)
            {
                if (!result.Columns.Contains(col))
                {
                    MessageBox.Show($"缺少必需列: {col}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }

            // 检查矿物成分列是否存在
            foreach (string col in brittleColumns)
            {
                if (!result.Columns.Contains(col))
                {
                    MessageBox.Show($"缺少脆性矿物列: {col}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }

            foreach (string col in ductileColumns)
            {
                if (!result.Columns.Contains(col))
                {
                    MessageBox.Show($"缺少塑性矿物列: {col}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }

            // 检查数据是否有效
            foreach (DataRow row in result.Rows)
            {
                // 检查脆性指数是否计算正确
                if (row["脆性指数"] == DBNull.Value)
                {
                    MessageBox.Show($"脆性指数为空: GeoID={row["GeoID"]}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                // 检查矿物成分数据是否保留
                foreach (string col in brittleColumns.Concat(ductileColumns))
                {
                    if (row[col] == DBNull.Value)
                    {
                        MessageBox.Show($"矿物成分数据丢失: {col}, GeoID={row["GeoID"]}", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                }
            }

            return true;
        }

        private void ShowDetailedInfo(DataTable result, List<string> brittleColumns, List<string> ductileColumns)
        {
            string info = $"计算结果详细信息:\n\n";
            info += $"总行数: {result.Rows.Count}\n";
            info += $"总列数: {result.Columns.Count}\n\n";

            info += "列信息:\n";
            foreach (DataColumn col in result.Columns)
            {
                string columnType = "";
                if (brittleColumns.Contains(col.ColumnName))
                    columnType = " [脆性矿物]";
                else if (ductileColumns.Contains(col.ColumnName))
                    columnType = " [塑性矿物]";
                else if (col.ColumnName == "脆性指数")
                    columnType = " [计算结果]";
                else if (col.ColumnName.Contains("总量"))
                    columnType = " [汇总数据]";

                info += $"- {col.ColumnName}{columnType}\n";
            }

            info += "\n第一行数据示例:\n";
            if (result.Rows.Count > 0)
            {
                DataRow firstRow = result.Rows[0];
                foreach (DataColumn col in result.Columns)
                {
                    info += $"- {col.ColumnName}: {firstRow[col.ColumnName]}\n";
                }
            }

            MessageBox.Show(info, "详细信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestMineralDataPreservation());
        }
    }
}
