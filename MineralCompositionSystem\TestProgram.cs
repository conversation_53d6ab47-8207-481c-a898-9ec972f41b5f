using System;
using System.Collections.Generic;
using System.Data;
using MineralCompositionSystem.Core;

namespace MineralCompositionSystem
{
    /// <summary>
    /// 测试程序
    /// </summary>
    public class TestProgram
    {
        /// <summary>
        /// 测试用户友好列名映射功能
        /// </summary>
        public static void TestUserFriendlyColumnNames()
        {
            Console.WriteLine("开始测试用户友好列名映射功能...");

            // 创建测试数据表，模拟原始Excel数据
            DataTable sourceData = new DataTable();
            sourceData.Columns.Add("Column1", typeof(double)); // 顶深
            sourceData.Columns.Add("Column2", typeof(double)); // 底深
            sourceData.Columns.Add("Column42", typeof(double)); // 石英列（技术列名）
            sourceData.Columns.Add("Column43", typeof(double)); // 长石列（技术列名）
            sourceData.Columns.Add("Column44", typeof(double)); // 黏土列（技术列名）

            // 添加测试数据
            DataRow row1 = sourceData.NewRow();
            row1["Column1"] = 100.0; // 顶深
            row1["Column2"] = 110.0; // 底深
            row1["Column42"] = 25.5; // 石英
            row1["Column43"] = 15.2; // 长石
            row1["Column44"] = 30.8; // 黏土
            sourceData.Rows.Add(row1);

            DataRow row2 = sourceData.NewRow();
            row2["Column1"] = 110.0;
            row2["Column2"] = 120.0;
            row2["Column42"] = 30.2;
            row2["Column43"] = 18.5;
            row2["Column44"] = 25.3;
            sourceData.Rows.Add(row2);

            // 模拟用户选择的列名（包含用户友好名称和示例值）
            List<string> brittleColumns = new List<string>
            {
                "石英: 25.5", // 用户看到的是"石英"，但实际数据在"Column42"
                "长石: 15.2"  // 用户看到的是"长石"，但实际数据在"Column43"
            };

            List<string> ductileColumns = new List<string>
            {
                "黏土: 30.8"  // 用户看到的是"黏土"，但实际数据在"Column44"
            };

            // 创建BrittlenessCalculator实例
            BrittlenessCalculator calculator = new BrittlenessCalculator(
                sourceData,
                brittleColumns,
                ductileColumns,
                0, // 顶深列索引
                1  // 底深列索引
            );

            // 执行计算
            DataTable result = calculator.Calculate();

            // 验证结果
            Console.WriteLine("计算完成，验证结果...");
            Console.WriteLine($"结果表列数: {result.Columns.Count}");
            
            // 打印所有列名
            Console.WriteLine("结果表列名:");
            foreach (DataColumn column in result.Columns)
            {
                Console.WriteLine($"  - {column.ColumnName}");
            }

            // 验证是否包含用户友好的列名
            bool hasQuartzColumn = result.Columns.Contains("石英");
            bool hasFeldsparColumn = result.Columns.Contains("长石");
            bool hasClayColumn = result.Columns.Contains("黏土");

            Console.WriteLine($"包含'石英'列: {hasQuartzColumn}");
            Console.WriteLine($"包含'长石'列: {hasFeldsparColumn}");
            Console.WriteLine($"包含'黏土'列: {hasClayColumn}");

            // 验证不应该包含技术列名
            bool hasColumn42 = result.Columns.Contains("Column42");
            bool hasColumn43 = result.Columns.Contains("Column43");
            bool hasColumn44 = result.Columns.Contains("Column44");

            Console.WriteLine($"包含'Column42'列: {hasColumn42}");
            Console.WriteLine($"包含'Column43'列: {hasColumn43}");
            Console.WriteLine($"包含'Column44'列: {hasColumn44}");

            // 验证数据是否正确复制
            if (result.Rows.Count > 0)
            {
                DataRow firstRow = result.Rows[0];
                if (hasQuartzColumn)
                {
                    double quartzValue = Convert.ToDouble(firstRow["石英"]);
                    Console.WriteLine($"第一行石英值: {quartzValue}");
                    Console.WriteLine($"石英值是否正确: {quartzValue == 25.5}");
                }
            }

            // 测试结果
            bool testPassed = hasQuartzColumn && hasFeldsparColumn && hasClayColumn && 
                             !hasColumn42 && !hasColumn43 && !hasColumn44;

            Console.WriteLine($"\n测试结果: {(testPassed ? "通过" : "失败")}");
            
            if (testPassed)
            {
                Console.WriteLine("✓ 用户友好列名映射功能正常工作");
            }
            else
            {
                Console.WriteLine("✗ 用户友好列名映射功能存在问题");
            }
        }

        /// <summary>
        /// 主测试入口
        /// </summary>
        public static void RunTests()
        {
            Console.WriteLine("=== BrittlenessCalculator 测试开始 ===");
            Console.WriteLine("测试时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            Console.WriteLine();

            try
            {
                TestUserFriendlyColumnNames();
                
                Console.WriteLine();
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("=== BrittlenessCalculator 测试结束 ===");
        }
    }
}
