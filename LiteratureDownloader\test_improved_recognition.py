#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的引用识别功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.pdf_reader import PDFReader
from modules.citation_parser import CitationParser

def test_improved_recognition():
    """测试改进后的引用识别"""
    pdf_file = "paper.pdf"
    
    if not Path(pdf_file).exists():
        print(f"错误: 找不到文件 {pdf_file}")
        return
    
    print("=" * 60)
    print("测试改进后的文献引用识别功能")
    print("=" * 60)
    
    # 创建PDF读取器
    pdf_reader = PDFReader()
    
    # 提取文本
    print("1. 提取PDF文本...")
    text = pdf_reader.extract_text(pdf_file)
    
    if not text:
        print("错误: 无法提取PDF文本")
        return
    
    print(f"   提取的文本长度: {len(text)} 字符")
    
    # 查找References部分
    print("\n2. 查找References部分...")
    references_text = pdf_reader.extract_references_section(text)
    
    if references_text:
        print(f"   找到References部分，长度: {len(references_text)} 字符")
        print(f"   前200个字符: {references_text[:200]}...")
    else:
        print("   未找到明确的References部分")
    
    # 识别引用
    print("\n3. 识别文献引用...")
    citations = pdf_reader.find_citations(text)
    
    print(f"   识别到 {len(citations)} 个文献引用")
    
    if citations:
        print("\n4. 引用详情:")
        print("-" * 60)
        
        for i, citation in enumerate(citations[:10]):  # 只显示前10个
            print(f"\n引用 {i+1}:")
            print(f"  类型: {citation.get('pattern_type', 'unknown')}")
            print(f"  作者: {citation.get('authors', '未知')}")
            print(f"  标题: {citation.get('title', '未知')}")
            print(f"  年份: {citation.get('year', '未知')}")
            print(f"  期刊: {citation.get('journal', '未知')}")
            if citation.get('doi'):
                print(f"  DOI: {citation.get('doi')}")
            print(f"  原文: {citation.get('full_match', '')[:100]}...")
        
        if len(citations) > 10:
            print(f"\n... 还有 {len(citations) - 10} 个引用未显示")
    
    # 解析引用
    print(f"\n5. 解析引用信息...")
    parser = CitationParser()
    parsed_citations = parser.parse_multiple_citations(citations)
    
    print(f"   成功解析 {len(parsed_citations)} 个引用")
    
    if parsed_citations:
        print("\n6. 解析后的引用信息:")
        print("-" * 60)
        
        for i, citation in enumerate(parsed_citations[:5]):  # 只显示前5个
            print(f"\n解析引用 {i+1}:")
            print(f"  标题: {citation.get('title', '未知')}")
            print(f"  作者: {citation.get('authors', '未知')}")
            print(f"  年份: {citation.get('year', '未知')}")
            print(f"  期刊: {citation.get('journal', '未知')}")
            print(f"  置信度: {citation.get('confidence', 0):.2f}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_improved_recognition()
