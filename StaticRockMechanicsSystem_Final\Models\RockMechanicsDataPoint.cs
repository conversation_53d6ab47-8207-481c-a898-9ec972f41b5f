using System;

namespace StaticRockMechanicsSystem.Models
{
    /// <summary>
    /// 岩石力学参数数据点模型
    /// </summary>
    public class RockMechanicsDataPoint
    {
        /// <summary>
        /// 地质点唯一标识
        /// </summary>
        public string GeoID { get; set; } = string.Empty;

        /// <summary>
        /// 顶深
        /// </summary>
        public double TopDepth { get; set; }

        /// <summary>
        /// 底深
        /// </summary>
        public double BottomDepth { get; set; }

        /// <summary>
        /// 密度 (g/cm³)
        /// </summary>
        public double Density { get; set; }

        /// <summary>
        /// 纵波速度 (m/s)
        /// </summary>
        public double VelocityP { get; set; }

        /// <summary>
        /// 横波速度 (m/s)
        /// </summary>
        public double VelocityS { get; set; }

        /// <summary>
        /// 动态杨氏模量 (GPa)
        /// </summary>
        public double DynamicYoungsModulus { get; set; }

        /// <summary>
        /// 动态泊松比
        /// </summary>
        public double DynamicPoissonsRatio { get; set; }

        /// <summary>
        /// 静态杨氏模量 (GPa)
        /// </summary>
        public double StaticYoungsModulus { get; set; }

        /// <summary>
        /// 静态泊松比
        /// </summary>
        public double StaticPoissonsRatio { get; set; }

        /// <summary>
        /// 脆性指数 (%)
        /// </summary>
        public double BrittlenessIndex { get; set; }

        /// <summary>
        /// 原始数据行索引
        /// </summary>
        public int RowIndex { get; set; } = -1;

        /// <summary>
        /// 生成唯一的GeoID
        /// </summary>
        public void GenerateGeoID()
        {
            // 使用深度和脆性指数生成唯一的GeoID
            GeoID = $"ROCK_{TopDepth:F2}_{BottomDepth:F2}_{BrittlenessIndex:F4}";
            // 添加哈希校验保证唯一性
            GeoID += $"_{Math.Abs(GeoID.GetHashCode()):X8}";
        }

        /// <summary>
        /// 验证数据有效性
        /// </summary>
        /// <returns>数据是否有效</returns>
        public bool IsValid()
        {
            return Density > 0 && VelocityP > 0 && VelocityS > 0 && 
                   VelocityP > VelocityS && // 纵波速度应大于横波速度
                   TopDepth >= 0 && BottomDepth >= TopDepth;
        }

        /// <summary>
        /// 获取数据摘要信息
        /// </summary>
        /// <returns>数据摘要字符串</returns>
        public string GetSummary()
        {
            return $"深度: {TopDepth:F2}-{BottomDepth:F2}m, " +
                   $"密度: {Density:F2}g/cm³, " +
                   $"Vp: {VelocityP:F0}m/s, " +
                   $"Vs: {VelocityS:F0}m/s, " +
                   $"脆性指数: {BrittlenessIndex:F2}%";
        }
    }
}
