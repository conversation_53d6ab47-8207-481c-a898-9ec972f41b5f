﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Globalization;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using System.Windows.Forms.DataVisualization.Charting;
using BritSystem.Models;
using BritSystem.Core;
using BritSystem.Helpers;
using BritSystem.Services;
using System.Diagnostics;
using System.Reflection;
using System.Text.RegularExpressions;

namespace BritSystem
{
    public partial class AlgorithmFormulaCal : Form
    {
        #region 私有字段

        // 主要数据字段
        private DataTable _sourceData;  // 原始数据表
        private DataTable _resultData;   // 计算结果数据表
        private List<string> _brittleColumns = new List<string>();  // 脆性矿物列
        private List<string> _ductileColumns = new List<string>();  // 塑性矿物列
        private string _lastLoadedFilePath = string.Empty;  // 最后加载的文件路径

        // 顶深和底深列
        private int _topDepthIndex = -1;
        private int _bottomDepthIndex = -1;
        private string _topDepthColumnName = string.Empty;
        private string _bottomDepthColumnName = string.Empty;

        // 辅助功能变量
        private Dictionary<string, string> _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        // 核心功能模块
        private DataManager _dataManager;
        private ColumnDetector _columnDetector;
        private BrittlenessCalculator _brittlenessCalculator;

        // UI控件 - 用于数据显示的DataGridView
        private DataGridView dgvSource;
        private Label lblStatus;



        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AlgorithmFormulaCal()
        {
            InitializeComponent();
            InitializeFields();
            CreateSampleData();
            InitializeResultDataTable();
            InitializeRowSelector();
        }

        /// <summary>
        /// 带数据源的构造函数
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="parentForm">父窗体</param>
        public AlgorithmFormulaCal(DataTable sourceData, Form parentForm = null)
        {
            InitializeComponent();
            InitializeFields();

            // 设置父窗体
            if (parentForm != null)
            {
                this.Owner = parentForm;
            }
            _sourceData = new DataTable();
            if (sourceData != null)
            {
                _sourceData = sourceData.Copy();
            }

            _resultData = new DataTable();
            InitializeResultDataTable();
            InitializeRowSelector();
            LoadColumnNames();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化字段
        /// </summary>
        private void InitializeFields()
        {
            // 初始化列表
            _brittleColumns = new List<string>();
            _ductileColumns = new List<string>();

            // 初始化列名映射
            _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

            // 初始化核心模块
            _dataManager = new DataManager();
            _columnDetector = null; // 会在数据加载后初始化
            _brittlenessCalculator = null; // 会在计算前初始化

            // 初始化UI控件
            InitializeUIControls();
        }

        /// <summary>
        /// 初始化UI控件
        /// </summary>
        private void InitializeUIControls()
        {
            // 初始化源数据网格视图
            dgvSource = new DataGridView();
            dgvSource.Dock = DockStyle.Fill;
            dgvSource.AllowUserToAddRows = false;
            dgvSource.AllowUserToDeleteRows = false;
            dgvSource.ReadOnly = true;
            dgvSource.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;

            // 初始化状态标签
            lblStatus = new Label();
            lblStatus.AutoSize = true;
            lblStatus.Text = "准备就绪";
            lblStatus.ForeColor = Color.Gray;
        }

        /// <summary>
        /// 创建示例数据
        /// </summary>
        private void CreateSampleData()
        {
            // 创建一个空的数据表用于测试
            _sourceData = new DataTable();
            _sourceData.Columns.Add("石英", typeof(double));
            _sourceData.Columns.Add("长石", typeof(double));
            _sourceData.Columns.Add("碳酸盐", typeof(double));
            _sourceData.Columns.Add("黏土", typeof(double));
            _sourceData.Columns.Add("其他矿物", typeof(double));

            // 添加一些测试数据
            _sourceData.Rows.Add(30.5, 25.2, 15.8, 20.3, 8.2);

            // 设置数据管理器
            _dataManager = new DataManager(_sourceData);
        }

        /// <summary>
        /// 初始化结果数据表
        /// </summary>
        private void InitializeResultDataTable()
        {
            try
            {
                // 创建结果数据表
                _resultData = new DataTable();
                _resultData.Columns.Add("GeoID", typeof(string));
                _resultData.Columns.Add("顶深/m", typeof(double));
                _resultData.Columns.Add("底深/m", typeof(double));
                _resultData.Columns.Add("脆性指数", typeof(double));
                _resultData.Columns.Add("脆性矿物总量", typeof(double));
                _resultData.Columns.Add("塑性矿物总量", typeof(double));

                // 设置数据网格视图的样式
                if (dgvResult != null)
                {
                    // 设置数据网格视图的背景颜色为白色
                    dgvResult.BackgroundColor = Color.White;
                    dgvResult.GridColor = Color.LightGray;

                    // 设置默认单元格样式
                    dgvResult.DefaultCellStyle.BackColor = Color.White;
                    dgvResult.DefaultCellStyle.ForeColor = Color.Black;
                    dgvResult.DefaultCellStyle.SelectionBackColor = Color.LightSkyBlue;
                    dgvResult.DefaultCellStyle.SelectionForeColor = Color.Black;

                    // 设置列标题样式
                    dgvResult.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
                    dgvResult.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
                    dgvResult.ColumnHeadersDefaultCellStyle.SelectionBackColor = Color.LightSkyBlue;
                    dgvResult.ColumnHeadersDefaultCellStyle.SelectionForeColor = Color.Black;
                    dgvResult.EnableHeadersVisualStyles = false;

                    // 设置行标题样式
                    dgvResult.RowHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
                    dgvResult.RowHeadersDefaultCellStyle.ForeColor = Color.Black;
                    dgvResult.RowHeadersDefaultCellStyle.SelectionBackColor = Color.LightSkyBlue;
                    dgvResult.RowHeadersDefaultCellStyle.SelectionForeColor = Color.Black;

                    // 设置交替行样式
                    dgvResult.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 250);
                    dgvResult.AlternatingRowsDefaultCellStyle.ForeColor = Color.Black;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化结果数据表时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化行选择下拉框
        /// </summary>
        private void InitializeRowSelector()
        {
            try
            {
                // 不再创建新的下拉框，而是使用设计器中的ICombox
                // 初始化ICombox下拉框内容
                UpdateRowSelector();

                System.Diagnostics.Debug.WriteLine("行选择下拉框初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化行选择下拉框时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新行选择下拉框
        /// </summary>
        private void UpdateRowSelector()
        {
            try
            {
                if (ICombox == null)
                    return;

                // 保存当前选中项
                object selectedItem = ICombox.SelectedItem;

                // 清空下拉框
                ICombox.Items.Clear();

                // 如果没有数据，添加默认项
                if (_sourceData == null || _sourceData.Rows.Count == 0)
                {
                    ICombox.Items.Add("无数据");
                    ICombox.SelectedIndex = 0;
                    return;
                }

                // 添加所有行
                for (int i = 0; i < _sourceData.Rows.Count; i++)
                {
                    ICombox.Items.Add($"行 {i + 1}");
                }

                // 恢复选中项，如果不存在则选择第一项
                if (selectedItem != null && ICombox.Items.Contains(selectedItem))
                {
                    ICombox.SelectedItem = selectedItem;
                }
                else if (ICombox.Items.Count > 0)
                {
                    ICombox.SelectedIndex = 0;
                }

                Debug.WriteLine($"行选择下拉框更新完成，共 {ICombox.Items.Count} 项");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新行选择下拉框时出错: {ex.Message}");
            }
        }

        #endregion

        #region 窗体事件处理方法

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void AlgorithmFormulaCal_Load(object sender, EventArgs e)
        {
            // 设置高DPI支持
            SetHighDpiMode();

            // 设置列表背景色
            lstBrittleColumns.BackColor = Color.FromArgb(80, 100, 120);
            lstBrittleColumns.ForeColor = Color.White;

            lstDuctileColumns.BackColor = Color.FromArgb(120, 80, 100);
            lstDuctileColumns.ForeColor = Color.White;

            LoadColumnNames();
            InitializeResultDataTable();

            // 添加窗体大小改变事件
            this.Resize += AlgorithmFormulaCal_Resize;

            // 设置顶深和底深列选择按钮的事件处理方法
            // 先移除可能存在的事件处理程序，避免重复添加
            btnTopDepth.Click -= BtnTopDepth_Click;
            btnBottomDepth.Click -= BtnBottomDepth_Click;
            btnTopDepth.MouseEnter -= BtnDepth_MouseEnter;
            btnTopDepth.MouseLeave -= BtnDepth_MouseLeave;
            btnBottomDepth.MouseEnter -= BtnDepth_MouseEnter;
            btnBottomDepth.MouseLeave -= BtnDepth_MouseLeave;

            // 重新添加事件处理程序
            btnTopDepth.Click += BtnTopDepth_Click;
            btnBottomDepth.Click += BtnBottomDepth_Click;
            btnTopDepth.MouseEnter += BtnDepth_MouseEnter;
            btnTopDepth.MouseLeave += BtnDepth_MouseLeave;
            btnBottomDepth.MouseEnter += BtnDepth_MouseEnter;
            btnBottomDepth.MouseLeave += BtnDepth_MouseLeave;

            // 确保按钮可见并设置正确的文本和样式
            btnTopDepth.Visible = true;
            btnTopDepth.Text = "选择顶深列";
            btnTopDepth.Size = new Size(243, 36);
            btnTopDepth.FlatStyle = FlatStyle.Flat;
            btnTopDepth.BackColor = Color.FromArgb(60, 60, 60);
            btnTopDepth.ForeColor = Color.White;
            btnTopDepth.Font = new Font("微软雅黑", 9F, FontStyle.Regular);
            btnTopDepth.Cursor = Cursors.Hand;

            btnBottomDepth.Visible = true;
            btnBottomDepth.Text = "选择底深列";
            btnBottomDepth.Size = new Size(243, 36);
            btnBottomDepth.FlatStyle = FlatStyle.Flat;
            btnBottomDepth.BackColor = Color.FromArgb(60, 60, 60);
            btnBottomDepth.ForeColor = Color.White;
            btnBottomDepth.Font = new Font("微软雅黑", 9F, FontStyle.Regular);
            btnBottomDepth.Cursor = Cursors.Hand;

            // 初始化标签文本
            lblTopDepth.Text = "未选择";
            lblTopDepth.ForeColor = Color.Red;

            lblBottomDepth.Text = "未选择";
            lblBottomDepth.ForeColor = Color.Red;

            // 初始化ICombox下拉框
            InitializeICombox();

            // 设置手动映射按钮事件
            btnManualMapping.Click += BtnManualMapping_Click;

            // 设置公式显示文本
            formulaLabel.Text = "脆性指数计算公式:";
            formulaText.Text = "脆性指数 = 脆性矿物总量 / (脆性矿物总量 + 塑性矿物总量)";

            // 按钮事件已在Designer.cs中绑定，这里不需要重复绑定
            // 只需确保BackBtnGenerateCurve和手动映射按钮的事件绑定

            // 设置返回并生成曲线按钮事件
            if (BackBtnGenerateCurve != null)
            {
                BackBtnGenerateCurve.Click += BackBtnGenerateCurve_Click;
                BackBtnGenerateCurve.Enabled = false; // 初始状态禁用

                // 设置按钮样式
                BackBtnGenerateCurve.FlatStyle = FlatStyle.Flat;
                BackBtnGenerateCurve.BackColor = Color.FromArgb(60, 60, 60);
                BackBtnGenerateCurve.ForeColor = Color.White;
                BackBtnGenerateCurve.Font = new Font("微软雅黑", 9F, FontStyle.Regular);
                BackBtnGenerateCurve.Cursor = Cursors.Hand;

                // 添加鼠标悬停事件
                BackBtnGenerateCurve.MouseEnter += BackBtnGenerateCurve_MouseEnter;
                BackBtnGenerateCurve.MouseLeave += BackBtnGenerateCurve_MouseLeave;
            }

            // 设置列表双击事件和多选模式
            lstAvailableColumns.DoubleClick += lstAvailableColumns_DoubleClick;
            lstAvailableColumns.SelectionMode = SelectionMode.MultiExtended; // 支持多选

            // 设置脆性矿物和塑性矿物列表框也支持多选
            if (lstBrittleColumns != null)
                lstBrittleColumns.SelectionMode = SelectionMode.MultiExtended;
            if (lstDuctileColumns != null)
                lstDuctileColumns.SelectionMode = SelectionMode.MultiExtended;

            // 确保ICombox已正确初始化
            if (ICombox != null)
            {
                ICombox.SelectedIndexChanged += ICombox_SelectedIndexChanged;
            }

            // 调整控件位置以适应当前窗体大小
            AdjustControlPositions();

            // 设置窗体图标和标题
            this.Text = "脆性指数计算系统";
        }

        /// <summary>
        /// 设置高DPI模式
        /// </summary>
        private void SetHighDpiMode()
        {
            try
            {
                // 使用AppConfig类应用高DPI设置
                AppConfig.ApplyHighDpiSettings(this);

                // 启用双缓冲和优化绘制
                this.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
                this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置高DPI模式时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体大小变化事件处理
        /// </summary>
        private void AlgorithmFormulaCal_Resize(object sender, EventArgs e)
        {
            // 重新调整所有控件位置
            AdjustControlPositions();

            // 重新高亮显示选中的列
            HighlightSelectedColumns();
        }

        /// <summary>
        /// 深度按钮鼠标进入事件处理
        /// </summary>
        private void BtnDepth_MouseEnter(object sender, EventArgs e)
        {
            if (sender is Button btn)
            {
                // 保存原始颜色
                btn.Tag = btn.ForeColor;

                // 设置鼠标悬停时的字体颜色为青色
                btn.ForeColor = Color.Cyan;
            }
        }

        /// <summary>
        /// 深度按钮鼠标离开事件处理
        /// </summary>
        private void BtnDepth_MouseLeave(object sender, EventArgs e)
        {
            if (sender is Button btn)
            {
                // 恢复原始颜色
                if (btn.Tag is Color originalColor)
                {
                    btn.ForeColor = originalColor;
                }
                else
                {
                    // 如果没有保存原始颜色，则使用默认颜色
                    btn.ForeColor = SystemColors.ControlText;
                }
            }
        }

        /// <summary>
        /// 返回并生成曲线按钮鼠标进入事件处理
        /// </summary>
        private void BackBtnGenerateCurve_MouseEnter(object sender, EventArgs e)
        {
            if (sender is Button btn && btn.Enabled)
            {
                // 保存原始颜色
                btn.Tag = btn.ForeColor;
                // 设置鼠标悬停时的字体颜色为青色
                btn.ForeColor = Color.Cyan;
            }
        }

        /// <summary>
        /// 返回并生成曲线按钮鼠标离开事件处理
        /// </summary>
        private void BackBtnGenerateCurve_MouseLeave(object sender, EventArgs e)
        {
            if (sender is Button btn)
            {
                // 恢复原始颜色
                if (btn.Tag is Color originalColor)
                {
                    btn.ForeColor = originalColor;
                }
                else
                {
                    // 如果没有保存原始颜色，则使用默认颜色
                    btn.ForeColor = Color.White;
                }
            }
        }

        #endregion

        #region 数据处理方法

        /// <summary>
        /// 加载列名到可用列表中
        /// </summary>

        private void LoadColumnNames()
        {
            if (_sourceData == null || _sourceData.Columns.Count == 0)
                return;

            lstAvailableColumns.Items.Clear();

            // 只显示列名，不显示数值
            foreach (DataColumn column in _sourceData.Columns)
            {
                lstAvailableColumns.Items.Add(column.ColumnName);
            }

            // 自动检测顶深和底深列
            AutoDetectDepthColumns();

            // 自动检测矿物列
            AutoDetectMineralColumns();
        }



        /// <summary>
        /// 自动检测顶深和底深列
        /// </summary>
        private void AutoDetectDepthColumns()
        {
            if (_sourceData == null || _columnDetector == null)
                return;

            // 检测顶深列
            string topDepthColumn = _columnDetector.DetectTopDepthColumn();
            if (!string.IsNullOrEmpty(topDepthColumn))
            {
                _topDepthColumnName = topDepthColumn;
                _topDepthIndex = _columnDetector.GetColumnIndex(topDepthColumn);
                lblTopDepth.Text = topDepthColumn;
                lblTopDepth.ForeColor = Color.Black;
            }

            // 检测底深列
            string bottomDepthColumn = _columnDetector.DetectBottomDepthColumn();
            if (!string.IsNullOrEmpty(bottomDepthColumn))
            {
                _bottomDepthColumnName = bottomDepthColumn;
                _bottomDepthIndex = _columnDetector.GetColumnIndex(bottomDepthColumn);
                lblBottomDepth.Text = bottomDepthColumn;
                lblBottomDepth.ForeColor = Color.Black;
            }
        }

        /// <summary>
        /// 自动检测矿物列
        /// </summary>
        private void AutoDetectMineralColumns()
        {
            if (_columnDetector == null)
                return;

            // 检测脆性矿物列
            List<string> brittleColumns = _columnDetector.DetectBrittleMineralColumns();
            foreach (string column in brittleColumns)
            {
                if (!_brittleColumns.Contains(column))
                {
                    _brittleColumns.Add(column);
                    lstBrittleColumns.Items.Add(column);
                }
            }

            // 检测塑性矿物列
            List<string> ductileColumns = _columnDetector.DetectDuctileMineralColumns();
            foreach (string column in ductileColumns)
            {
                if (!_ductileColumns.Contains(column))
                {
                    _ductileColumns.Add(column);
                    lstDuctileColumns.Items.Add(column);
                }
            }
        }

        /// <summary>
        /// 调整控件位置以适应窗体大小变化
        /// </summary>
        private void AdjustControlPositions()
        {
            try
            {
                // 输出调试信息
                Debug.WriteLine("开始调整控件位置");

                // 计算窗口宽度和高度的比例
                float widthRatio = (float)this.ClientSize.Width / 1107f; // 原始设计宽度
                float heightRatio = (float)this.ClientSize.Height / 898f; // 原始设计高度

                // 使用相同的缩放比例，保持布局比例
                float ratio = Math.Min(widthRatio, heightRatio);

                // 计算居中偏移量
                int offsetX = (int)((this.ClientSize.Width - 1107 * ratio) / 2);
                int offsetY = (int)((this.ClientSize.Height - 898 * ratio) / 2);

                // 左侧面板
                leftPanel.Location = new Point(offsetX, offsetY);
                leftPanel.Width = (int)(489 * ratio);
                leftPanel.Height = (int)(898 * ratio);

                // 右侧面板
                rightPanel.Location = new Point(offsetX + (int)(489 * ratio), offsetY);
                rightPanel.Width = (int)(618 * ratio);
                rightPanel.Height = (int)(898 * ratio);

                // 可用列标签和列表
                lblAvailable.Location = new Point((int)(24 * ratio), (int)(24 * ratio));

                lstAvailableColumns.Location = new Point((int)(24 * ratio), (int)(60 * ratio));
                lstAvailableColumns.Size = new Size((int)(440 * ratio), (int)(194 * ratio));

                // ICombox下拉框
                ICombox.Location = new Point((int)(286 * ratio), (int)(24 * ratio));
                ICombox.Size = new Size((int)(180 * ratio), (int)(32 * ratio));

                // 顶深和底深标签和按钮
                lblTopDepth.Location = new Point((int)(24 * ratio), (int)(304 * ratio));
                lblTopDepth.Size = new Size((int)(80 * ratio), (int)(24 * ratio));

                btnTopDepth.Location = new Point((int)(128 * ratio), (int)(304 * ratio));
                btnTopDepth.Size = new Size((int)(243 * ratio), (int)(34 * ratio));
                btnTopDepth.Visible = true;
                btnTopDepth.BringToFront();

                lblBottomDepth.Location = new Point((int)(24 * ratio), (int)(364 * ratio));
                lblBottomDepth.Size = new Size((int)(80 * ratio), (int)(24 * ratio));

                btnBottomDepth.Location = new Point((int)(128 * ratio), (int)(364 * ratio));
                btnBottomDepth.Size = new Size((int)(243 * ratio), (int)(34 * ratio));
                btnBottomDepth.Visible = true;
                btnBottomDepth.BringToFront();

                // 脆性矿物标签和列表
                lblBrittle.Location = new Point((int)(24 * ratio), (int)(431 * ratio));

                lstBrittleColumns.Location = new Point((int)(24 * ratio), (int)(483 * ratio));
                lstBrittleColumns.Size = new Size((int)(440 * ratio), (int)(194 * ratio));

                // 脆性矿物添加和移除按钮
                btnAddBrittle.Location = new Point((int)(367 * ratio), (int)(425 * ratio));
                btnAddBrittle.Size = new Size((int)(98 * ratio), (int)(36 * ratio));

                btnRemoveBrittle.Location = new Point((int)(257 * ratio), (int)(425 * ratio));
                btnRemoveBrittle.Size = new Size((int)(98 * ratio), (int)(36 * ratio));

                // 塑性矿物标签和列表
                lblDuctile.Location = new Point((int)(24 * ratio), (int)(719 * ratio));

                lstDuctileColumns.Location = new Point((int)(24 * ratio), (int)(767 * ratio));
                lstDuctileColumns.Size = new Size((int)(440 * ratio), (int)(194 * ratio));

                // 塑性矿物添加和移除按钮
                btnAddDuctile.Location = new Point((int)(367 * ratio), (int)(713 * ratio));
                btnAddDuctile.Size = new Size((int)(98 * ratio), (int)(36 * ratio));

                btnRemoveDuctile.Location = new Point((int)(257 * ratio), (int)(714 * ratio));
                btnRemoveDuctile.Size = new Size((int)(98 * ratio), (int)(36 * ratio));

                // 右侧面板控件 - 设置精确的位置和锚点
                // 公式面板
                formulaPanel.Location = new Point((int)(24 * ratio), (int)(24 * ratio));
                formulaPanel.Size = new Size((int)(rightPanel.Width - (48 * ratio)), (int)(96 * ratio));

                // 按钮
                btnLoadData.Location = new Point((int)(26 * ratio), (int)(144 * ratio));
                btnLoadData.Size = new Size((int)(183 * ratio), (int)(48 * ratio));

                btnManualMapping.Location = new Point((int)(254 * ratio), (int)(144 * ratio));
                btnManualMapping.Size = new Size((int)(183 * ratio), (int)(48 * ratio));

                btnVisualize.Location = new Point((int)(493 * ratio), (int)(144 * ratio));
                btnVisualize.Size = new Size((int)(183 * ratio), (int)(48 * ratio));

                btnCalculate.Location = new Point((int)(731 * ratio), (int)(144 * ratio));
                btnCalculate.Size = new Size((int)(183 * ratio), (int)(48 * ratio));

                // 结果标签
                resultLabel.Location = new Point((int)(24 * ratio), (int)(216 * ratio));

                // 数据网格
                dgvResult.Location = new Point((int)(24 * ratio), (int)(252 * ratio));
                dgvResult.Size = new Size(
                    (int)(rightPanel.Width - (48 * ratio)),
                    (int)(rightPanel.Height - (252 * ratio) - (80 * ratio))
                );

                // 保存数据按钮
                btnSaveData.Location = new Point(
                    (int)(rightPanel.Width - (183 * ratio) - (24 * ratio)),
                    (int)(rightPanel.Height - (60 * ratio))
                );
                btnSaveData.Size = new Size((int)(183 * ratio), (int)(48 * ratio));

                // 返回按钮位置设置
                if (BackBtnGenerateCurve != null)
                {
                    BackBtnGenerateCurve.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                    BackBtnGenerateCurve.Location = new Point(
                        (int)(btnSaveData.Location.X - (200 * ratio)),
                        btnSaveData.Location.Y
                    );
                }

                // 确保所有控件可见
                foreach (Control control in leftPanel.Controls)
                {
                    control.Visible = true;
                }

                foreach (Control control in rightPanel.Controls)
                {
                    control.Visible = true;
                }

                // 刷新界面
                this.Refresh();

                Debug.WriteLine("控件位置调整完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"调整控件位置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 可用列表双击事件处理
        /// </summary>
        private void lstAvailableColumns_DoubleClick(object sender, EventArgs e)
        {
            if (lstAvailableColumns.SelectedItem == null)
                return;

            string selectedColumn = lstAvailableColumns.SelectedItem.ToString();
            // 提取实际的列名（如果包含值）
            string actualColumnName = selectedColumn.Contains(":") ? selectedColumn.Split(':')[0].Trim() : selectedColumn;

            // 检测该列是脆性矿物还是塑性矿物
            if (_columnDetector != null)
            {
                if (_columnDetector.IsBrittleMineral(actualColumnName))
                {
                    AddToBrittleList(selectedColumn);
                }
                else if (_columnDetector.IsDuctileMineral(actualColumnName))
                {
                    AddToDuctileList(selectedColumn);
                }
            }
        }

        /// <summary>
        /// 添加脆性矿物按钮点击事件
        /// </summary>
        private void btnAddBrittle_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAvailableColumns.SelectedItems.Count == 0)
                    return;

                // 处理多选项
                List<string> itemsToRemove = new List<string>();

                foreach (var selectedItem in lstAvailableColumns.SelectedItems)
                {
                    string selectedColumn = selectedItem.ToString();
                    // 提取实际的列名（如果包含值）
                    string actualColumnName = selectedColumn.Contains(':') ? selectedColumn.Split(':')[0].Trim() : selectedColumn;

                    // 添加到脆性列表
                    if (!_brittleColumns.Contains(selectedColumn) && !_brittleColumns.Contains(actualColumnName))
                    {
                        _brittleColumns.Add(selectedColumn);
                        lstBrittleColumns.Items.Add(selectedColumn);

                        // 记录要从可用列表中移除的项
                        itemsToRemove.Add(selectedColumn);

                        // 如果同时在塑性列表中，则从塑性列表移除
                        if (_ductileColumns.Contains(selectedColumn) || _ductileColumns.Contains(actualColumnName))
                        {
                            _ductileColumns.Remove(selectedColumn);
                            _ductileColumns.Remove(actualColumnName);
                            lstDuctileColumns.Items.Remove(selectedColumn);
                            lstDuctileColumns.Items.Remove(actualColumnName);
                        }

                        Debug.WriteLine($"已添加脆性矿物: {selectedColumn}");
                    }
                }

                // 从可用列表中移除已添加的项
                foreach (string item in itemsToRemove)
                {
                    lstAvailableColumns.Items.Remove(item);
                }

                // 高亮显示选中的列
                HighlightSelectedColumns();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加脆性矿物时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加塑性矿物按钮点击事件
        /// </summary>
        private void btnAddDuctile_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAvailableColumns.SelectedItems.Count == 0)
                    return;

                // 处理多选项
                List<string> itemsToRemove = new List<string>();

                foreach (var selectedItem in lstAvailableColumns.SelectedItems)
                {
                    string selectedColumn = selectedItem.ToString();
                    // 提取实际的列名（如果包含值）
                    string actualColumnName = selectedColumn.Contains(':') ? selectedColumn.Split(':')[0].Trim() : selectedColumn;

                    // 添加到塑性列表
                    if (!_ductileColumns.Contains(selectedColumn) && !_ductileColumns.Contains(actualColumnName))
                    {
                        _ductileColumns.Add(selectedColumn);
                        lstDuctileColumns.Items.Add(selectedColumn);

                        // 记录要从可用列表中移除的项
                        itemsToRemove.Add(selectedColumn);

                        // 如果同时在脆性列表中，则从脆性列表移除
                        if (_brittleColumns.Contains(selectedColumn) || _brittleColumns.Contains(actualColumnName))
                        {
                            _brittleColumns.Remove(selectedColumn);
                            _brittleColumns.Remove(actualColumnName);
                            lstBrittleColumns.Items.Remove(selectedColumn);
                            lstBrittleColumns.Items.Remove(actualColumnName);
                        }

                        Debug.WriteLine($"已添加塑性矿物: {selectedColumn}");
                    }
                }

                // 从可用列表中移除已添加的项
                foreach (string item in itemsToRemove)
                {
                    lstAvailableColumns.Items.Remove(item);
                }

                // 高亮显示选中的列
                HighlightSelectedColumns();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加塑性矿物时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除脆性矿物按钮点击事件
        /// </summary>
        private void btnRemoveBrittle_Click(object sender, EventArgs e)
        {
            if (lstBrittleColumns.SelectedItems.Count == 0)
                return;

            // 处理多选项
            List<string> itemsToRemove = new List<string>();

            foreach (var selectedItem in lstBrittleColumns.SelectedItems)
            {
                string selectedColumn = selectedItem.ToString();
                itemsToRemove.Add(selectedColumn);
                _brittleColumns.Remove(selectedColumn);
            }

            // 从列表中移除选中的项
            foreach (string item in itemsToRemove)
            {
                lstBrittleColumns.Items.Remove(item);
            }

            // 高亮显示选中的列
            HighlightSelectedColumns();
        }

        /// <summary>
        /// 移除塑性矿物按钮点击事件
        /// </summary>
        private void btnRemoveDuctile_Click(object sender, EventArgs e)
        {
            if (lstDuctileColumns.SelectedItems.Count == 0)
                return;

            // 处理多选项
            List<string> itemsToRemove = new List<string>();

            foreach (var selectedItem in lstDuctileColumns.SelectedItems)
            {
                string selectedColumn = selectedItem.ToString();
                itemsToRemove.Add(selectedColumn);
                _ductileColumns.Remove(selectedColumn);
            }

            // 从列表中移除选中的项
            foreach (string item in itemsToRemove)
            {
                lstDuctileColumns.Items.Remove(item);
            }

            // 高亮显示选中的列
            HighlightSelectedColumns();
        }

        /// <summary>
        /// 初始化ICombox下拉框
        /// </summary>
        private void InitializeICombox()
        {
            try
            {
                // 设置ICombox属性
                ICombox.DropDownStyle = ComboBoxStyle.DropDownList;
                ICombox.Visible = true;

                // 添加事件处理
                ICombox.SelectedIndexChanged += ICombox_SelectedIndexChanged;

                // 更新下拉框内容
                UpdateRowSelector();

                Debug.WriteLine("ICombox下拉框初始化完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化ICombox下拉框时出错: {ex.Message}");
            }
        }



        /// <summary>
        /// ICombox选择变更事件
        /// </summary>
        private void ICombox_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (ICombox.SelectedIndex < 0 || _sourceData == null)
                    return;

                // 获取选中的行索引
                int selectedIndex = ICombox.SelectedIndex;

                // 更新可用列表，显示选中行的数据
                if (selectedIndex < _sourceData.Rows.Count)
                {
                    DataRow row = _sourceData.Rows[selectedIndex];

                    // 清空可用列表
                    lstAvailableColumns.Items.Clear();

                    // 添加列名和值
                    foreach (DataColumn column in _sourceData.Columns)
                    {
                        string columnName = column.ColumnName;
                        object value = row[columnName];
                        string displayText = $"{columnName}: {value}";
                        lstAvailableColumns.Items.Add(displayText);
                    }

                    Debug.WriteLine($"已更新可用列表，显示第 {selectedIndex + 1} 行的数据");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ICombox选择变更事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 顶深列选择按钮点击事件
        /// </summary>
        private void BtnTopDepth_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建列选择窗体
                Form columnSelectForm = new Form
                {
                    Text = "选择顶深列",
                    Size = new Size(300, 400),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                // 创建列表框
                ListBox lstColumns = new ListBox
                {
                    Dock = DockStyle.Fill,
                    SelectionMode = SelectionMode.One
                };

                // 添加可用列 - 与lstAvailableColumns同步
                foreach (var item in lstAvailableColumns.Items)
                {
                    lstColumns.Items.Add(item);
                }

                // 添加确定按钮
                Button btnOK = new Button
                {
                    Text = "确定",
                    Dock = DockStyle.Bottom,
                    DialogResult = DialogResult.OK
                };

                // 添加控件到窗体
                columnSelectForm.Controls.Add(lstColumns);
                columnSelectForm.Controls.Add(btnOK);

                // 显示窗体
                if (columnSelectForm.ShowDialog() == DialogResult.OK &&
           lstColumns.SelectedItem != null)
                {
                    // 设置顶深列名
                    string selectedColumn = lstColumns.SelectedItem.ToString();
                    // 提取实际的列名（如果包含值）
                    string actualColumnName = selectedColumn.Contains(':') ? selectedColumn.Split(':')[0].Trim() : selectedColumn;

                    _topDepthColumnName = selectedColumn;
                    _topDepthIndex = _columnDetector?.GetColumnIndex(actualColumnName) ?? -1;

                    lblTopDepth.Text = selectedColumn;
                    lblTopDepth.ForeColor = Color.Green;

                    // 只高亮顶深列
                    HighlightSingleColumn(_topDepthColumnName, "TopDepth", Color.FromArgb(230, 255, 230));

                    System.Diagnostics.Debug.WriteLine($"已选择顶深列: {selectedColumn}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择顶深列时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"选择顶深列时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 底深列选择按钮点击事件
        /// </summary>
        private void BtnBottomDepth_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建列选择窗体
                Form columnSelectForm = new Form
                {
                    Text = "选择底深列",
                    Size = new Size(300, 400),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                // 创建列表框
                ListBox lstColumns = new ListBox
                {
                    Dock = DockStyle.Fill,
                    SelectionMode = SelectionMode.One
                };

                // 添加可用列 - 与lstAvailableColumns同步
                foreach (var item in lstAvailableColumns.Items)
                {
                    lstColumns.Items.Add(item);
                }

                // 添加确定按钮
                Button btnOK = new Button
                {
                    Text = "确定",
                    Dock = DockStyle.Bottom,
                    DialogResult = DialogResult.OK
                };

                // 添加控件到窗体
                columnSelectForm.Controls.Add(lstColumns);
                columnSelectForm.Controls.Add(btnOK);

                // 显示窗体
                if (columnSelectForm.ShowDialog() == DialogResult.OK && lstColumns.SelectedItem != null)
                {
                    // 设置底深列名
                    string selectedColumn = lstColumns.SelectedItem.ToString();
                    // 提取实际的列名（如果包含值）
                    string actualColumnName = selectedColumn.Contains(':') ? selectedColumn.Split(':')[0].Trim() : selectedColumn;

                    _bottomDepthColumnName = selectedColumn;
                    _bottomDepthIndex = _columnDetector?.GetColumnIndex(actualColumnName) ?? -1;

                    lblBottomDepth.Text = selectedColumn;
                    lblBottomDepth.ForeColor = Color.Green;

                    // 只高亮底深列
                    HighlightSingleColumn(_bottomDepthColumnName, "BottomDepth", Color.FromArgb(220, 255, 245));

                    System.Diagnostics.Debug.WriteLine($"已选择底深列: {selectedColumn}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择底深列时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"选择底深列时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动映射按钮点击事件
        /// </summary>
        private void BtnManualMapping_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否已加载数据
                if (_sourceData == null || _sourceData.Columns.Count == 0)
                {
                    MessageBox.Show("请先加载数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 创建手动映射窗体
                using (var form = new ManualMappingForm(_sourceData, _brittleColumns, _ductileColumns, _columnMappings))
                {
                    // 显示手动映射窗体
                    DialogResult result = form.ShowDialog();

                    // 如果用户点击了保存按钮
                    if (result == DialogResult.OK)
                    {
                        // 获取更新后的映射
                        var newMappings = form.GetMappings();

                        // 检查是否有映射
                        if (newMappings.Count == 0)
                        {
                            MessageBox.Show("未添加任何映射！请至少添加一个映射。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }

                        // 更新映射字典
                        _columnMappings = newMappings;

                        // 清空现有的脆性矿物和塑性矿物列表
                        _brittleColumns.Clear();
                        _ductileColumns.Clear();

                        // 从 ManualMappingForm 获取更新后的脆性矿物和塑性矿物列表
                        _brittleColumns.AddRange(form.GetBrittleMinerals());
                        _ductileColumns.AddRange(form.GetDuctileMinerals());

                        // 记录映射信息到日志
                        Debug.WriteLine($"手动映射更新: 共 {_columnMappings.Count} 个映射");
                        Debug.WriteLine($"脆性矿物列: {_brittleColumns.Count} 个");
                        foreach (var col in _brittleColumns)
                        {
                            Debug.WriteLine($"  - {col}");
                        }

                        Debug.WriteLine($"塑性矿物列: {_ductileColumns.Count} 个");
                        foreach (var col in _ductileColumns)
                        {
                            Debug.WriteLine($"  - {col}");
                        }

                        // 更新 UI 列表
                        lstBrittleColumns.Items.Clear();
                        lstDuctileColumns.Items.Clear();

                        foreach (string column in _brittleColumns)
                        {
                            lstBrittleColumns.Items.Add(column);
                        }

                        foreach (string column in _ductileColumns)
                        {
                            lstDuctileColumns.Items.Add(column);
                        }

                        // 更新列表
                        UpdateColumnLists();

                        // 更新结果表
                        InitializeResultDataTable();

                        // 确保计算按钮可用
                        btnCalculate.Enabled = true;

                        // 提示用户可以进行计算
                        MessageBox.Show("手动映射已更新！现在您可以点击\"计算脆性指数\"按钮进行计算。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        Debug.WriteLine("用户取消了手动映射");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"手动映射按钮点击事件出错: {ex.Message}");
                MessageBox.Show($"手动映射出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新列表
        /// </summary>
        private void UpdateColumnLists()
        {
            try
            {
                // 清空列表
                lstAvailableColumns.Items.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();

                // 重新加载列名
                LoadColumnNames();

                // 应用手动映射
                foreach (var mapping in _columnMappings)
                {
                    string sourceColumn = mapping.Key;
                    string targetColumn = mapping.Value;

                    // 检查目标列是否是脆性矿物列
                    if (_brittleColumns.Contains(targetColumn))
                    {
                        // 从可用列表中移除源列
                        if (lstAvailableColumns.Items.Contains(sourceColumn))
                        {
                            lstAvailableColumns.Items.Remove(sourceColumn);
                        }

                        Debug.WriteLine($"应用手动映射: {sourceColumn} -> {targetColumn} (脆性矿物)");
                    }
                    // 检查目标列是否是塑性矿物列
                    else if (_ductileColumns.Contains(targetColumn))
                    {
                        // 从可用列表中移除源列
                        if (lstAvailableColumns.Items.Contains(sourceColumn))
                        {
                            lstAvailableColumns.Items.Remove(sourceColumn);
                        }

                        Debug.WriteLine($"应用手动映射: {sourceColumn} -> {targetColumn} (塑性矿物)");
                    }
                }

                Debug.WriteLine("列表更新完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新列表时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算按钮点击事件
        /// </summary>
        private void btnCalculate_Click(object sender, EventArgs e)
        {
            CalculateBrittlenessIndex();
        }

        /// <summary>
        /// 加载数据按钮点击事件
        /// </summary>
        private void btnLoadData_Click(object sender, EventArgs e)
        {
            LoadExcelFile();
        }

        /// <summary>
        /// 保存数据按钮点击事件
        /// </summary>
        private void BtnSaveData_Click(object sender, EventArgs e)
        {
            SaveResultsToExcel();
        }

        /// <summary>
        /// 可视化按钮点击事件
        /// </summary>
        private void BtnVisualize_Click(object sender, EventArgs e)
        {
            // 添加详细日志
            LoggingService.Instance.Info("===== 可视化按钮被点击 =====");

            // 检查是否有结果数据
            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                LoggingService.Instance.Warning("没有计算结果数据可以可视化");
                MessageBox.Show("没有计算结果可以可视化！请先计算脆性指数。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            LoggingService.Instance.Info($"结果数据检查通过: 行数={_resultData.Rows.Count}, 列数={_resultData.Columns.Count}");

            try
            {
                // 确保列表不为null
                var brittleColumns = _brittleColumns ?? new List<string>();
                var ductileColumns = _ductileColumns ?? new List<string>();

                LoggingService.Instance.Info($"原始脆性矿物列: {string.Join(", ", brittleColumns)}");
                LoggingService.Instance.Info($"原始塑性矿物列: {string.Join(", ", ductileColumns)}");

                // 如果列表为空，提供默认值或从结果数据中推断
                if (brittleColumns.Count == 0 && ductileColumns.Count == 0)
                {
                    LoggingService.Instance.Info("矿物列表为空，开始自动推断...");
                    // 从结果数据中查找可能的矿物列
                    foreach (DataColumn column in _resultData.Columns)
                    {
                        string columnName = column.ColumnName.ToLower();
                        if (columnName.Contains("石英") || columnName.Contains("长石") ||
                            columnName.Contains("quartz") || columnName.Contains("feldspar"))
                        {
                            brittleColumns.Add(column.ColumnName);
                            LoggingService.Instance.Info($"自动添加脆性矿物列: {column.ColumnName}");
                        }
                        else if (columnName.Contains("黏土") || columnName.Contains("方解石") ||
                                columnName.Contains("clay") || columnName.Contains("calcite"))
                        {
                            ductileColumns.Add(column.ColumnName);
                            LoggingService.Instance.Info($"自动添加塑性矿物列: {column.ColumnName}");
                        }
                    }
                }

                LoggingService.Instance.Info($"原始脆性矿物列: {string.Join(", ", brittleColumns)}");
                LoggingService.Instance.Info($"原始塑性矿物列: {string.Join(", ", ductileColumns)}");

                // 提取纯净的列名（移除数值部分）
                List<string> pureBrittleMinerals = brittleColumns.Select(c => c.Contains(":") ? c.Split(':')[0].Trim() : c).ToList();
                List<string> pureDuctileMinerals = ductileColumns.Select(c => c.Contains(":") ? c.Split(':')[0].Trim() : c).ToList();

                LoggingService.Instance.Info($"最终脆性矿物列: {string.Join(", ", pureBrittleMinerals)}");
                LoggingService.Instance.Info($"最终塑性矿物列: {string.Join(", ", pureDuctileMinerals)}");

                LoggingService.Instance.Info("开始创建VisualizationForm...");

                // 使用新的VisualizationForm，传递纯净的列名
                VisualizationForm visualForm = new VisualizationForm(_resultData, pureBrittleMinerals, pureDuctileMinerals);

                LoggingService.Instance.Info("VisualizationForm创建成功，准备显示窗口...");
                visualForm.Show();
                LoggingService.Instance.Info("VisualizationForm.Show()调用完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"创建可视化窗口时出错: {ex.Message}");
                LoggingService.Instance.Error($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"创建可视化窗口时出错: {ex.Message}\n\n详细信息: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 数据结果单元格点击事件
        /// </summary>
        private void DgvResult_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // 处理点击结果表格单元格的逻辑
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // 显示所选单元格的详细信息
            }
        }

        /// <summary>
        /// 数据结果单元格格式化事件
        /// </summary>
        private void DgvResult_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex < 0 || e.CellStyle == null)
                    return;

                // 获取列
                DataGridViewColumn column = dgvResult.Columns[e.ColumnIndex];
                if (column == null)
                    return;

                // 检查列是否已经被标记为特殊列（顶深、底深、脆性矿物、塑性矿物）
                // 如果已标记，则不在这里设置背景色，而是由HighlightCellFormatting处理
                if (column.Tag != null)
                {
                    // 只处理脆性指数列的百分比格式化
                    string columnName = column.Name;
                    string headerText = column.HeaderText;

                    // 检查是否是脆性指数列
                    if (columnName == "脆性指数" || headerText.Contains("脆性指数"))
                    {
                        if (e.Value != null && double.TryParse(e.Value.ToString(), out double value))
                        {
                            e.Value = $"{value}%";
                            e.FormattingApplied = true;
                        }
                    }

                    return; // 已标记的列由HighlightCellFormatting处理，这里不再处理
                }

                // 对于未标记的列，设置默认样式
                // 设置单元格背景颜色为白色
                e.CellStyle.BackColor = Color.White;
                e.CellStyle.ForeColor = Color.Black;

                // 设置选中时的颜色
                e.CellStyle.SelectionBackColor = Color.LightSkyBlue;
                e.CellStyle.SelectionForeColor = Color.Black;

                // 获取列名
                string colName = column.Name;
                string colHeaderText = column.HeaderText;

                // 检查是否是脆性指数列
                if (colName == "脆性指数" || colHeaderText.Contains("脆性指数"))
                {
                    if (e.Value != null && double.TryParse(e.Value.ToString(), out double value))
                    {
                        e.Value = $"{value}%";
                        e.FormattingApplied = true;
                    }

                    // 脆性指数相关列使用浅蓝色
                    e.CellStyle.BackColor = Color.FromArgb(230, 240, 255);
                }
                else if (colName == "脆性矿物总量" || colHeaderText.Contains("脆性矿物总量"))
                {
                    e.CellStyle.BackColor = Color.FromArgb(235, 245, 255);
                }
                else if (colName == "塑性矿物总量" || colHeaderText.Contains("塑性矿物总量"))
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 240, 245);
                }

                // 交替行使用稍微不同的背景色，提高可读性
                if (e.RowIndex % 2 == 1)
                {
                    // 保持原有的背景色，但稍微调暗
                    e.CellStyle.BackColor = Color.FromArgb(
                        Math.Max(e.CellStyle.BackColor.R - 10, 0),
                        Math.Max(e.CellStyle.BackColor.G - 10, 0),
                        Math.Max(e.CellStyle.BackColor.B - 10, 0)
                    );
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"单元格格式化时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 行选择下拉框选择改变事件 - 不再使用，由ICombox_SelectedIndexChanged替代
        /// </summary>
        private void CboRowSelector_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 此方法不再使用，所有功能已移至ICombox_SelectedIndexChanged
        }

        /// <summary>
        /// 添加到脆性矿物列表
        /// </summary>
        private void AddToBrittleList(string columnName)
        {
            // 提取纯列名（移除数值部分）
            string pureColumnName = columnName.Contains(":")
                ? columnName.Split(':')[0].Trim()
                : columnName;

            if (!_brittleColumns.Contains(pureColumnName))
            {
                _brittleColumns.Add(pureColumnName);
                // 显示时仍保留完整信息
                lstBrittleColumns.Items.Add(columnName);

                // 更新高亮显示
                HighlightSelectedColumns();
            }
        }

        /// <summary>
        /// 添加到塑性矿物列表
        /// </summary>
        private void AddToDuctileList(string columnName)
        {
            string pureColumnName = columnName.Contains(":")
                ? columnName.Split(':')[0].Trim()
                : columnName;

            if (!_ductileColumns.Contains(pureColumnName))
            {
                _ductileColumns.Add(pureColumnName);
                lstDuctileColumns.Items.Add(columnName);
                HighlightSelectedColumns();
            }
        }

        /// <summary>
        /// 高亮显示选中的列
        /// </summary>
        private void HighlightSelectedColumns()
        {
            try
            {
                Debug.WriteLine("开始高亮显示选中列");
                Debug.WriteLine($"脆性矿物列数量: {_brittleColumns.Count}");
                Debug.WriteLine($"塑性矿物列数量: {_ductileColumns.Count}");
                Debug.WriteLine($"顶深列名: {_topDepthColumnName}, 底深列名: {_bottomDepthColumnName}");

                // 定义颜色 - 使用更适合白色背景的颜色，并确保顶深和底深使用不同颜色
                Color topDepthColor = Color.FromArgb(230, 255, 230); // 浅绿色
                Color bottomDepthColor = Color.FromArgb(220, 255, 245); // 浅蓝绿色
                Color brittleColor = Color.FromArgb(230, 240, 255); // 浅蓝色
                Color ductileColor = Color.FromArgb(255, 240, 245); // 浅粉色

                // 高亮 dgvSource (如果可用)
                if (dgvSource != null && dgvSource.DataSource != null)
                {
                    HighlightColumnsInGrid(dgvSource, topDepthColor, bottomDepthColor, brittleColor, ductileColor);
                }

                // 高亮 dgvResult (如果可用且显示的是源数据)
                if (dgvResult != null && dgvResult.DataSource != null)
                {
                    HighlightColumnsInGrid(dgvResult, topDepthColor, bottomDepthColor, brittleColor, ductileColor);
                }

                Debug.WriteLine("完成高亮显示选中列");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"高亮显示选中列时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 在指定的数据网格中高亮显示列
        /// </summary>
        private void HighlightColumnsInGrid(DataGridView grid, Color topDepthColor, Color bottomDepthColor, Color brittleColor, Color ductileColor)
        {
            try
            {
                // 重置所有列的样式和Tag，确保移除的列不再高亮
                foreach (DataGridViewColumn col in grid.Columns)
                {
                    col.DefaultCellStyle.BackColor = Color.White;
                    col.HeaderCell.Style.BackColor = Color.White;
                    col.DefaultCellStyle.ForeColor = Color.Black;
                    col.HeaderCell.Style.ForeColor = Color.Black;
                    col.Tag = null; // 重置Tag，这样移除的列就不会再高亮
                }

                // 添加CellFormatting事件处理程序，确保整列高亮
                // 先移除可能存在的事件处理程序，避免重复添加
                grid.CellFormatting -= HighlightCellFormatting;

                // 添加事件处理程序
                grid.CellFormatting += HighlightCellFormatting;
                Debug.WriteLine("已添加CellFormatting事件处理程序");

                // 高亮显示顶深列（如果已选择）
                if (!string.IsNullOrEmpty(_topDepthColumnName))
                {
                    string actualColumnName = _topDepthColumnName.Contains(':') ?
                        _topDepthColumnName.Split(':')[0].Trim() : _topDepthColumnName;

                    if (grid.Columns.Contains(actualColumnName))
                    {
                        // 使用浅绿色高亮顶深列
                        Color highlightColor = Color.FromArgb(230, 255, 230); // 浅绿色
                        grid.Columns[actualColumnName].HeaderCell.Style.BackColor = highlightColor;
                        grid.Columns[actualColumnName].HeaderCell.Style.ForeColor = Color.Black;

                        // 标记该列为顶深列，以便在CellFormatting事件中使用
                        grid.Columns[actualColumnName].Tag = "TopDepth";

                        Debug.WriteLine($"成功高亮顶深列标题: {actualColumnName}");
                    }
                }

                // 高亮显示底深列（如果已选择）
                if (!string.IsNullOrEmpty(_bottomDepthColumnName))
                {
                    string actualColumnName = _bottomDepthColumnName.Contains(':') ?
                        _bottomDepthColumnName.Split(':')[0].Trim() : _bottomDepthColumnName;

                    if (grid.Columns.Contains(actualColumnName))
                    {
                        // 使用浅蓝绿色高亮底深列
                        Color highlightColor = Color.FromArgb(220, 255, 245); // 浅蓝绿色
                        grid.Columns[actualColumnName].HeaderCell.Style.BackColor = highlightColor;
                        grid.Columns[actualColumnName].HeaderCell.Style.ForeColor = Color.Black;

                        // 标记该列为底深列，以便在CellFormatting事件中使用
                        grid.Columns[actualColumnName].Tag = "BottomDepth";

                        Debug.WriteLine($"成功高亮底深列标题: {actualColumnName}");
                    }
                }

                // 高亮显示脆性矿物列
                foreach (string columnName in _brittleColumns)
                {
                    string actualColumnName = columnName.Contains(':') ?
                        columnName.Split(':')[0].Trim() : columnName;

                    if (grid.Columns.Contains(actualColumnName))
                    {
                        grid.Columns[actualColumnName].HeaderCell.Style.BackColor = brittleColor;
                        grid.Columns[actualColumnName].HeaderCell.Style.ForeColor = Color.Black;

                        // 标记该列为脆性矿物列，以便在CellFormatting事件中使用
                        grid.Columns[actualColumnName].Tag = "BrittleMineral";

                        Debug.WriteLine($"成功高亮脆性矿物列标题: {actualColumnName}");
                    }
                }

                // 高亮显示塑性矿物列
                foreach (string columnName in _ductileColumns)
                {
                    string actualColumnName = columnName.Contains(':') ?
                        columnName.Split(':')[0].Trim() : columnName;

                    if (grid.Columns.Contains(actualColumnName))
                    {
                        grid.Columns[actualColumnName].HeaderCell.Style.BackColor = ductileColor;
                        grid.Columns[actualColumnName].HeaderCell.Style.ForeColor = Color.Black;

                        // 标记该列为塑性矿物列，以便在CellFormatting事件中使用
                        grid.Columns[actualColumnName].Tag = "DuctileMineral";

                        Debug.WriteLine($"成功高亮塑性矿物列标题: {actualColumnName}");
                    }
                }

                // 刷新数据网格视图
                grid.Refresh();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"在网格中高亮列时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 单元格格式化事件处理程序，用于高亮显示整列
        /// </summary>
        private void HighlightCellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex < 0)
                    return;

                if (sender is not DataGridView grid || grid.Columns.Count <= e.ColumnIndex)
                    return;

                DataGridViewColumn column = grid.Columns[e.ColumnIndex];
                if (column?.Tag == null)
                    return;

                if (e.CellStyle == null)
                    return;

                string columnTag = column.Tag.ToString() ?? string.Empty;

                // 根据列的标记设置单元格样式
                switch (columnTag)
                {
                    case "TopDepth":
                        // 顶深列使用浅绿色
                        e.CellStyle.BackColor = Color.FromArgb(230, 255, 230);
                        e.CellStyle.ForeColor = Color.Black;
                        break;

                    case "BottomDepth":
                        // 底深列使用浅蓝绿色
                        e.CellStyle.BackColor = Color.FromArgb(220, 255, 245);
                        e.CellStyle.ForeColor = Color.Black;
                        break;

                    case "BrittleMineral":
                        // 脆性矿物列使用浅蓝色
                        e.CellStyle.BackColor = Color.FromArgb(230, 240, 255);
                        e.CellStyle.ForeColor = Color.Black;
                        break;

                    case "DuctileMineral":
                        // 塑性矿物列使用浅粉色
                        e.CellStyle.BackColor = Color.FromArgb(255, 240, 245);
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                }

                // 设置选中时的样式
                e.CellStyle.SelectionBackColor = Color.LightSkyBlue;
                e.CellStyle.SelectionForeColor = Color.Black;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"单元格格式化时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 高亮显示单个列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="tagName">标记名称</param>
        /// <param name="highlightColor">高亮颜色</param>
        private void HighlightSingleColumn(string columnName, string tagName, Color highlightColor)
        {
            try
            {
                Debug.WriteLine($"开始高亮单个列: {columnName}, 标记: {tagName}");

                // 注意：不再重置所有列的高亮，而是只处理当前列

                if (string.IsNullOrEmpty(columnName))
                    return;

                // 处理dgvSource
                if (dgvSource != null && dgvSource.DataSource != null)
                {
                    HighlightColumnInGrid(dgvSource, columnName, tagName, highlightColor);
                }

                // 处理dgvResult
                if (dgvResult != null && dgvResult.DataSource != null)
                {
                    HighlightColumnInGrid(dgvResult, columnName, tagName, highlightColor);
                }

                Debug.WriteLine($"完成高亮单个列: {columnName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"高亮单个列时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 在指定的数据网格中高亮显示单个列
        /// </summary>
        private void HighlightColumnInGrid(DataGridView grid, string columnName, string tagName, Color highlightColor)
        {
            try
            {
                string actualColumnName = columnName.Contains(':') ?
                    columnName.Split(':')[0].Trim() : columnName;

                if (grid.Columns.Contains(actualColumnName))
                {
                    // 设置列标题样式
                    grid.Columns[actualColumnName].HeaderCell.Style.BackColor = highlightColor;
                    grid.Columns[actualColumnName].HeaderCell.Style.ForeColor = Color.Black;

                    // 标记该列，以便在CellFormatting事件中使用
                    grid.Columns[actualColumnName].Tag = tagName;

                    // 先移除事件处理程序，然后重新添加
                    // 这样可以确保事件处理程序只添加一次
                    grid.CellFormatting -= HighlightCellFormatting;
                    grid.CellFormatting += HighlightCellFormatting;
                    Debug.WriteLine("已添加CellFormatting事件处理程序");

                    // 刷新数据网格视图
                    grid.Refresh();

                    Debug.WriteLine($"成功在网格中高亮列: {actualColumnName}, 标记为: {tagName}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"在网格中高亮单个列时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置所有列的高亮
        /// </summary>
        private void ResetAllColumnsHighlight()
        {
            try
            {
                // 重置dgvSource中的列
                if (dgvSource != null && dgvSource.DataSource != null)
                {
                    foreach (DataGridViewColumn col in dgvSource.Columns)
                    {
                        col.HeaderCell.Style.BackColor = dgvSource.ColumnHeadersDefaultCellStyle.BackColor;
                        col.HeaderCell.Style.ForeColor = dgvSource.ColumnHeadersDefaultCellStyle.ForeColor;
                        col.Tag = null;
                    }
                }

                // 重置dgvResult中的列
                if (dgvResult != null && dgvResult.DataSource != null)
                {
                    foreach (DataGridViewColumn col in dgvResult.Columns)
                    {
                        col.HeaderCell.Style.BackColor = dgvResult.ColumnHeadersDefaultCellStyle.BackColor;
                        col.HeaderCell.Style.ForeColor = dgvResult.ColumnHeadersDefaultCellStyle.ForeColor;
                        col.Tag = null;
                    }
                }

                Debug.WriteLine("已重置所有列的高亮");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重置列高亮时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取结果数据
        /// </summary>
        /// <returns>结果数据表</returns>
        public DataTable GetResultData()
        {
            return _resultData?.Copy();
        }

        #endregion

        #region 文件操作方法

        /// <summary>
        /// 加载Excel文件
        /// </summary>
        private void LoadExcelFile()
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Excel文件|*.xlsx;*.xls";
                openFileDialog.Title = "选择Excel文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = openFileDialog.FileName;

                    try
                    {
                        // 使用数据管理器加载Excel文件
                        if (_dataManager.LoadFromExcel(filePath))
                        {
                            _sourceData = _dataManager.SourceData;
                            _lastLoadedFilePath = filePath;

                            // 显示数据到数据网格视图
                            dgvSource.DataSource = _sourceData;

                            // 初始化列检测器和并更新列列表
                            _columnDetector = new ColumnDetector(_sourceData);
                            LoadColumnNames();

                            // 清空之前的选择
                            lstBrittleColumns.Items.Clear();
                            lstDuctileColumns.Items.Clear();
                            _brittleColumns.Clear();
                            _ductileColumns.Clear();

                            // 更新状态
                            lblStatus.Text = $"已加载: {Path.GetFileName(filePath)}";
                            lblStatus.ForeColor = Color.Green;

                            // 显示结果数据
                            if (dgvResult != null)
                            {
                                // 先清空数据源
                                dgvResult.DataSource = null;

                                // 设置表格属性
                                dgvResult.AutoGenerateColumns = true;
                                dgvResult.AllowUserToAddRows = false;
                                dgvResult.AllowUserToDeleteRows = false;
                                dgvResult.ReadOnly = true;
                                dgvResult.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

                                // 设置新的数据源
                                dgvResult.DataSource = _sourceData;

                                // 优化列宽
                                dgvResult.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                                // 刷新表格
                                dgvResult.Refresh();
                            }

                            // 更新ICombox下拉框
                            UpdateRowSelector();
                        }
                        else
                        {
                            MessageBox.Show("无法加载Excel文件，请检查文件格式是否正确。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"加载Excel文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        private void CalculateBrittlenessIndex()
        {
            // 检查数据是否准备好
            if (_sourceData == null || _sourceData.Rows.Count == 0)
            {
                MessageBox.Show("没有数据可以计算！请先加载数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查是否有脆性矿物或塑性矿物列，或者是否有手动映射
            bool hasBrittleOrDuctile = _brittleColumns.Count > 0 || _ductileColumns.Count > 0;
            bool hasMappings = false;

            // 检查是否有映射到脆性矿物或塑性矿物的列
            foreach (var mapping in _columnMappings)
            {
                if (mapping.Value == "脆性矿物" || mapping.Value == "塑性矿物")
                {
                    hasMappings = true;
                    break;
                }
            }

            if (!hasBrittleOrDuctile && !hasMappings)
            {
                MessageBox.Show("请至少选择一个脆性矿物或塑性矿物列。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (_topDepthIndex < 0 || _bottomDepthIndex < 0)
            {
                MessageBox.Show("请选择顶深和底深列。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 处理列名格式，提取实际的列名（如果包含值）
                List<string> processedBrittleColumns = new List<string>();
                List<string> processedDuctileColumns = new List<string>();

                foreach (string column in _brittleColumns)
                {
                    string actualColumnName = column.Contains(":") ? column.Split(':')[0].Trim() : column;
                    processedBrittleColumns.Add(actualColumnName);
                }

                foreach (string column in _ductileColumns)
                {
                    string actualColumnName = column.Contains(":") ? column.Split(':')[0].Trim() : column;
                    processedDuctileColumns.Add(actualColumnName);
                }

                // 处理顶深和底深列名
                string topDepthActualName = _topDepthColumnName.Contains(":") ?
                    _topDepthColumnName.Split(':')[0].Trim() : _topDepthColumnName;

                string bottomDepthActualName = _bottomDepthColumnName.Contains(":") ?
                    _bottomDepthColumnName.Split(':')[0].Trim() : _bottomDepthColumnName;

                // 创建脆性指数计算器
                _brittlenessCalculator = new BrittlenessCalculator(
                    _sourceData,
                    processedBrittleColumns,
                    processedDuctileColumns,
                    _topDepthIndex,
                    _bottomDepthIndex,
                    topDepthActualName,
                    bottomDepthActualName,
                    _columnMappings);

                // 计算脆性指数，BrittlenessCalculator现在已经包含矿物成分列
                _resultData = _brittlenessCalculator.Calculate();

                // 将脆性指数值乘以100（转换为百分比）
                foreach (DataRow row in _resultData.Rows)
                {
                    if (row["脆性指数"] != DBNull.Value)
                    {
                        double brittlenessIndex = Convert.ToDouble(row["脆性指数"]);
                        row["脆性指数"] = brittlenessIndex * 100; // 将脆性指数乘以100
                    }
                }

                // 将计算结果绑定到dgvResult
                if (dgvResult != null)
                {
                    dgvResult.DataSource = null; // 先清空
                    dgvResult.DataSource = _resultData; // 绑定结果数据
                    dgvResult.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
                    dgvResult.Refresh();
                }

                // 启用返回并生成曲线按钮
                if (BackBtnGenerateCurve != null)
                {
                    BackBtnGenerateCurve.Enabled = true;
                    BackBtnGenerateCurve.BackColor = Color.FromArgb(0, 120, 215); // 启用时使用蓝色
                    BackBtnGenerateCurve.ForeColor = Color.White;
                    Debug.WriteLine("BackBtnGenerateCurve按钮已启用");
                }

                MessageBox.Show($"计算完成，共 {_resultData.Rows.Count} 行结果。", "计算成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算脆性指数时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Debug.WriteLine($"计算错误: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 过滤掉顶深和底深都为空的行
        /// </summary>
        private void FilterEmptyDepthRows()
        {
            try
            {
                if (_resultData == null || _resultData.Rows.Count == 0)
                    return;

                // 创建一个临时表来存储过滤后的结果
                DataTable filteredTable = _resultData.Clone();
                int removedRows = 0;

                // 遍历所有行
                foreach (DataRow row in _resultData.Rows)
                {
                    bool topDepthEmpty = row["顶深/m"] == DBNull.Value || string.IsNullOrEmpty(row["顶深/m"].ToString());
                    bool bottomDepthEmpty = row["底深/m"] == DBNull.Value || string.IsNullOrEmpty(row["底深/m"].ToString());

                    // 如果顶深和底深都为空，则跳过该行
                    if (topDepthEmpty && bottomDepthEmpty)
                    {
                        removedRows++;
                        Debug.WriteLine($"过滤掉顶深和底深都为空的行: GeoID={row["GeoID"]}");
                        continue;
                    }

                    // 否则，将行添加到过滤后的表中
                    filteredTable.ImportRow(row);
                }

                // 如果有行被移除，显示提示信息
                if (removedRows > 0)
                {
                    Debug.WriteLine($"共过滤掉 {removedRows} 行顶深和底深都为空的数据");
                    MessageBox.Show($"已过滤掉 {removedRows} 行顶深和底深都为空的数据，这些数据对开采岩石没有意义。",
                        "数据过滤", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // 用过滤后的表替换原表
                _resultData = filteredTable;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"过滤顶深和底深都为空的行时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 过滤掉脆性指数为0的行
        /// </summary>
        private void FilterZeroBrittlenessRows()
        {
            try
            {
                if (_resultData == null || _resultData.Rows.Count == 0)
                    return;

                // 创建一个临时表来存储过滤后的结果
                DataTable filteredTable = _resultData.Clone();
                int removedRows = 0;

                // 遍历所有行
                foreach (DataRow row in _resultData.Rows)
                {
                    // 检查脆性指数是否为0
                    if (row["脆性指数"] != DBNull.Value &&
                        Convert.ToDouble(row["脆性指数"]) == 0)
                    {
                        removedRows++;
                        Debug.WriteLine($"过滤掉脆性指数为0的行: GeoID={row["GeoID"]}");
                        continue;
                    }

                    // 否则，将行添加到过滤后的表中
                    filteredTable.ImportRow(row);
                }

                // 如果有行被移除，显示提示信息
                if (removedRows > 0)
                {
                    Debug.WriteLine($"共过滤掉 {removedRows} 行脆性指数为0的数据");
                    MessageBox.Show($"已过滤掉 {removedRows} 行脆性指数为0的数据，这些数据在分析中没有意义。",
                        "数据过滤", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // 用过滤后的表替换原表
                _resultData = filteredTable;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"过滤脆性指数为0的行时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存结果到Excel
        /// </summary>
        private void SaveResultsToExcel()
        {
            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                MessageBox.Show("没有结果可以保存！请先计算脆性指数。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "Excel文件|*.xlsx;*.xls";
                saveFileDialog.Title = "保存计算结果";
                saveFileDialog.DefaultExt = "xlsx";

                // 建议的文件名基于原始文件
                if (!string.IsNullOrEmpty(_lastLoadedFilePath))
                {
                    string directory = Path.GetDirectoryName(_lastLoadedFilePath);
                    string fileName = Path.GetFileNameWithoutExtension(_lastLoadedFilePath);
                    saveFileDialog.FileName = $"{fileName}_脆性指数计算结果.xlsx";
                    saveFileDialog.InitialDirectory = directory;
                }
                else
                {
                    saveFileDialog.FileName = "脆性指数计算结果.xlsx";
                }

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;

                    try
                    {
                        // 使用数据管理器保存结果
                        bool result = _dataManager.SaveToExcel(_resultData, filePath);
                        if (result)
                        {
                            MessageBox.Show($"结果已保存到: {filePath}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 更新状态
                            lblStatus.Text = $"结果已保存: {Path.GetFileName(filePath)}";
                            lblStatus.ForeColor = Color.Green;
                        }
                        else
                        {
                            MessageBox.Show("保存结果时出错。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存结果时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 返回并生成曲线按钮点击事件处理
        /// </summary>
        private void BackBtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("===== BackBtnGenerateCurve_Click 开始 =====");

                // 检查是否有计算结果
                if (_resultData == null || _resultData.Rows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("错误: 没有计算结果");
                    MessageBox.Show("没有计算结果可以返回！请先计算脆性指数。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                System.Diagnostics.Debug.WriteLine($"计算结果行数: {_resultData.Rows.Count}");

                // 查找父窗体MineralogicalForm
                Form parentForm = this.Owner;
                System.Diagnostics.Debug.WriteLine($"Owner窗体: {parentForm?.GetType().Name ?? "null"}");

                if (parentForm == null)
                {
                    System.Diagnostics.Debug.WriteLine("Owner为null，尝试从OpenForms查找MineralogicalForm");
                    parentForm = Application.OpenForms.Cast<Form>().FirstOrDefault(f => f.GetType().Name == "MineralogicalForm");
                    System.Diagnostics.Debug.WriteLine($"从OpenForms找到的窗体: {parentForm?.GetType().Name ?? "null"}");
                }

                if (parentForm != null && parentForm is MineralogicalForm mineralogicalForm)
                {
                    System.Diagnostics.Debug.WriteLine("找到MineralogicalForm，准备传递数据");
                    // 确保脆性指数值已经乘以100
                    if (_resultData.Columns.Contains("脆性指数"))
                    {
                        foreach (DataRow row in _resultData.Rows)
                        {
                            if (row["脆性指数"] != DBNull.Value)
                            {
                                double brittlenessIndex = Convert.ToDouble(row["脆性指数"]);
                                // 检查脆性指数是否已经是百分比值（大于1）
                                if (brittlenessIndex < 1 && brittlenessIndex > 0)
                                {
                                    row["脆性指数"] = brittlenessIndex * 100; // 将脆性指数乘以100
                                }
                            }
                        }

                    }

                    // 调用LoadCalculatedData方法传递计算结果
                    System.Diagnostics.Debug.WriteLine($"调用LoadCalculatedData，脆性矿物: {string.Join(", ", _brittleColumns)}, 塑性矿物: {string.Join(", ", _ductileColumns)}");
                    mineralogicalForm.LoadCalculatedData(_resultData, _brittleColumns, _ductileColumns);
                    System.Diagnostics.Debug.WriteLine("LoadCalculatedData调用完成");

                    // 关闭当前窗体
                    System.Diagnostics.Debug.WriteLine("关闭AlgorithmFormulaCal窗体");
                    this.Close();
                    System.Diagnostics.Debug.WriteLine("===== BackBtnGenerateCurve_Click 成功完成 =====");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("错误: 未找到MineralogicalForm窗体");
                    MessageBox.Show("未找到MineralogicalForm窗体，无法返回数据。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"返回并生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Debug.WriteLine($"返回并生成曲线错误: {ex.Message}\n{ex.StackTrace}");
            }
        }

        #endregion
    }
}
