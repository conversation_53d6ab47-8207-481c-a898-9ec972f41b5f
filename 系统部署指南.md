# 脆性指数分析系统部署指南

## 📦 系统概述

原有的脆性指数分析系统已成功拆分为两个独立的专业系统：

### 🔬 MineralCompositionSystem_Final
- **系统名称**: 矿物组分法脆性指数分析系统
- **专业领域**: 基于矿物成分的脆性指数计算
- **适用场景**: 地质勘探、矿物学分析

### 🏗️ StaticRockMechanicsSystem_Final  
- **系统名称**: 静态岩石力学参数法脆性指数分析系统
- **专业领域**: 基于岩石力学参数的脆性指数计算
- **适用场景**: 岩石力学分析、地质工程

## 🚀 快速部署

### 1. 环境准备
确保目标计算机已安装：
- **操作系统**: Windows 10/11 (64位)
- **.NET 8.0 Desktop Runtime**: [下载地址](https://dotnet.microsoft.com/download/dotnet/8.0)

### 2. 系统部署

#### 方法一：直接复制文件夹（推荐）
1. 将 `MineralCompositionSystem_Final` 文件夹复制到目标位置
2. 将 `StaticRockMechanicsSystem_Final` 文件夹复制到目标位置
3. 双击对应文件夹中的 `启动系统.bat` 即可运行

#### 方法二：打包发布
```bash
# 进入系统目录
cd MineralCompositionSystem_Final
# 发布为独立可执行文件
dotnet publish -c Release -r win-x64 --self-contained true

cd ../StaticRockMechanicsSystem_Final  
dotnet publish -c Release -r win-x64 --self-contained true
```

## 📁 文件夹结构

### MineralCompositionSystem_Final/
```
MineralCompositionSystem_Final/
├── 启动系统.bat                    # 一键启动脚本
├── 使用说明.md                     # 详细使用说明
├── MineralCompositionSystem.csproj # 项目文件
├── Program.cs                      # 程序入口
├── AppConfig.cs                   # 应用配置
├── Core/                          # 核心算法
│   ├── BrittlenessCalculator.cs   # 脆性指数计算器
│   ├── ColumnDetector.cs          # 列检测器
│   └── DataManager.cs             # 数据管理器
├── Models/                        # 数据模型
├── Services/                      # 服务层
├── Forms/                         # 用户界面
└── Resources/                     # 资源文件
```

### StaticRockMechanicsSystem_Final/
```
StaticRockMechanicsSystem_Final/
├── 启动系统.bat                    # 一键启动脚本
├── 使用说明.md                     # 详细使用说明
├── StaticRockMechanicsSystem.csproj # 项目文件
├── Program.cs                      # 程序入口
├── AppConfig.cs                   # 应用配置
├── Core/                          # 核心算法
├── Models/                        # 数据模型
├── Services/                      # 服务层
├── Forms/                         # 用户界面
└── Resources/                     # 资源文件
```

## 🔧 启动方式

### 方式一：使用启动脚本（推荐）
1. 进入对应系统文件夹
2. 双击 `启动系统.bat`
3. 系统会自动检查环境并启动

### 方式二：命令行启动
```bash
# 启动矿物组分法系统
cd MineralCompositionSystem_Final
dotnet run

# 启动静态岩石力学参数法系统  
cd StaticRockMechanicsSystem_Final
dotnet run
```

### 方式三：发布后启动
发布后会生成独立的 .exe 文件，可直接双击运行。

## 👤 登录信息

两个系统使用相同的登录信息：
- **用户名**: admin
- **密码**: 123

## 📋 系统要求

### 最低配置
- **操作系统**: Windows 10 (64位)
- **处理器**: Intel Core i3 或同等级别
- **内存**: 4GB RAM
- **硬盘**: 1GB 可用空间
- **显示器**: 1024×768 分辨率

### 推荐配置
- **操作系统**: Windows 11 (64位)
- **处理器**: Intel Core i5 或更高
- **内存**: 8GB RAM 或更多
- **硬盘**: 2GB 可用空间
- **显示器**: 1920×1080 分辨率

## 🔍 故障排除

### 常见问题

**Q: 双击启动脚本没有反应？**
A: 
1. 检查是否安装了 .NET 8.0 Runtime
2. 右键启动脚本，选择"以管理员身份运行"
3. 查看是否有杀毒软件阻止

**Q: 提示"找不到 dotnet 命令"？**
A: 
1. 安装 .NET 8.0 Desktop Runtime
2. 重启计算机
3. 检查环境变量设置

**Q: 系统启动后界面显示异常？**
A:
1. 检查显示器分辨率设置
2. 确认系统DPI设置
3. 更新显卡驱动程序

**Q: 无法导入Excel文件？**
A:
1. 确认Excel文件格式正确（.xlsx 或 .xls）
2. 检查文件是否被其他程序占用
3. 验证文件路径中没有特殊字符

## 📦 打包分发

### 创建安装包
1. 使用发布命令生成独立可执行文件
2. 将生成的文件和资源打包为ZIP
3. 创建安装向导（可选）

### 分发建议
- 提供完整的使用说明文档
- 包含示例数据文件
- 提供技术支持联系方式

## 🔄 版本更新

### 更新流程
1. 备份现有系统和数据
2. 下载新版本文件
3. 替换系统文件
4. 验证功能正常

### 数据迁移
- 系统数据存储在 SampleData 目录
- 日志文件位于 SampleData/日志.txt
- 导出的结果文件需要手动备份

## 📞 技术支持

### 日志文件位置
- 矿物组分法系统：`MineralCompositionSystem_Final/SampleData/日志.txt`
- 静态岩石力学参数法系统：`StaticRockMechanicsSystem_Final/SampleData/日志.txt`

### 支持信息
遇到问题时，请提供：
1. 系统版本信息
2. 错误截图或描述
3. 相关日志文件内容
4. 操作步骤重现

---

**注意**: 两个系统完全独立，可以同时安装和运行，互不影响。根据实际需求选择安装一个或两个系统。
