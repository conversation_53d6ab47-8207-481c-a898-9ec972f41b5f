# 静态岩石力学参数法脆性指数分析系统 V1.0

## 📋 系统简介

静态岩石力学参数法脆性指数分析系统是一款专业的岩石力学分析软件，专门用于基于岩石力学参数计算岩石样本的脆性指数。系统通过分析岩石的密度、纵波速度、横波速度等参数，计算动态和静态岩石力学参数，进而得出脆性指数。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
1. 双击 `启动系统.bat` 文件
2. 系统会自动检查环境、编译并启动程序

### 方法二：命令行启动
1. 打开命令提示符或PowerShell
2. 进入系统目录
3. 执行命令：`dotnet run`

## 📋 系统要求

- **操作系统**: Windows 10/11 (64位)
- **运行环境**: .NET 8.0 Desktop Runtime
- **内存**: 建议 4GB 以上
- **硬盘空间**: 500MB 可用空间

## 🔧 环境配置

### 安装 .NET 8.0 Runtime
如果系统提示缺少 .NET 8.0，请：
1. 访问：https://dotnet.microsoft.com/download/dotnet/8.0
2. 下载并安装 ".NET 8.0 Desktop Runtime"
3. 重启系统后再次运行

## 👤 登录信息

- **用户名**: admin
- **密码**: 123

## 🎯 主要功能

### 1. 数据导入
- 支持 Excel 文件导入 (.xlsx, .xls)
- 自动识别岩石力学参数列
- 智能检测深度、密度、速度列

### 2. 岩石力学参数分析
- **基础参数**: 密度(ρ)、纵波速度(Vp)、横波速度(Vs)
- **动态参数**: 动态杨氏模量(Ed)、动态泊松比(μd)
- **静态参数**: 静态杨氏模量(Es)、静态泊松比(μs)

### 3. 计算公式

#### 动态参数计算
```
Ed = ρ × Vs² × (3Vp² - 4Vs²) / (Vp² - Vs²)
μd = (Vp² - 2Vs²) / (2(Vp² - Vs²))
```

#### 静态参数计算
```
Es = 0.0289 × Ed² + 0.2676 × Ed
μs = 1 / (1 + 0.8 × (1/μd - 1))
```

#### 脆性指数计算
```
脆性指数 = (归一化Es + 归一化μs) / 2 × 100%
```

### 4. 数据可视化
- 深度-脆性指数关系曲线
- 岩石力学参数分布图
- 交互式图表操作

### 5. 结果导出
- Excel 格式导出
- 图表保存功能
- 对比分析功能

## 📖 操作指南

### 基本操作流程
1. **启动系统** → 使用启动脚本或命令行
2. **用户登录** → 输入用户名和密码
3. **选择功能** → 点击"开始分析"
4. **导入数据** → 加载包含岩石力学参数的 Excel 文件
5. **参数设置** → 选择密度和速度的单位类型
6. **执行计算** → 批量计算或单个计算
7. **查看结果** → 查看计算结果和图表
8. **导出数据** → 保存分析结果

### 数据格式要求
Excel 文件应包含以下列：
- **深度信息**: 深度列（m）
- **密度**: ρ (g/cm³) 或 RHOB
- **纵波**: Vp (m/s) 或 DT (μs/m)
- **横波**: Vs (m/s) 或 DTS (μs/m)

### 单位转换说明
- **密度**: 支持 ρ (g/cm³) 和 RHOB 两种单位
- **纵波**: 支持 Vp (m/s) 和 DT (μs/m) 两种单位
- **横波**: 支持 Vs (m/s) 和 DTS (μs/m) 两种单位

### 数据验证
系统会自动验证：
- Vp > Vs（纵波速度大于横波速度）
- Vp > √2 × Vs（物理合理性检查）
- 密度 > 0（正值检查）

### 常见问题解决

**Q: 系统无法启动？**
A: 检查是否安装了 .NET 8.0 Runtime

**Q: 计算结果显示异常？**
A: 检查输入数据是否满足 Vp > Vs 的物理约束

**Q: 无法识别参数列？**
A: 确认列名包含关键词（如：密度、Vp、Vs、深度等）

**Q: 脆性指数值不合理？**
A: 检查杨氏模量和泊松比的取值范围设置

## 🔬 技术特点

### 计算精度
- 采用国际标准的岩石力学计算公式
- 支持动静态参数转换
- 提供数据有效性验证

### 专业功能
- 支持批量数据处理
- 提供参数范围自定义
- 包含对比分析功能

## 📞 技术支持

如遇到技术问题，请：
1. 查看系统日志文件（SampleData/日志.txt）
2. 检查数据格式和单位设置
3. 确认输入参数的物理合理性

## 📝 版本信息

- **版本**: V1.0
- **发布日期**: 2025年6月
- **适用范围**: 岩石力学分析、地质工程
- **技术框架**: .NET 8.0 + Windows Forms

---

**注意**: 本系统专门用于静态岩石力学参数法脆性指数分析，如需进行矿物组分法分析，请使用对应的矿物组分法系统。
