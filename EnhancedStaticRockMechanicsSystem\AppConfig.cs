using System;
using System.IO;

namespace EnhancedStaticRockMechanicsSystem
{
    /// <summary>
    /// 应用程序配置类
    /// </summary>
    public static class AppConfig
    {
        /// <summary>
        /// 应用程序名称
        /// </summary>
        public const string AppName = "增强版静态岩石力学参数法系统";

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public const string Version = "2.0.0";

        /// <summary>
        /// 数据文件夹路径
        /// </summary>
        public static string DataFolderPath => Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
            "EnhancedStaticRockMechanicsSystem", "Data");

        /// <summary>
        /// 日志文件夹路径
        /// </summary>
        public static string LogFolderPath => Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
            "EnhancedStaticRockMechanicsSystem", "Logs");

        /// <summary>
        /// 对比数据共享路径
        /// </summary>
        public static string SharedDataPath => Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData),
            "BritSystem", "SharedData");

        /// <summary>
        /// 临时文件路径
        /// </summary>
        public static string TempDataPath => Path.GetTempPath();

        /// <summary>
        /// 初始化配置
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // 创建必要的文件夹
                Directory.CreateDirectory(DataFolderPath);
                Directory.CreateDirectory(LogFolderPath);
                Directory.CreateDirectory(SharedDataPath);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"初始化配置失败: {ex.Message}", ex);
            }
        }
    }
}
