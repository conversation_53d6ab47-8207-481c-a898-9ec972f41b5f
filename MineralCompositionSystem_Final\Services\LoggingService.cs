using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace MineralCompositionSystem.Services
{
    /// <summary>
    /// 日志服务类，提供系统日志记录功能
    /// </summary>
    public class LoggingService
    {
        #region 单例实现

        private static readonly Lazy<LoggingService> _instance = new Lazy<LoggingService>(() => new LoggingService());

        /// <summary>
        /// 获取日志服务实例
        /// </summary>
        public static LoggingService Instance => _instance.Value;

        #endregion

        #region 私有字段

        private readonly string _logFilePath;
        private readonly object _lockObj = new object();
        private readonly bool _debugMode;
        private List<string> _logBuffer = new List<string>();
        private bool _isBufferEnabled = true;
        private int _bufferSize = 100;
        private bool _isFileLoggingEnabled = true;

        #endregion

        #region 构造函数

        /// <summary>
        /// 私有构造函数，初始化日志服务
        /// </summary>
        private LoggingService()
        {
            // 使用项目的SampleData目录作为日志文件路径
            string currentDirectory = Directory.GetCurrentDirectory();
            string logDirectory = Path.Combine(currentDirectory, "SampleData");

            // 确保日志目录存在
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            _logFilePath = Path.Combine(logDirectory, "日志.txt");

            // 从配置或环境变量中获取调试模式设置
            _debugMode = Convert.ToBoolean(Environment.GetEnvironmentVariable("BRITSYSTEM_DEBUG_MODE") ?? "false");

            // 记录启动日志
            Info("日志服务已初始化");
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Debug(string message)
        {
            if (_debugMode)
            {
                WriteLog("DEBUG", message);
            }
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Warning(string message)
        {
            WriteLog("WARNING", message);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="contextMessage">上下文信息</param>
        public void Exception(Exception ex, string contextMessage = null)
        {
            if (string.IsNullOrEmpty(contextMessage))
            {
                WriteLog("EXCEPTION", $"{ex.GetType().Name}: {ex.Message}\n{ex.StackTrace}");
            }
            else
            {
                WriteLog("EXCEPTION", $"{contextMessage}\n{ex.GetType().Name}: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 将缓冲区中的日志写入文件
        /// </summary>
        public void FlushBuffer()
        {
            if (_logBuffer.Count > 0 && _isFileLoggingEnabled)
            {
                try
                {
                    File.AppendAllLines(_logFilePath, _logBuffer);
                    _logBuffer.Clear();
                }
                catch (Exception ex)
                {
                    // 如果写入失败，禁用文件日志功能
                    _isFileLoggingEnabled = false;
                    _logBuffer.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [错误] 无法写入日志文件: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取所有缓冲区中的日志
        /// </summary>
        /// <returns>日志列表</returns>
        public List<string> GetBufferedLogs()
        {
            return new List<string>(_logBuffer);
        }

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        /// <returns>日志文件路径</returns>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        /// <summary>
        /// 设置缓冲区大小
        /// </summary>
        /// <param name="size">缓冲区大小</param>
        public void SetBufferSize(int size)
        {
            if (size > 0)
            {
                _bufferSize = size;
            }
        }

        /// <summary>
        /// 启用或禁用缓冲
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void EnableBuffer(bool enabled)
        {
            _isBufferEnabled = enabled;

            // 如果禁用缓冲，则立即刷新缓冲区
            if (!enabled)
            {
                FlushBuffer();
            }
        }

        /// <summary>
        /// 启用或禁用文件日志
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void EnableFileLogging(bool enabled)
        {
            _isFileLoggingEnabled = enabled;
        }

        /// <summary>
        /// 在应用程序退出时调用，确保所有日志都被写入
        /// </summary>
        public void Shutdown()
        {
            FlushBuffer();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 写入日志到文件
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        private void WriteLog(string level, string message)
        {
            try
            {
                // 获取调用方信息
                string callerInfo = GetCallerInfo();

                // 格式化日志消息
                string logMessage = $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}] [{level}] [{Thread.CurrentThread.ManagedThreadId}] [{callerInfo}] {message}";

                // 线程安全写入
                lock (_lockObj)
                {
                    File.AppendAllText(_logFilePath, logMessage + Environment.NewLine, Encoding.UTF8);
                }

                // 如果是异常或错误，同时输出到控制台
                if (level == "ERROR" || level == "EXCEPTION")
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine(logMessage);
                    Console.ResetColor();
                }
                else if (level == "WARNING")
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine(logMessage);
                    Console.ResetColor();
                }
                else if (_debugMode)
                {
                    Console.WriteLine(logMessage);
                }
            }
            catch
            {
                // 日志记录失败时不抛出异常
            }
        }

        /// <summary>
        /// 获取调用方信息
        /// </summary>
        /// <returns>调用方信息</returns>
        private string GetCallerInfo()
        {
            try
            {
                System.Diagnostics.StackTrace stackTrace = new System.Diagnostics.StackTrace(true);

                // 跳过LoggingService自身的帧
                int frameIndex = 2;
                while (frameIndex < stackTrace.FrameCount)
                {
                    System.Diagnostics.StackFrame frame = stackTrace.GetFrame(frameIndex);
                    if (frame != null && frame.GetMethod() != null)
                    {
                        var method = frame.GetMethod();
                        if (method.DeclaringType != null && !method.DeclaringType.FullName.StartsWith("BritSystem.Services.LoggingService"))
                        {
                            return $"{method.DeclaringType.Name}.{method.Name}";
                        }
                    }
                    frameIndex++;
                }

                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        #endregion
    }
}