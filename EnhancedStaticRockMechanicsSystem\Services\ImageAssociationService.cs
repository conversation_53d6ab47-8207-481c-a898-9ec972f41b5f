using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 图片关联显示服务
    /// </summary>
    public class ImageAssociationService
    {
        /// <summary>
        /// 查找与数据文件关联的图片文件
        /// </summary>
        public List<string> FindAssociatedImages(string dataFilePath)
        {
            var associatedImages = new List<string>();
            
            try
            {
                string directory = Path.GetDirectoryName(dataFilePath);
                string baseFileName = Path.GetFileNameWithoutExtension(dataFilePath);
                
                // 支持的图片格式
                var imageExtensions = new[] { ".png", ".jpg", ".jpeg", ".bmp", ".gif", ".tiff" };
                
                // 查找策略1：相同基础文件名
                foreach (var ext in imageExtensions)
                {
                    string imagePath = Path.Combine(directory, baseFileName + ext);
                    if (File.Exists(imagePath))
                    {
                        associatedImages.Add(imagePath);
                    }
                }
                
                // 查找策略2：包含基础文件名的图片
                var allImages = Directory.GetFiles(directory, "*.*", SearchOption.TopDirectoryOnly)
                    .Where(f => imageExtensions.Contains(Path.GetExtension(f).ToLower()))
                    .ToList();
                    
                foreach (var imagePath in allImages)
                {
                    string imageFileName = Path.GetFileNameWithoutExtension(imagePath);
                    
                    // 检查是否包含数据文件的关键信息
                    if (IsRelatedImage(baseFileName, imageFileName))
                    {
                        if (!associatedImages.Contains(imagePath))
                        {
                            associatedImages.Add(imagePath);
                        }
                    }
                }
                
                // 查找策略3：时间戳匹配
                var timeStampImages = FindImagesByTimestamp(dataFilePath, allImages);
                foreach (var img in timeStampImages)
                {
                    if (!associatedImages.Contains(img))
                    {
                        associatedImages.Add(img);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"查找关联图片失败: {ex.Message}");
            }
            
            return associatedImages;
        }

        private bool IsRelatedImage(string dataFileName, string imageFileName)
        {
            // 提取关键词进行匹配
            var dataKeywords = ExtractKeywords(dataFileName);
            var imageKeywords = ExtractKeywords(imageFileName);
            
            // 计算匹配度
            int matchCount = dataKeywords.Intersect(imageKeywords, StringComparer.OrdinalIgnoreCase).Count();
            
            // 如果有2个或以上关键词匹配，认为是相关图片
            return matchCount >= 2;
        }

        private List<string> ExtractKeywords(string fileName)
        {
            var keywords = new List<string>();
            
            // 按分隔符分割
            var parts = fileName.Split(new[] { '_', '-', ' ', '.' }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var part in parts)
            {
                // 过滤掉纯数字（除非是特定格式的时间戳）
                if (!IsTimestamp(part) && !IsSimpleNumber(part))
                {
                    keywords.Add(part.Trim());
                }
            }
            
            return keywords;
        }

        private bool IsTimestamp(string str)
        {
            // 检查是否是时间戳格式
            return str.Length == 8 && str.All(char.IsDigit) || // YYYYMMDD
                   str.Length == 6 && str.All(char.IsDigit);   // HHMMSS
        }

        private bool IsSimpleNumber(string str)
        {
            return str.Length <= 3 && str.All(char.IsDigit);
        }

        private List<string> FindImagesByTimestamp(string dataFilePath, List<string> allImages)
        {
            var relatedImages = new List<string>();
            
            try
            {
                var dataFileInfo = ComparisonFileParser.ParseFileName(dataFilePath);
                var dataTime = dataFileInfo.CreateTime;
                
                foreach (var imagePath in allImages)
                {
                    var imageFileInfo = ComparisonFileParser.ParseFileName(imagePath);
                    
                    // 如果时间戳相近（1小时内），认为是相关图片
                    if (Math.Abs((imageFileInfo.CreateTime - dataTime).TotalHours) <= 1)
                    {
                        relatedImages.Add(imagePath);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"按时间戳查找图片失败: {ex.Message}");
            }
            
            return relatedImages;
        }

        /// <summary>
        /// 验证图片文件是否有效
        /// </summary>
        public bool IsValidImageFile(string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath))
                    return false;

                var fileInfo = new FileInfo(imagePath);
                
                // 检查文件大小（不能太小或太大）
                if (fileInfo.Length < 1024 || fileInfo.Length > 50 * 1024 * 1024) // 1KB - 50MB
                    return false;

                // 检查文件扩展名
                var validExtensions = new[] { ".png", ".jpg", ".jpeg", ".bmp", ".gif", ".tiff" };
                return validExtensions.Contains(fileInfo.Extension.ToLower());
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取图片文件信息
        /// </summary>
        public ImageFileInfo GetImageFileInfo(string imagePath)
        {
            try
            {
                var fileInfo = new FileInfo(imagePath);
                return new ImageFileInfo
                {
                    FilePath = imagePath,
                    FileName = fileInfo.Name,
                    FileSize = fileInfo.Length,
                    CreationTime = fileInfo.CreationTime,
                    LastModifiedTime = fileInfo.LastWriteTime,
                    IsValid = IsValidImageFile(imagePath)
                };
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"获取图片文件信息失败: {ex.Message}");
                return new ImageFileInfo
                {
                    FilePath = imagePath,
                    FileName = Path.GetFileName(imagePath),
                    IsValid = false
                };
            }
        }
    }

    /// <summary>
    /// 图片文件信息
    /// </summary>
    public class ImageFileInfo
    {
        public string FilePath { get; set; }
        public string FileName { get; set; }
        public long FileSize { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime LastModifiedTime { get; set; }
        public bool IsValid { get; set; }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        public string FormattedFileSize
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} B";
                else if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024.0:F1} KB";
                else
                    return $"{FileSize / (1024.0 * 1024.0):F1} MB";
            }
        }
    }
}
