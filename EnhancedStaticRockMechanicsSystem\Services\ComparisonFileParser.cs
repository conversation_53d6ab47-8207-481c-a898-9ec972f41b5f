using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 对比数据文件信息解析器
    /// </summary>
    public class ComparisonFileParser
    {
        /// <summary>
        /// 文件信息类
        /// </summary>
        public class FileInfo
        {
            public string SystemName { get; set; }
            public string DataType { get; set; }
            public DateTime CreateTime { get; set; }
            public string OriginalFileName { get; set; }
            public string FileExtension { get; set; }
            public bool IsValidComparisonFile { get; set; }
        }

        /// <summary>
        /// 解析文件名格式：矿物组分法_对比数据_20250701_181602
        /// </summary>
        public static FileInfo ParseFileName(string filePath)
        {
            var fileInfo = new FileInfo
            {
                OriginalFileName = Path.GetFileName(filePath),
                FileExtension = Path.GetExtension(filePath).ToLower(),
                IsValidComparisonFile = false
            };

            try
            {
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                
                // 支持多种文件名格式
                var patterns = new[]
                {
                    // 标准格式：矿物组分法_对比数据_20250701_181602
                    @"^(.+?)_对比数据_(\d{8})_(\d{6})$",
                    // 简化格式：矿物组分法_对比数据_20250701
                    @"^(.+?)_对比数据_(\d{8})$",
                    // 英文格式：MineralComposition_ComparisonData_20250701_181602
                    @"^(.+?)_ComparisonData_(\d{8})_(\d{6})$",
                    // 通用格式：系统名_数据类型_时间戳
                    @"^(.+?)_(对比数据|ComparisonData|comparison)_(.+)$"
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(fileName, pattern, RegexOptions.IgnoreCase);
                    
                    if (match.Success)
                    {
                        fileInfo.SystemName = NormalizeSystemName(match.Groups[1].Value);
                        fileInfo.DataType = "对比数据";
                        fileInfo.IsValidComparisonFile = true;

                        // 尝试解析时间戳
                        if (match.Groups.Count > 2)
                        {
                            fileInfo.CreateTime = ParseTimestamp(match.Groups[2].Value, 
                                match.Groups.Count > 3 ? match.Groups[3].Value : null);
                        }
                        break;
                    }
                }

                // 如果没有匹配到标准格式，检查是否包含关键词
                if (!fileInfo.IsValidComparisonFile)
                {
                    var keywords = new[] { "对比", "comparison", "脆性", "brittleness", "矿物", "mineral" };
                    if (keywords.Any(k => fileName.ToLower().Contains(k.ToLower())))
                    {
                        fileInfo.IsValidComparisonFile = true;
                        fileInfo.SystemName = "未知系统";
                        fileInfo.DataType = "对比数据";
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"解析文件名失败: {filePath}, 错误: {ex.Message}");
            }

            return fileInfo;
        }

        private static string NormalizeSystemName(string systemName)
        {
            var mappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { "矿物组分法", "矿物组分法" },
                { "MineralComposition", "矿物组分法" },
                { "静态岩石力学参数法", "静态岩石力学参数法" },
                { "StaticRockMechanics", "静态岩石力学参数法" },
                { "岩石力学", "静态岩石力学参数法" }
            };

            return mappings.TryGetValue(systemName, out string normalized) ? normalized : systemName;
        }

        private static DateTime ParseTimestamp(string dateStr, string timeStr = null)
        {
            try
            {
                if (dateStr.Length == 8) // YYYYMMDD
                {
                    var date = DateTime.ParseExact(dateStr, "yyyyMMdd", null);
                    if (!string.IsNullOrEmpty(timeStr) && timeStr.Length == 6) // HHMMSS
                    {
                        var time = DateTime.ParseExact(timeStr, "HHmmss", null);
                        return date.Add(time.TimeOfDay);
                    }
                    return date;
                }
            }
            catch
            {
                // 解析失败，返回文件修改时间
            }
            
            return DateTime.Now;
        }
    }
}
