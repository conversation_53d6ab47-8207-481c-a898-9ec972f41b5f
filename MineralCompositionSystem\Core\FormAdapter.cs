using System;
using System.Windows.Forms;
using System.Diagnostics;
using MineralCompositionSystem.Forms;

namespace MineralCompositionSystem.Core
{
    /// <summary>
    /// 窗体适配器类，用于将新的原生窗体集成到现有项目中
    /// </summary>
    public static class FormAdapter
    {
        /// <summary>
        /// 显示登录窗体
        /// </summary>
        /// <param name="success">登录是否成功</param>
        /// <param name="username">用户名</param>
        /// <returns>登录结果</returns>
        public static DialogResult ShowLoginForm(out bool success, out string username)
        {
            success = false;
            username = string.Empty;

            try
            {
                // 创建登录窗体
                var loginForm = new LoginForm();
                var result = loginForm.ShowDialog();

                if (result == DialogResult.OK)
                {
                    success = true;
                    username = loginForm.Username;
                }

                return result;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示登录窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示仪表盘窗体
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowDashboardForm(string username)
        {
            try
            {
                // 创建仪表盘窗体
                var dashboardForm = new DashboardForm(username);
                return dashboardForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示仪表盘窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示矿物组分法窗体
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowMineralogicalForm(string username)
        {
            try
            {
                // 创建矿物组分法窗体
                var mineralogicalForm = new MineralogicalForm(username);
                return mineralogicalForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示矿物组分法窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示脆性指数计算器窗体
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowAlgorithmFormulaCalForm(System.Data.DataTable sourceData = null)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("FormAdapter.ShowAlgorithmFormulaCalForm开始执行");
                System.Diagnostics.Debug.WriteLine($"传入的sourceData是否为null: {sourceData == null}");
                if (sourceData != null)
                {
                    System.Diagnostics.Debug.WriteLine($"sourceData行数: {sourceData.Rows.Count}, 列数: {sourceData.Columns.Count}");
                }

                // 创建脆性指数计算器窗体
                System.Diagnostics.Debug.WriteLine("准备创建AlgorithmFormulaCal窗体");
                var algorithmFormulaCal = sourceData != null ?
                    new AlgorithmFormulaCal(sourceData) :
                    new AlgorithmFormulaCal();

                System.Diagnostics.Debug.WriteLine("AlgorithmFormulaCal窗体创建成功，准备显示");

                // 显示窗体并获取结果
                DialogResult result = algorithmFormulaCal.ShowDialog();

                System.Diagnostics.Debug.WriteLine($"AlgorithmFormulaCal窗体关闭，返回结果: {result}");

                // 如果用户点击了可视化按钮，则返回结果为OK
                if (result == DialogResult.OK)
                {
                    // 获取计算结果数据表
                    System.Data.DataTable resultData = algorithmFormulaCal.GetResultData();

                    // 将结果数据表传递给MineralogicalForm
                    if (resultData != null && resultData.Rows.Count > 0)
                    {
                        MineralogicalForm.LoadBrittlenessData(resultData);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FormAdapter.ShowAlgorithmFormulaCalForm异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"显示脆性指数计算器窗体时出错: {ex.Message}\n\n详细信息: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }
    }
}
