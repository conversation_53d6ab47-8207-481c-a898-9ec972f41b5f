{"format": 1, "restore": {"F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\MineralCompositionSystem_Final\\MineralCompositionSystem.csproj": {}}, "projects": {"F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\MineralCompositionSystem_Final\\MineralCompositionSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\MineralCompositionSystem_Final\\MineralCompositionSystem.csproj", "projectName": "MineralCompositionSystem", "projectPath": "F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\MineralCompositionSystem_Final\\MineralCompositionSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\MineralCompositionSystem_Final\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2019SDK\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"DotNetCore.NPOI": {"target": "Package", "version": "[1.2.3, )"}, "EPPlus": {"target": "Package", "version": "[8.0.1, )"}, "HIC.System.Windows.Forms.DataVisualization": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.Office.Interop.Excel": {"target": "Package", "version": "[15.0.4795.1001, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.1774.30, )"}, "Microsoft.Web.WebView2.DevToolsProtocolExtension": {"target": "Package", "version": "[1.0.824, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.OleDb": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}