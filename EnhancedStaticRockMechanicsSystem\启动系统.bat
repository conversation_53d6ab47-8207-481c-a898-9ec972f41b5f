@echo off
chcp 65001 >nul
title 增强版静态岩石力学参数法系统

echo ========================================
echo    增强版静态岩石力学参数法系统
echo ========================================
echo.

echo 正在启动系统...
echo.

REM 检查是否存在编译后的可执行文件
if exist "bin\Debug\net6.0-windows\EnhancedStaticRockMechanicsSystem.exe" (
    echo 启动 Debug 版本...
    start "" "bin\Debug\net6.0-windows\EnhancedStaticRockMechanicsSystem.exe"
    goto :end
)

if exist "bin\Release\net6.0-windows\EnhancedStaticRockMechanicsSystem.exe" (
    echo 启动 Release 版本...
    start "" "bin\Release\net6.0-windows\EnhancedStaticRockMechanicsSystem.exe"
    goto :end
)

REM 如果没有编译后的文件，尝试使用 dotnet run
echo 未找到编译后的可执行文件，尝试使用 dotnet run...
echo.

REM 检查是否安装了 .NET 6.0
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到 .NET 6.0 运行时
    echo 请先安装 .NET 6.0 Desktop Runtime
    echo 下载地址：https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    goto :end
)

echo 使用 dotnet run 启动系统...
dotnet run

:end
echo.
echo 系统已启动完成
pause
