#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件下载模块
功能：
1. 下载PDF文献文件
2. 支持断点续传
3. 进度显示
4. 文件验证
5. 批量下载
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Optional, Callable
from urllib.parse import urlparse, unquote
from loguru import logger

try:
    import requests
    from tqdm import tqdm
except ImportError as e:
    logger.error(f"下载模块导入失败: {e}")
    raise

from config.settings import NETWORK_CONFIG, DOWNLOAD_CONFIG, DOWNLOAD_DIR


class Downloader:
    """文件下载器"""
    
    def __init__(self, download_dir: str = None):
        self.download_dir = Path(download_dir) if download_dir else DOWNLOAD_DIR
        self.download_dir.mkdir(exist_ok=True)
        
        self.network_config = NETWORK_CONFIG
        self.download_config = DOWNLOAD_CONFIG
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.network_config['user_agent']
        })
    
    def download_file(self, url: str, filename: str = None, 
                     progress_callback: Callable = None) -> Optional[str]:
        """下载单个文件"""
        try:
            if not url:
                logger.error("下载URL为空")
                return None
            
            # 生成文件名
            if not filename:
                filename = self.generate_filename_from_url(url)
            
            # 确保文件名安全
            filename = self.sanitize_filename(filename)
            file_path = self.download_dir / filename
            
            logger.info(f"开始下载: {url} -> {file_path}")
            
            # 检查文件是否已存在
            if file_path.exists():
                logger.info(f"文件已存在: {file_path}")
                return str(file_path)
            
            # 获取文件信息
            head_response = self.session.head(url, timeout=self.network_config['timeout'])
            if head_response.status_code != 200:
                logger.warning(f"无法获取文件信息: {head_response.status_code}")
            
            file_size = int(head_response.headers.get('content-length', 0))
            
            # 检查文件大小限制
            if file_size > self.download_config['max_file_size']:
                logger.error(f"文件过大: {file_size} bytes")
                return None
            
            # 下载文件
            response = self.session.get(url, stream=True, timeout=self.network_config['timeout'])
            response.raise_for_status()
            
            # 写入文件
            downloaded_size = 0
            with open(file_path, 'wb') as f:
                with tqdm(total=file_size, unit='B', unit_scale=True, desc=filename) as pbar:
                    for chunk in response.iter_content(chunk_size=self.download_config['chunk_size']):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            pbar.update(len(chunk))
                            
                            # 调用进度回调
                            if progress_callback:
                                progress = downloaded_size / file_size if file_size > 0 else 0
                                progress_callback(progress, downloaded_size, file_size)
            
            # 验证下载
            if file_path.exists() and file_path.stat().st_size > 0:
                logger.info(f"下载完成: {file_path}")
                return str(file_path)
            else:
                logger.error("下载失败：文件为空或不存在")
                if file_path.exists():
                    file_path.unlink()
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"下载失败: {e}")
            return None
    
    def download_with_retry(self, url: str, filename: str = None, 
                           max_retries: int = None) -> Optional[str]:
        """带重试的下载"""
        if max_retries is None:
            max_retries = self.network_config['max_retries']
        
        for attempt in range(max_retries + 1):
            try:
                result = self.download_file(url, filename)
                if result:
                    return result
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.info(f"下载失败，{wait_time}秒后重试 (尝试 {attempt + 1}/{max_retries + 1})")
                    time.sleep(wait_time)
                
            except Exception as e:
                logger.error(f"下载尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries:
                    time.sleep(2 ** attempt)
        
        logger.error(f"下载最终失败，已尝试 {max_retries + 1} 次")
        return None
    
    def batch_download(self, download_tasks: List[Dict[str, str]], 
                      progress_callback: Callable = None) -> List[Dict[str, str]]:
        """批量下载文件"""
        results = []
        total_tasks = len(download_tasks)
        
        logger.info(f"开始批量下载，共 {total_tasks} 个任务")
        
        for i, task in enumerate(download_tasks):
            try:
                url = task.get('url', '')
                filename = task.get('filename', '')
                
                if not url:
                    logger.warning(f"任务 {i+1} URL为空，跳过")
                    results.append({
                        'task': task,
                        'success': False,
                        'error': 'URL为空',
                        'file_path': None
                    })
                    continue
                
                logger.info(f"下载任务 {i+1}/{total_tasks}: {filename or url}")
                
                # 下载文件
                file_path = self.download_with_retry(url, filename)
                
                success = file_path is not None
                results.append({
                    'task': task,
                    'success': success,
                    'error': None if success else '下载失败',
                    'file_path': file_path
                })
                
                # 调用进度回调
                if progress_callback:
                    progress_callback(i + 1, total_tasks, success)
                
                # 请求间隔
                if i < total_tasks - 1:
                    time.sleep(self.network_config['delay_between_requests'])
                
            except Exception as e:
                logger.error(f"批量下载任务 {i+1} 失败: {e}")
                results.append({
                    'task': task,
                    'success': False,
                    'error': str(e),
                    'file_path': None
                })
        
        successful_downloads = sum(1 for r in results if r['success'])
        logger.info(f"批量下载完成: {successful_downloads}/{total_tasks} 成功")
        
        return results
    
    def generate_filename_from_url(self, url: str) -> str:
        """从URL生成文件名"""
        try:
            parsed_url = urlparse(url)
            filename = os.path.basename(parsed_url.path)
            
            # URL解码
            filename = unquote(filename)
            
            # 如果没有扩展名，添加.pdf
            if not filename or '.' not in filename:
                filename = f"download_{int(time.time())}.pdf"
            
            # 确保是支持的格式
            file_ext = Path(filename).suffix.lower()
            if file_ext not in self.download_config['allowed_extensions']:
                filename = Path(filename).stem + '.pdf'
            
            return filename
            
        except Exception as e:
            logger.warning(f"从URL生成文件名失败: {e}")
            return f"download_{int(time.time())}.pdf"
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        if not filename:
            return f"download_{int(time.time())}.pdf"
        
        # 移除非法字符
        import re
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # 限制长度
        max_length = 200
        if len(filename) > max_length:
            name, ext = os.path.splitext(filename)
            filename = name[:max_length - len(ext)] + ext
        
        # 确保不为空
        if not filename.strip():
            filename = f"download_{int(time.time())}.pdf"
        
        return filename
    
    def verify_download(self, file_path: str) -> bool:
        """验证下载的文件"""
        try:
            path = Path(file_path)
            
            # 检查文件是否存在
            if not path.exists():
                return False
            
            # 检查文件大小
            if path.stat().st_size == 0:
                return False
            
            # 检查文件格式（简单验证）
            file_ext = path.suffix.lower()
            if file_ext == '.pdf':
                # 检查PDF文件头
                with open(path, 'rb') as f:
                    header = f.read(4)
                    if header != b'%PDF':
                        logger.warning(f"文件可能不是有效的PDF: {file_path}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证文件失败: {e}")
            return False
    
    def get_download_info(self, url: str) -> Dict[str, any]:
        """获取下载信息"""
        try:
            response = self.session.head(url, timeout=self.network_config['timeout'])
            
            info = {
                'url': url,
                'status_code': response.status_code,
                'content_type': response.headers.get('content-type', ''),
                'content_length': int(response.headers.get('content-length', 0)),
                'filename': '',
                'downloadable': False
            }
            
            # 从Content-Disposition获取文件名
            content_disposition = response.headers.get('content-disposition', '')
            if content_disposition:
                import re
                filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
                if filename_match:
                    info['filename'] = filename_match.group(1).strip('"\'')
            
            # 检查是否可下载
            if response.status_code == 200:
                content_type = info['content_type'].lower()
                if any(ct in content_type for ct in ['pdf', 'application/pdf', 'octet-stream']):
                    info['downloadable'] = True
            
            return info
            
        except Exception as e:
            logger.error(f"获取下载信息失败: {e}")
            return {'url': url, 'error': str(e)}
    
    def cleanup_failed_downloads(self):
        """清理失败的下载文件"""
        try:
            for file_path in self.download_dir.glob('*'):
                if file_path.is_file() and file_path.stat().st_size == 0:
                    logger.info(f"删除空文件: {file_path}")
                    file_path.unlink()
        except Exception as e:
            logger.error(f"清理失败下载失败: {e}")
    
    def close(self):
        """关闭下载器"""
        self.session.close()
