# 矿物堆叠柱状图控制面板使用指南

## 概述

`MineralStackedBarChartControl` 控件现在包含完整的可视化坐标轴控制面板，位于控件顶部，高度为120像素。用户可以通过界面直接设置X轴和Y轴的参数，无需编程。

## 控制面板布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│  [X轴设置]                    [Y轴设置]                    [应用设置] [重置]    │
│  □ 手动设置X轴                □ 手动设置Y轴                                      │
│  最小值: [0  ] 最大值: [100]  最小值: [4700] 最大值: [5650]                    │
│  间隔: [20 ] 标题: [矿物含量/%] 间隔: [200 ] 标题: [深度/m]                    │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 控制元素说明

### X轴设置组
- **手动设置X轴复选框**：启用/禁用手动X轴设置
- **最小值**：X轴最小值（支持负数到正数）
- **最大值**：X轴最大值（支持负数到正数）
- **间隔**：X轴刻度间隔（1-100）
- **标题**：X轴标题文本（默认："矿物含量/%"）

### Y轴设置组
- **手动设置Y轴复选框**：启用/禁用手动Y轴设置
- **最小值**：Y轴最小值（0-10000）
- **最大值**：Y轴最大值（0-10000）
- **间隔**：Y轴刻度间隔（1-1000）
- **标题**：Y轴标题文本（默认："深度/m"）

### 控制按钮
- **应用设置**：应用当前设置到图表
- **重置**：重置所有设置为默认值并恢复自动模式

## 使用步骤

### 1. 手动设置X轴
1. 勾选"手动设置X轴"复选框
2. 设置最小值（例如：0）
3. 设置最大值（例如：100）
4. 设置间隔（例如：10，表示每10%一个刻度）
5. 修改标题（例如："矿物含量百分比/%"）
6. 点击"应用设置"按钮

### 2. 手动设置Y轴
1. 勾选"手动设置Y轴"复选框
2. 系统会自动填充当前数据的深度范围
3. 可以手动调整最小值和最大值
4. 设置间隔（例如：200，表示每200m一个刻度）
5. 修改标题（例如："井深/m"）
6. 点击"应用设置"按钮

### 3. 重置为自动模式
1. 点击"重置"按钮
2. 所有设置恢复为默认值
3. 自动模式重新启用

## 智能功能

### 自动深度填充
当启用手动Y轴设置时：
- 控件自动从数据中提取深度范围
- 自动设置最小值为数据中的最小深度
- 自动设置最大值为数据中的最大深度
- 自动计算建议的间隔值（深度范围/10，最小100m）

### 输入验证
- 最小值不能大于最大值
- 间隔值必须为正数
- 超出范围的值会被自动调整

### 实时更新
- 设置更改后点击"应用设置"立即生效
- 图表会重新绘制以反映新的坐标轴设置

## 图例优化

### 自适应布局
- 图例项自动水平均分面板宽度
- 根据面板宽度动态计算最佳列数
- 每个图例项最小宽度150像素

### 改进显示
- 颜色块大小：18x18像素
- 文字使用Microsoft YaHei字体，8号
- 自动调整行高以适应内容
- 图例面板高度自适应，最大不超过控件高度的1/4

## 使用场景示例

### 场景1：标准化显示
```
X轴设置：0-100%，间隔20%
Y轴设置：使用数据范围，间隔100m
用途：标准的矿物含量分布显示
```

### 场景2：精细分析
```
X轴设置：0-100%，间隔10%
Y轴设置：4700-5000m，间隔50m
用途：特定深度段的详细分析
```

### 场景3：对比分析
```
X轴设置：0-80%，间隔10%
Y轴设置：固定范围，间隔200m
用途：多个数据集的对比分析
```

## 注意事项

1. **数据兼容性**：确保手动设置的范围包含实际数据
2. **性能考虑**：频繁更改设置会触发图表重绘
3. **显示效果**：合理设置间隔以确保刻度清晰可读
4. **深度数据**：Y轴自动填充需要数据中包含深度列（顶深/m等）

## 故障排除

### 问题：Y轴自动填充不工作
- 检查数据表是否包含深度列
- 确认深度列名是否为"顶深/m"或其他支持的格式

### 问题：图例显示不完整
- 检查控件宽度是否足够
- 尝试调整窗体大小以提供更多空间

### 问题：设置不生效
- 确认已点击"应用设置"按钮
- 检查输入值是否在有效范围内
