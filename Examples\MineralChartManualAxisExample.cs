using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using BritSystem.Controls;

namespace BritSystem.Examples
{
    /// <summary>
    /// 矿物堆叠柱状图手动坐标轴设置示例
    /// </summary>
    public partial class MineralChartManualAxisExample : Form
    {
        private MineralStackedBarChartControl mineralChart;
        private Button btnSetManualXAxis;
        private Button btnSetManualYAxis;
        private Button btnDisableManual;
        private Button btnLoadSampleData;
        private Panel controlPanel;

        public MineralChartManualAxisExample()
        {
            InitializeComponent();
            SetupSampleData();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1200, 800);
            this.Text = "矿物堆叠柱状图手动坐标轴设置示例";
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建控制面板
            controlPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.LightGray,
                Padding = new Padding(10)
            };

            // 创建按钮
            btnLoadSampleData = new Button
            {
                Text = "加载示例数据",
                Size = new Size(120, 30),
                Location = new Point(10, 15)
            };
            btnLoadSampleData.Click += BtnLoadSampleData_Click;

            btnSetManualXAxis = new Button
            {
                Text = "设置手动X轴",
                Size = new Size(120, 30),
                Location = new Point(140, 15)
            };
            btnSetManualXAxis.Click += BtnSetManualXAxis_Click;

            btnSetManualYAxis = new Button
            {
                Text = "设置手动Y轴",
                Size = new Size(120, 30),
                Location = new Point(270, 15)
            };
            btnSetManualYAxis.Click += BtnSetManualYAxis_Click;

            btnDisableManual = new Button
            {
                Text = "恢复自动模式",
                Size = new Size(120, 30),
                Location = new Point(400, 15)
            };
            btnDisableManual.Click += BtnDisableManual_Click;

            // 添加按钮到控制面板
            controlPanel.Controls.AddRange(new Control[] {
                btnLoadSampleData, btnSetManualXAxis, btnSetManualYAxis, btnDisableManual
            });

            // 创建矿物图表控件
            mineralChart = new MineralStackedBarChartControl
            {
                Dock = DockStyle.Fill
            };

            // 添加控件到窗体
            this.Controls.Add(mineralChart);
            this.Controls.Add(controlPanel);
        }

        /// <summary>
        /// 加载示例数据
        /// </summary>
        private void SetupSampleData()
        {
            // 创建示例数据表
            DataTable sampleData = new DataTable();
            sampleData.Columns.Add("GeoID", typeof(string));
            sampleData.Columns.Add("顶深/m", typeof(double));
            sampleData.Columns.Add("底深/m", typeof(double));
            sampleData.Columns.Add("石英", typeof(double));
            sampleData.Columns.Add("长石", typeof(double));
            sampleData.Columns.Add("方解石", typeof(double));
            sampleData.Columns.Add("黏土", typeof(double));
            sampleData.Columns.Add("脆性指数", typeof(double));

            // 添加示例数据行
            var random = new Random();
            for (int i = 0; i < 20; i++)
            {
                double depth = 4700 + i * 50; // 深度从4700m到5650m
                DataRow row = sampleData.NewRow();
                row["GeoID"] = $"Sample_{i + 1:D3}";
                row["顶深/m"] = depth;
                row["底深/m"] = depth + 50;
                
                // 生成随机矿物含量（确保总和为100%）
                double quartz = random.NextDouble() * 40 + 20;    // 20-60%
                double feldspar = random.NextDouble() * 30 + 10;  // 10-40%
                double calcite = random.NextDouble() * 20 + 5;    // 5-25%
                double clay = 100 - quartz - feldspar - calcite;  // 剩余部分
                
                row["石英"] = Math.Round(quartz, 2);
                row["长石"] = Math.Round(feldspar, 2);
                row["方解石"] = Math.Round(calcite, 2);
                row["黏土"] = Math.Round(clay, 2);
                row["脆性指数"] = Math.Round((quartz + feldspar + calcite), 2);
                
                sampleData.Rows.Add(row);
            }

            // 设置矿物列表
            var brittleMinerals = new List<string> { "石英", "长石", "方解石" };
            var ductileMinerals = new List<string> { "黏土" };

            // 设置图表数据
            mineralChart.ResultData = sampleData;
            mineralChart.BrittleMinerals = brittleMinerals;
            mineralChart.DuctileMinerals = ductileMinerals;
        }

        private void BtnLoadSampleData_Click(object sender, EventArgs e)
        {
            SetupSampleData();
            MessageBox.Show("示例数据已加载！", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnSetManualXAxis_Click(object sender, EventArgs e)
        {
            // 设置手动X轴：0-100%，每10%一个刻度
            mineralChart.SetManualXAxis(0, 100, 10, "矿物含量百分比/%");
            MessageBox.Show("已设置手动X轴：0-100%，间隔10%", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnSetManualYAxis_Click(object sender, EventArgs e)
        {
            // 设置手动Y轴，使用深度数据自动填充，间隔200m
            mineralChart.SetManualYAxisWithDepthData("井深/m", 200);
            MessageBox.Show("已设置手动Y轴，使用深度数据，间隔200m", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDisableManual_Click(object sender, EventArgs e)
        {
            // 禁用手动设置，恢复自动模式
            mineralChart.DisableManualAxis();
            MessageBox.Show("已恢复自动坐标轴模式", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
