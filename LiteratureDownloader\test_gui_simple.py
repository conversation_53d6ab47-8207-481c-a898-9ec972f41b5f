#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单GUI测试
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_simple_gui():
    """测试简单GUI"""
    root = tk.Tk()
    root.title("GUI测试")
    root.geometry("400x300")
    
    # 确保窗口显示在前台
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    label = ttk.Label(root, text="如果您看到这个窗口，说明GUI正常工作！")
    label.pack(pady=50)
    
    def show_message():
        messagebox.showinfo("测试", "GUI功能正常！")
    
    button = ttk.Button(root, text="点击测试", command=show_message)
    button.pack(pady=20)
    
    def close_app():
        root.destroy()
    
    close_button = ttk.Button(root, text="关闭", command=close_app)
    close_button.pack(pady=10)
    
    print("GUI测试窗口已创建，请查看是否显示")
    root.mainloop()

if __name__ == "__main__":
    print("开始GUI测试...")
    test_simple_gui()
    print("GUI测试结束")
