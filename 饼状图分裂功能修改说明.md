# 饼状图分裂功能修改说明

## 修改概述

根据您的需求，我已经成功修改了饼状图的左键点击事件和分裂功能，实现了真正的饼状图扇形分裂效果。

## 主要修改内容

### 1. 左键点击事件逻辑优化

**修改位置**: `VisualizationForm.cs` 第866-909行

**主要改进**:
- 增加了智能分裂/还原逻辑
- 首次点击基本矿物类型（脆性/塑性）：执行分裂
- 再次点击同一类型：执行还原
- 点击分裂后的子矿物：执行还原

```csharp
// 检查是否已经处于分裂状态
if (!_isExpanded || _expandedType != pointTag)
{
    // 分裂饼块
    SplitPieChart(pointTag, hitTestResult.PointIndex);
}
else
{
    // 如果已经分裂，点击同一类型则还原
    RestorePieChart();
}
```

### 2. SplitPieChart方法核心改进

**修改位置**: `VisualizationForm.cs` 第1119-1220行

**关键改进**:

#### a) 比例调整算法
```csharp
// 调整子矿物值，使其总和等于原始点的值，保持分裂后扇形角度不变
double adjustmentFactor = (categoryTotal > 0) ? 
    _originalPointInfo.Value / categoryTotal : 1;

for (int i = 0; i < subMinerals.Count; i++)
{
    var subMineral = subMinerals[i];
    double adjustedValue = subMineral.value * adjustmentFactor;
    subMinerals[i] = (subMineral.name, adjustedValue, subMineral.color);
}
```

#### b) 标签显示优化
```csharp
Label = $"{subMineral.name}\n{(subMineral.value / _originalPointInfo.Value * 100):F1}%"
```
- 显示矿物名称和在该类别中的百分比
- 提高了可读性和用户体验

#### c) 真正的分裂效果
- 移除原始扇形
- 在相同位置插入多个小扇形
- 保持饼状图其他部分不变
- 分裂后的扇形总角度等于原始扇形角度

## 功能特点

### 1. 分裂效果
- **左键点击脆性矿物扇形** → 分裂成石英、长石、碳酸盐矿物等小扇形
- **左键点击塑性矿物扇形** → 分裂成黏土矿物等小扇形
- 分裂后的小扇形保持原始扇形的位置和总角度大小

### 2. 交互逻辑
- **首次左键点击大类矿物**: 执行分裂
- **再次左键点击同一大类矿物**: 执行还原
- **左键点击分裂后的子矿物**: 执行还原
- **右键点击**: 保持原有的分散显示功能

### 3. 视觉优化
- 分裂后的扇形显示矿物名称和在该类别中的百分比
- 使用预定义颜色确保一致性
- 鼠标悬停时有突出效果
- 保持饼状图的整体布局和比例

### 4. 数据准确性
- 分裂后的子矿物值经过调整，确保总和等于原始扇形值
- 保持饼状图的数学准确性
- 不影响其他扇形的显示

## 与右键功能的区别

| 功能 | 左键点击（分裂） | 右键点击（分散） |
|------|------------------|------------------|
| 效果 | 在原位置分裂扇形 | 重新绘制整个饼图 |
| 布局 | 保持原有布局 | 显示所有具体矿物 |
| 比例 | 保持原扇形角度 | 显示占总体比例 |
| 用途 | 查看类别内部构成 | 查看所有矿物分布 |

## 技术实现要点

1. **状态管理**: 使用`_isExpanded`和`_expandedType`跟踪分裂状态
2. **数据保存**: 使用`_originalPointInfo`保存原始点信息用于还原
3. **比例计算**: 确保分裂后子矿物值总和等于原始值
4. **视觉一致性**: 保持饼状图的整体外观和交互体验

## 测试建议

1. 启动程序并加载矿物数据
2. 进入可视化界面
3. 左键点击脆性矿物扇形，观察分裂效果
4. 再次左键点击，观察还原效果
5. 左键点击塑性矿物扇形，测试另一类型的分裂
6. 点击分裂后的子矿物，测试还原功能
7. 右键点击测试分散功能，确保不影响原有功能

修改已完成，饼状图现在支持真正的扇形分裂功能！
