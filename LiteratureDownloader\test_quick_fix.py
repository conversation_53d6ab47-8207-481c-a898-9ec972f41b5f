#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.pdf_reader import PDFReader
from modules.citation_parser import CitationParser

def test_quick_fix():
    """快速测试修复效果"""
    pdf_file = "paper.pdf"
    
    if not Path(pdf_file).exists():
        print(f"错误: 找不到文件 {pdf_file}")
        return
    
    print("快速测试修复效果...")
    print("=" * 50)
    
    # 创建PDF读取器
    pdf_reader = PDFReader()
    
    # 提取引用
    print("1. 提取PDF文本...")
    text = pdf_reader.extract_text(pdf_file)
    
    print("2. 识别文献引用...")
    citations = pdf_reader.find_citations(text)
    
    print(f"3. 识别结果: {len(citations)} 个引用")
    
    if citations:
        print("\n4. 前3个引用的基本信息:")
        for i, citation in enumerate(citations[:3]):
            ref_num = citation.get('reference_number', '?')
            authors = citation.get('authors', '未识别')
            title = citation.get('title', '未识别')
            year = citation.get('year', '未识别')
            
            print(f"\n   [{ref_num}] 引用 {i+1}:")
            print(f"   作者: {authors}")
            print(f"   标题: {title[:60]}..." if len(str(title)) > 60 else f"   标题: {title}")
            print(f"   年份: {year}")
    
    # 测试解析器
    print("\n5. 测试引用解析器...")
    parser = CitationParser()
    parsed_citations = parser.parse_multiple_citations(citations)
    
    print(f"6. 解析结果: {len(parsed_citations)} 个引用")
    
    # 统计信息
    if parsed_citations:
        valid_authors = sum(1 for c in parsed_citations if c.get('authors', '').strip())
        valid_titles = sum(1 for c in parsed_citations if c.get('title', '').strip())
        valid_years = sum(1 for c in parsed_citations if c.get('year', '').strip())
        
        print(f"\n7. 统计信息:")
        print(f"   有作者: {valid_authors}/{len(parsed_citations)}")
        print(f"   有标题: {valid_titles}/{len(parsed_citations)}")
        print(f"   有年份: {valid_years}/{len(parsed_citations)}")
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    test_quick_fix()
