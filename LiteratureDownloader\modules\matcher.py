#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文献匹配和验证模块
功能：
1. 通过作者名匹配文献
2. 通过DOI/ISBN验证文献
3. 计算文献相似度
4. 避免下载错误文献
"""

from typing import List, Dict, Optional, Tuple
from loguru import logger

try:
    from fuzzywuzzy import fuzz, process
    import re
    from datetime import datetime
except ImportError as e:
    logger.error(f"匹配模块导入失败: {e}")
    raise

from config.settings import MATCHING_CONFIG


class LiteratureMatcher:
    """文献匹配器"""
    
    def __init__(self):
        self.config = MATCHING_CONFIG
    
    def match_literature(self, target: Dict[str, str], candidates: List[Dict[str, str]]) -> List[Dict[str, any]]:
        """匹配文献"""
        matches = []
        
        for candidate in candidates:
            try:
                similarity = self.calculate_similarity(target, candidate)
                
                match_result = {
                    'candidate': candidate,
                    'similarity': similarity,
                    'title_match': similarity.get('title_similarity', 0),
                    'author_match': similarity.get('author_similarity', 0),
                    'year_match': similarity.get('year_similarity', 0),
                    'overall_score': similarity.get('overall_score', 0),
                    'is_match': similarity.get('overall_score', 0) >= self.config['similarity_threshold']
                }
                
                matches.append(match_result)
                
            except Exception as e:
                logger.warning(f"匹配文献失败: {e}")
                continue
        
        # 按相似度排序
        matches.sort(key=lambda x: x['overall_score'], reverse=True)
        
        return matches
    
    def calculate_similarity(self, target: Dict[str, str], candidate: Dict[str, str]) -> Dict[str, float]:
        """计算两个文献的相似度"""
        similarity = {
            'title_similarity': 0.0,
            'author_similarity': 0.0,
            'year_similarity': 0.0,
            'doi_match': False,
            'isbn_match': False,
            'overall_score': 0.0
        }
        
        # 标题相似度
        target_title = target.get('title', '').strip()
        candidate_title = candidate.get('title', '').strip()
        
        if target_title and candidate_title:
            similarity['title_similarity'] = fuzz.ratio(
                target_title.lower(), candidate_title.lower()
            ) / 100.0
        
        # 作者相似度
        target_authors = self.normalize_authors(target.get('authors', []))
        candidate_authors = self.normalize_authors(candidate.get('authors', []))
        
        if target_authors and candidate_authors:
            similarity['author_similarity'] = self.calculate_author_similarity(
                target_authors, candidate_authors
            )
        
        # 年份匹配
        target_year = target.get('year', '').strip()
        candidate_year = candidate.get('year', '').strip()
        
        if target_year and candidate_year:
            try:
                year_diff = abs(int(target_year) - int(candidate_year))
                similarity['year_similarity'] = max(0, 1 - year_diff / 5.0)  # 5年内认为相似
            except ValueError:
                similarity['year_similarity'] = 0.0
        
        # DOI精确匹配
        target_doi = target.get('doi', '').strip()
        candidate_doi = candidate.get('doi', '').strip()
        
        if target_doi and candidate_doi:
            similarity['doi_match'] = target_doi.lower() == candidate_doi.lower()
            if similarity['doi_match']:
                similarity['overall_score'] = 1.0  # DOI匹配则认为是同一文献
                return similarity
        
        # ISBN精确匹配
        target_isbn = target.get('isbn', '').strip()
        candidate_isbn = candidate.get('isbn', '').strip()
        
        if target_isbn and candidate_isbn:
            similarity['isbn_match'] = self.normalize_isbn(target_isbn) == self.normalize_isbn(candidate_isbn)
            if similarity['isbn_match']:
                similarity['overall_score'] = 1.0  # ISBN匹配则认为是同一文献
                return similarity
        
        # 计算综合相似度
        similarity['overall_score'] = (
            similarity['title_similarity'] * self.config['title_weight'] +
            similarity['author_similarity'] * self.config['author_weight'] +
            similarity['year_similarity'] * self.config['year_weight']
        )
        
        return similarity
    
    def normalize_authors(self, authors) -> List[str]:
        """标准化作者名列表"""
        if isinstance(authors, str):
            # 如果是字符串，分割成列表
            authors = re.split(r'[,;&]|\sand\s', authors)
        
        normalized = []
        for author in authors:
            if isinstance(author, str):
                normalized_author = self.normalize_author_name(author.strip())
                if normalized_author:
                    normalized.append(normalized_author)
        
        return normalized
    
    def normalize_author_name(self, author_name: str) -> str:
        """标准化单个作者姓名"""
        if not author_name:
            return ""
        
        # 移除多余空格
        author_name = re.sub(r'\s+', ' ', author_name.strip())
        
        # 移除标点符号
        author_name = re.sub(r'[^\w\s\-\']', '', author_name)
        
        # 转换为小写用于比较
        return author_name.lower()
    
    def calculate_author_similarity(self, authors1: List[str], authors2: List[str]) -> float:
        """计算作者列表相似度"""
        if not authors1 or not authors2:
            return 0.0
        
        # 计算每个作者的最佳匹配
        total_similarity = 0.0
        matched_count = 0
        
        for author1 in authors1:
            best_match = 0.0
            for author2 in authors2:
                similarity = fuzz.ratio(author1, author2) / 100.0
                best_match = max(best_match, similarity)
            
            if best_match > 0.6:  # 阈值
                total_similarity += best_match
                matched_count += 1
        
        if matched_count == 0:
            return 0.0
        
        # 考虑匹配的作者数量
        coverage = matched_count / max(len(authors1), len(authors2))
        average_similarity = total_similarity / matched_count
        
        return average_similarity * coverage
    
    def normalize_isbn(self, isbn: str) -> str:
        """标准化ISBN"""
        if not isbn:
            return ""
        
        # 移除所有非数字字符
        isbn = re.sub(r'[^\d]', '', isbn)
        
        # ISBN-10转ISBN-13
        if len(isbn) == 10:
            isbn = '978' + isbn[:-1]
            # 重新计算校验位
            check_sum = sum((i + 1) * int(digit) for i, digit in enumerate(isbn)) % 11
            check_digit = 0 if check_sum == 0 else 11 - check_sum
            isbn += str(check_digit)
        
        return isbn
    
    def verify_doi(self, doi: str) -> bool:
        """验证DOI格式"""
        if not doi:
            return False
        
        # DOI格式验证
        doi_pattern = r'^10\.\d+\/[^\s]+$'
        return bool(re.match(doi_pattern, doi.strip()))
    
    def verify_isbn(self, isbn: str) -> bool:
        """验证ISBN格式"""
        if not isbn:
            return False
        
        normalized_isbn = self.normalize_isbn(isbn)
        
        # 检查长度
        if len(normalized_isbn) not in [10, 13]:
            return False
        
        # 简单校验（这里可以添加更复杂的校验算法）
        return normalized_isbn.isdigit()
    
    def find_best_match(self, target: Dict[str, str], candidates: List[Dict[str, str]]) -> Optional[Dict[str, any]]:
        """找到最佳匹配"""
        matches = self.match_literature(target, candidates)
        
        if not matches:
            return None
        
        best_match = matches[0]
        
        # 检查是否达到阈值
        if best_match['overall_score'] >= self.config['similarity_threshold']:
            return best_match
        
        return None
    
    def filter_matches(self, matches: List[Dict[str, any]], min_score: float = None) -> List[Dict[str, any]]:
        """过滤匹配结果"""
        if min_score is None:
            min_score = self.config['similarity_threshold']
        
        filtered = [match for match in matches if match['overall_score'] >= min_score]
        
        return filtered
    
    def validate_literature_info(self, literature: Dict[str, str]) -> Dict[str, any]:
        """验证文献信息的完整性和有效性"""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'completeness_score': 0.0
        }
        
        required_fields = ['title', 'authors']
        optional_fields = ['year', 'journal', 'doi', 'isbn']
        
        # 检查必需字段
        for field in required_fields:
            if not literature.get(field, '').strip():
                validation['errors'].append(f"缺少必需字段: {field}")
                validation['is_valid'] = False
        
        # 检查年份格式
        year = literature.get('year', '').strip()
        if year:
            try:
                year_int = int(year)
                current_year = datetime.now().year
                if not (1900 <= year_int <= current_year + 1):
                    validation['warnings'].append(f"年份可能不正确: {year}")
            except ValueError:
                validation['warnings'].append(f"年份格式错误: {year}")
        
        # 检查DOI格式
        doi = literature.get('doi', '').strip()
        if doi and not self.verify_doi(doi):
            validation['warnings'].append(f"DOI格式可能错误: {doi}")
        
        # 检查ISBN格式
        isbn = literature.get('isbn', '').strip()
        if isbn and not self.verify_isbn(isbn):
            validation['warnings'].append(f"ISBN格式可能错误: {isbn}")
        
        # 计算完整性分数
        total_fields = len(required_fields) + len(optional_fields)
        filled_fields = sum(1 for field in required_fields + optional_fields 
                           if literature.get(field, '').strip())
        validation['completeness_score'] = filled_fields / total_fields
        
        return validation
