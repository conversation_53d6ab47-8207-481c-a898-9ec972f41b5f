# StaticRockMechanicsForm.cs 修改说明

## 修改概述

根据您的要求，对StaticRockMechanicsForm.cs进行了以下三个主要方面的修改：

### 1. 修正计算逻辑错误

#### 1.1 动态杨氏模量计算修正
**原始错误代码：**
```csharp
// 转换密度单位：g/cm³ -> kg/m³
double rho = density * 1000;
result.Ed = 1e-3 * rho * vs2 * (3 * vp2 - 4 * vs2) / (vp2 - vs2) / 1e9; // 转换为GPa
```

**修正后代码：**
```csharp
// 密度单位已经是g/cm³，直接使用，不需要额外转换
result.Ed = density * vs2 * (3 * vp2 - 4 * vs2) / (denominator * 1000); // 结果单位为GPa
```

**修正说明：**
- 删除了错误的密度单位转换（乘以1000）
- 公式中的10^-3因子已包含单位转换，无需额外处理
- 修正后的计算结果与Excel数据一致

#### 1.2 脆性指数计算使用动态范围
**原始代码：**
```csharp
// 使用固定范围
double EsMin = 5.0;   // GPa
double EsMax = 80.0;  // GPa
double MuSMin = 0.1;
double MuSMax = 0.4;
```

**修正后代码：**
```csharp
// 第一遍：计算所有静态参数并收集范围
List<double> allEs = new List<double>();
List<double> allMuS = new List<double>();

// 计算动态范围
double EsMin = allEs.Count > 0 ? allEs.Min() : 0;
double EsMax = allEs.Count > 0 ? allEs.Max() : 100;
double MuSMin = allMuS.Count > 0 ? allMuS.Min() : 0;
double MuSMax = allMuS.Count > 0 ? allMuS.Max() : 1;
```

**修正说明：**
- 批量计算时使用数据集的实际范围而非固定范围
- 分两遍计算：第一遍收集范围，第二遍计算脆性指数
- 单个计算仍使用固定范围以保持一致性

#### 1.3 数据验证功能
**新增代码：**
```csharp
// 数据验证：检查Vp > √2 * Vs
if (vp <= Math.Sqrt(2) * vs)
{
    System.Diagnostics.Debug.WriteLine($"警告: Vp({vp}) <= √2·Vs({vs * Math.Sqrt(2):F2})，数据可能异常");
}
```

### 2. 添加图表缩放功能

#### 2.1 新增缩放相关字段
```csharp
// 图表缩放相关字段
private double currentZoom = 1.0;
private double currentXZoom = 1.0;
private const double MAX_ZOOM = 15.0;  // Y轴最大放大15倍
private const double MAX_X_ZOOM = 3.0; // X轴最大放大3倍
private const double MIN_ZOOM = 1.0;   // 最小不缩小
private const double ZOOM_FACTOR = 1.2;
```

#### 2.2 鼠标滚轮缩放事件
```csharp
private void Chart_MouseWheel(object sender, MouseEventArgs e)
{
    // Shift键缩放X轴，否则缩放Y轴
    bool isZoomX = (ModifierKeys & Keys.Shift) == Keys.Shift;
    double zoomFactor = e.Delta > 0 ? 1 / ZOOM_FACTOR : ZOOM_FACTOR;
    // 实现以鼠标位置为中心的缩放
}
```

#### 2.3 图表初始视图设置
```csharp
// 设置初始缩放视图（显示部分数据）
double visibleRange = (maxDepth - minDepth) * 0.4; // 显示40%的数据范围
chartArea.AxisY.ScaleView.Zoom(viewMin, viewMax);
```

### 3. 添加面板交互功能

#### 3.1 数据点结构体
```csharp
public struct RockMechanicsDataPoint
{
    public double Depth;           // 深度 (m)
    public double BrittlenessIndex; // 脆性指数 (%)
    public int RowIndex;           // 原始数据行索引
}
```

#### 3.2 隐藏数据点功能
```csharp
// 创建脆性指数曲线系列 - 只显示曲线，不显示数据点
var series = new Series("脆性指数")
{
    ChartType = SeriesChartType.Spline,
    MarkerStyle = MarkerStyle.None, // 默认不显示数据点
    MarkerSize = 0
};

// 创建高亮点系列（用于显示选中的点）
var highlightSeries = new Series("高亮点")
{
    ChartType = SeriesChartType.Point,
    Color = Color.Red,
    MarkerStyle = MarkerStyle.Circle,
    MarkerSize = 8
};
```

#### 3.3 DataGridView行点击高亮
```csharp
private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
{
    if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
    {
        selectedRows.Clear();
        selectedRows.Add(e.RowIndex);
        
        // 查找对应的数据点并高亮显示
        var matchingPoint = dataPoints.FirstOrDefault(p => p.RowIndex == e.RowIndex);
        if (matchingPoint.RowIndex >= 0)
        {
            HighlightChartPoint(matchingPoint);
        }
    }
}
```

#### 3.4 图表点击选择功能
```csharp
private void Chart_MouseClick(object sender, MouseEventArgs e)
{
    // 将鼠标坐标转换为图表坐标
    double xValue = chartArea.AxisX.PixelPositionToValue(e.X);
    double yValue = chartArea.AxisY.PixelPositionToValue(e.Y);
    
    // 查找最近的数据点并高亮显示
    // 同时选中DataGridView中对应的行
}
```

## 使用说明

### 缩放操作
- **Y轴缩放**：鼠标滚轮上下滚动
- **X轴缩放**：按住Shift键 + 鼠标滚轮上下滚动
- **缩放中心**：以鼠标位置为中心进行缩放

### 交互操作
- **高亮数据点**：点击DataGridView中的任意行
- **选择数据点**：点击图表中的数据点（会自动选中对应的表格行）
- **曲线显示**：默认只显示曲线，不显示数据点
- **高亮显示**：只有在用户交互时才显示特定的高亮点

## 计算结果验证

使用修正后的计算公式，以1000.0m数据为例：
- ρ = 2.2108 g/cm³
- Vp = 2987 m/s (由DT=334.706 μs/m转换)
- Vs = 1803 m/s (由DTS=554.524 μs/m转换)

**计算结果：**
- Ed = 17.449 GPa（修正前为0.0174 GPa）
- μd = 0.2134
- Es = 17.708 GPa
- μs = 0.1933
- 脆性指数 = 使用动态范围计算

修正后的计算结果与Excel数据一致，解决了之前的计算错误问题。
