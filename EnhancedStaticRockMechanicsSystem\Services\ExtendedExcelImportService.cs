using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using EnhancedStaticRockMechanicsSystem.Models;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 扩展的Excel导入服务
    /// </summary>
    public class ExtendedExcelImportService
    {
        /// <summary>
        /// 从Excel文件导入对比数据
        /// </summary>
        public async Task<ComparisonDataSet> ImportComparisonDataFromExcel(string filePath)
        {
            try
            {
                var fileInfo = ComparisonFileParser.ParseFileName(filePath);
                var dataSet = new ComparisonDataSet
                {
                    SystemName = fileInfo.SystemName,
                    DataSource = filePath,
                    ImportTime = DateTime.Now,
                    DataPoints = new List<BrittlenessDataPoint>()
                };

                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = CreateWorkbook(filePath, fileStream);
                    
                    // 尝试找到包含对比数据的工作表
                    ISheet dataSheet = FindDataSheet(workbook);
                    if (dataSheet == null)
                    {
                        throw new InvalidDataException("未找到包含对比数据的工作表");
                    }

                    // 解析数据
                    var excelData = ParseExcelSheet(dataSheet);
                    var columnMapping = DetectColumnMapping(excelData.Headers);
                    
                    // 转换数据
                    foreach (var row in excelData.DataRows)
                    {
                        var dataPoint = ConvertRowToDataPoint(row, columnMapping);
                        if (dataPoint != null)
                        {
                            dataSet.DataPoints.Add(dataPoint);
                        }
                    }
                }

                LoggingService.Instance.Info($"成功从Excel导入 {dataSet.DataPoints.Count} 个数据点");
                return dataSet;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"Excel导入失败: {ex.Message}");
                throw;
            }
        }

        private IWorkbook CreateWorkbook(string filePath, FileStream fileStream)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            return extension == ".xlsx" ? new XSSFWorkbook(fileStream) : new HSSFWorkbook(fileStream);
        }

        private ISheet FindDataSheet(IWorkbook workbook)
        {
            // 优先级顺序查找工作表
            var sheetNames = new[] { "对比数据", "ComparisonData", "数据", "Data", "Sheet1" };
            
            foreach (var sheetName in sheetNames)
            {
                var sheet = workbook.GetSheet(sheetName);
                if (sheet != null && HasValidData(sheet))
                    return sheet;
            }

            // 如果没有找到指定名称的工作表，检查所有工作表
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                var sheet = workbook.GetSheetAt(i);
                if (HasValidData(sheet))
                    return sheet;
            }

            return null;
        }

        private bool HasValidData(ISheet sheet)
        {
            if (sheet.LastRowNum < 1) return false;

            // 检查前几行是否包含相关关键词
            for (int i = 0; i <= Math.Min(5, sheet.LastRowNum); i++)
            {
                var row = sheet.GetRow(i);
                if (row == null) continue;

                for (int j = 0; j < row.LastCellNum; j++)
                {
                    var cell = row.GetCell(j);
                    if (cell?.CellType == CellType.String)
                    {
                        string value = cell.StringCellValue.ToLower();
                        if (value.Contains("深度") || value.Contains("脆性") || 
                            value.Contains("depth") || value.Contains("brittle"))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        private ExcelData ParseExcelSheet(ISheet sheet)
        {
            var excelData = new ExcelData();
            
            // 查找表头行
            int headerRowIndex = FindHeaderRow(sheet);
            if (headerRowIndex == -1)
                throw new InvalidDataException("未找到有效的表头行");

            var headerRow = sheet.GetRow(headerRowIndex);
            excelData.Headers = new string[headerRow.LastCellNum];
            
            for (int i = 0; i < headerRow.LastCellNum; i++)
            {
                var cell = headerRow.GetCell(i);
                excelData.Headers[i] = cell?.ToString()?.Trim() ?? $"Column{i}";
            }

            // 读取数据行
            for (int i = headerRowIndex + 1; i <= sheet.LastRowNum; i++)
            {
                var row = sheet.GetRow(i);
                if (row == null) continue;

                var values = new object[excelData.Headers.Length];
                bool hasData = false;

                for (int j = 0; j < excelData.Headers.Length; j++)
                {
                    var cell = row.GetCell(j);
                    if (cell != null)
                    {
                        values[j] = GetCellValue(cell);
                        if (values[j] != null) hasData = true;
                    }
                }

                if (hasData)
                {
                    excelData.DataRows.Add(values);
                }
            }

            return excelData;
        }

        private int FindHeaderRow(ISheet sheet)
        {
            for (int i = 0; i <= Math.Min(10, sheet.LastRowNum); i++)
            {
                var row = sheet.GetRow(i);
                if (row == null) continue;

                int validHeaders = 0;
                for (int j = 0; j < row.LastCellNum; j++)
                {
                    var cell = row.GetCell(j);
                    if (cell?.CellType == CellType.String)
                    {
                        string value = cell.StringCellValue.ToLower();
                        if (value.Contains("深度") || value.Contains("脆性") || 
                            value.Contains("depth") || value.Contains("brittle") ||
                            value.Contains("序号") || value.Contains("index"))
                        {
                            validHeaders++;
                        }
                    }
                }

                if (validHeaders >= 2) // 至少包含2个相关列头
                    return i;
            }

            return -1;
        }

        private object GetCellValue(ICell cell)
        {
            switch (cell.CellType)
            {
                case CellType.Numeric:
                    return DateUtil.IsCellDateFormatted(cell) ? cell.DateCellValue : cell.NumericCellValue;
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Formula:
                    try
                    {
                        return cell.NumericCellValue;
                    }
                    catch
                    {
                        return cell.StringCellValue;
                    }
                default:
                    return null;
            }
        }

        private ColumnMapping DetectColumnMapping(string[] headers)
        {
            var mapping = new ColumnMapping();
            
            for (int i = 0; i < headers.Length; i++)
            {
                string header = headers[i].ToLower().Trim();
                
                if (header.Contains("顶深") || header.Contains("top") && header.Contains("depth"))
                    mapping.TopDepthIndex = i;
                else if (header.Contains("底深") || header.Contains("bottom") && header.Contains("depth"))
                    mapping.BottomDepthIndex = i;
                else if (header.Contains("深度") || header.Contains("depth"))
                    mapping.DepthIndex = i;
                else if (header.Contains("脆性指数") || header.Contains("brittleness") || header.Contains("脆性"))
                    mapping.BrittlenessIndex = i;
                else if (header.Contains("系统") || header.Contains("方法") || header.Contains("来源"))
                    mapping.SystemNameIndex = i;
            }

            return mapping;
        }

        private BrittlenessDataPoint ConvertRowToDataPoint(object[] values, ColumnMapping mapping)
        {
            try
            {
                var dataPoint = new BrittlenessDataPoint();
                
                // 解析深度
                if (mapping.TopDepthIndex >= 0 && mapping.BottomDepthIndex >= 0)
                {
                    dataPoint.TopDepth = ConvertToDouble(values[mapping.TopDepthIndex]);
                    dataPoint.BottomDepth = ConvertToDouble(values[mapping.BottomDepthIndex]);
                }
                else if (mapping.DepthIndex >= 0)
                {
                    var depth = ConvertToDouble(values[mapping.DepthIndex]);
                    dataPoint.TopDepth = depth;
                    dataPoint.BottomDepth = depth;
                }
                
                // 解析脆性指数
                if (mapping.BrittlenessIndex >= 0)
                {
                    dataPoint.BrittleIndex = ConvertToDouble(values[mapping.BrittlenessIndex]);
                }
                
                // 验证数据有效性
                if (dataPoint.IsValid())
                {
                    dataPoint.GenerateGeoID();
                    return dataPoint;
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"转换Excel数据行失败: {ex.Message}");
            }
            
            return null;
        }

        private double ConvertToDouble(object value)
        {
            if (value == null) return 0;
            
            if (value is double d) return d;
            if (value is int i) return i;
            if (value is float f) return f;
            
            if (value is string s)
            {
                s = s.Trim().Replace("m", "").Replace("%", "");
                if (double.TryParse(s, out double result))
                    return result;
            }
            
            return 0;
        }
    }

    /// <summary>
    /// Excel数据结构
    /// </summary>
    public class ExcelData
    {
        public string[] Headers { get; set; }
        public List<object[]> DataRows { get; set; } = new List<object[]>();
    }
}
