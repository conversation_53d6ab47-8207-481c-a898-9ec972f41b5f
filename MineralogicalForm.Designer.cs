﻿namespace BritSystem
{
    partial class MineralogicalForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // 控件声明
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.Button btnLogout;
        private System.Windows.Forms.Panel pnlInput;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.Panel pnlChart;
        private System.Windows.Forms.DataGridView dgvMineralData;
        private System.Windows.Forms.TextBox txtQuartz;
        private System.Windows.Forms.TextBox txtFeldspar;
        private System.Windows.Forms.TextBox txtCarbonate;
        private System.Windows.Forms.TextBox txtClay;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnGenerateCurve;
        private System.Windows.Forms.Button btnDeletePoint;
        private System.Windows.Forms.Button btnSaveCurve;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Button btnEmergencyExit;
        private System.Windows.Forms.Button btnAlgFormulaCal;
        private System.Windows.Forms.Button btnViewComparison;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            lblTitle = new Label();
            btnBack = new Button();
            btnLogout = new Button();
            pnlInput = new Panel();
            lblSearchTitle = new Label();
            lblInputTitle = new Label();
            lblQuartz = new Label();
            txtQuartz = new TextBox();
            lblFeldspar = new Label();
            txtFeldspar = new TextBox();
            lblCarbonate = new Label();
            txtCarbonate = new TextBox();
            lblClay = new Label();
            txtClay = new TextBox();
            btnCalculate = new Button();
            lblResult = new Label();
            lblSearchColumn = new Label();
            cboSearchColumn = new ComboBox();
            lblMinValue = new Label();
            txtMinValue = new TextBox();
            lblMaxValue = new Label();
            txtMaxValue = new TextBox();
            btnSearch = new Button();
            btnResetData = new Button();
            pnlDivider = new Panel();
            pnlData = new Panel();
            lblDataTitle = new Label();
            btnImport = new Button();
            btnExport = new Button();
            dgvMineralData = new DataGridView();
            pnlChart = new Panel();
            lblChartTitle = new Label();
            btnGenerateCurve = new Button();
            btnDeletePoint = new Button();
            btnReset = new Button();
            btnSaveCurve = new Button();
            btnEmergencyExit = new Button();
            lblWelcome = new Label();
            btnAlgFormulaCal = new Button();
            btnViewComparison = new Button();
            pnlInput.SuspendLayout();
            pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMineralData).BeginInit();
            pnlChart.SuspendLayout();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 20F, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Padding = new Padding(0, 10, 0, 0);
            lblTitle.Size = new Size(1439, 97);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "脆性指数系统 - 矿物组分法";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // btnBack
            // 
            btnBack.BackColor = Color.FromArgb(0, 120, 212);
            btnBack.FlatStyle = FlatStyle.Flat;
            btnBack.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnBack.ForeColor = Color.White;
            btnBack.Location = new Point(20, 100);
            btnBack.Name = "btnBack";
            btnBack.Size = new Size(150, 40);
            btnBack.TabIndex = 2;
            btnBack.Text = "返回仪表盘";
            btnBack.UseVisualStyleBackColor = false;
            // 
            // btnLogout
            // 
            btnLogout.BackColor = Color.FromArgb(153, 153, 153);
            btnLogout.FlatStyle = FlatStyle.Flat;
            btnLogout.Font = new Font("微软雅黑", 10F);
            btnLogout.ForeColor = Color.White;
            btnLogout.Location = new Point(1271, 12);
            btnLogout.Name = "btnLogout";
            btnLogout.Size = new Size(150, 40);
            btnLogout.TabIndex = 3;
            btnLogout.Text = "退出登录";
            btnLogout.UseVisualStyleBackColor = false;
            // 
            // pnlInput
            // 
            pnlInput.BackColor = Color.FromArgb(45, 45, 45);
            pnlInput.BorderStyle = BorderStyle.FixedSingle;
            pnlInput.Controls.Add(lblSearchTitle);
            pnlInput.Controls.Add(lblInputTitle);
            pnlInput.Controls.Add(lblQuartz);
            pnlInput.Controls.Add(txtQuartz);
            pnlInput.Controls.Add(lblFeldspar);
            pnlInput.Controls.Add(txtFeldspar);
            pnlInput.Controls.Add(lblCarbonate);
            pnlInput.Controls.Add(txtCarbonate);
            pnlInput.Controls.Add(lblClay);
            pnlInput.Controls.Add(txtClay);
            pnlInput.Controls.Add(btnCalculate);
            pnlInput.Controls.Add(lblResult);
            pnlInput.Controls.Add(lblSearchColumn);
            pnlInput.Controls.Add(cboSearchColumn);
            pnlInput.Controls.Add(lblMinValue);
            pnlInput.Controls.Add(txtMinValue);
            pnlInput.Controls.Add(lblMaxValue);
            pnlInput.Controls.Add(txtMaxValue);
            pnlInput.Controls.Add(btnSearch);
            pnlInput.Controls.Add(btnResetData);
            pnlInput.Controls.Add(pnlDivider);
            pnlInput.Location = new Point(20, 160);
            pnlInput.Name = "pnlInput";
            pnlInput.Size = new Size(1401, 131);
            pnlInput.TabIndex = 4;
            pnlInput.Paint += pnlInput_Paint;
            // 
            // lblSearchTitle
            // 
            lblSearchTitle.BackColor = Color.FromArgb(45, 45, 45);
            lblSearchTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            lblSearchTitle.ForeColor = Color.White;
            lblSearchTitle.Location = new Point(695, 5);
            lblSearchTitle.Name = "lblSearchTitle";
            lblSearchTitle.Size = new Size(124, 35);
            lblSearchTitle.TabIndex = 20;
            lblSearchTitle.Text = "数据搜索";
            // 
            // lblInputTitle
            // 
            lblInputTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            lblInputTitle.ForeColor = Color.White;
            lblInputTitle.Location = new Point(6, 0);
            lblInputTitle.Name = "lblInputTitle";
            lblInputTitle.Size = new Size(200, 35);
            lblInputTitle.TabIndex = 0;
            lblInputTitle.Text = "输入矿物组分 (%)";
            // 
            // lblQuartz
            // 
            lblQuartz.Font = new Font("微软雅黑", 10F);
            lblQuartz.ForeColor = Color.White;
            lblQuartz.Location = new Point(10, 45);
            lblQuartz.Name = "lblQuartz";
            lblQuartz.Size = new Size(80, 34);
            lblQuartz.TabIndex = 1;
            lblQuartz.Text = "石英:";
            // 
            // txtQuartz
            // 
            txtQuartz.Font = new Font("微软雅黑", 10F);
            txtQuartz.Location = new Point(90, 45);
            txtQuartz.Name = "txtQuartz";
            txtQuartz.Size = new Size(67, 34);
            txtQuartz.TabIndex = 2;
            // 
            // lblFeldspar
            // 
            lblFeldspar.Font = new Font("微软雅黑", 10F);
            lblFeldspar.ForeColor = Color.White;
            lblFeldspar.Location = new Point(180, 46);
            lblFeldspar.Name = "lblFeldspar";
            lblFeldspar.Size = new Size(62, 25);
            lblFeldspar.TabIndex = 3;
            lblFeldspar.Text = "长石:";
            // 
            // txtFeldspar
            // 
            txtFeldspar.Font = new Font("微软雅黑", 10F);
            txtFeldspar.Location = new Point(254, 45);
            txtFeldspar.Name = "txtFeldspar";
            txtFeldspar.Size = new Size(73, 34);
            txtFeldspar.TabIndex = 4;
            // 
            // lblCarbonate
            // 
            lblCarbonate.Font = new Font("微软雅黑", 10F);
            lblCarbonate.ForeColor = Color.White;
            lblCarbonate.Location = new Point(347, 45);
            lblCarbonate.Name = "lblCarbonate";
            lblCarbonate.Size = new Size(80, 25);
            lblCarbonate.TabIndex = 5;
            lblCarbonate.Text = "碳酸盐:";
            // 
            // txtCarbonate
            // 
            txtCarbonate.Font = new Font("微软雅黑", 10F);
            txtCarbonate.Location = new Point(428, 45);
            txtCarbonate.Name = "txtCarbonate";
            txtCarbonate.Size = new Size(80, 34);
            txtCarbonate.TabIndex = 6;
            // 
            // lblClay
            // 
            lblClay.Font = new Font("微软雅黑", 10F);
            lblClay.ForeColor = Color.White;
            lblClay.Location = new Point(516, 46);
            lblClay.Name = "lblClay";
            lblClay.Size = new Size(68, 25);
            lblClay.TabIndex = 7;
            lblClay.Text = "粘土:";
            // 
            // txtClay
            // 
            txtClay.Font = new Font("微软雅黑", 10F);
            txtClay.Location = new Point(584, 45);
            txtClay.Name = "txtClay";
            txtClay.Size = new Size(80, 34);
            txtClay.TabIndex = 8;
            // 
            // btnCalculate
            // 
            btnCalculate.BackColor = Color.Cyan;
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnCalculate.ForeColor = Color.Black;
            btnCalculate.Location = new Point(20, 89);
            btnCalculate.Name = "btnCalculate";
            btnCalculate.Size = new Size(150, 35);
            btnCalculate.TabIndex = 9;
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.UseVisualStyleBackColor = false;
            // 
            // lblResult
            // 
            lblResult.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            lblResult.ForeColor = Color.Cyan;
            lblResult.Location = new Point(176, 91);
            lblResult.Name = "lblResult";
            lblResult.Size = new Size(251, 35);
            lblResult.TabIndex = 10;
            lblResult.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // lblSearchColumn
            // 
            lblSearchColumn.AutoSize = true;
            lblSearchColumn.Font = new Font("微软雅黑", 10F);
            lblSearchColumn.ForeColor = Color.White;
            lblSearchColumn.Location = new Point(700, 49);
            lblSearchColumn.Name = "lblSearchColumn";
            lblSearchColumn.Size = new Size(77, 27);
            lblSearchColumn.TabIndex = 12;
            lblSearchColumn.Text = "选择列:";
            // 
            // cboSearchColumn
            // 
            cboSearchColumn.BackColor = Color.FromArgb(60, 60, 60);
            cboSearchColumn.DropDownStyle = ComboBoxStyle.DropDownList;
            cboSearchColumn.ForeColor = Color.White;
            cboSearchColumn.FormattingEnabled = true;
            cboSearchColumn.Location = new Point(791, 47);
            cboSearchColumn.Name = "cboSearchColumn";
            cboSearchColumn.Size = new Size(120, 32);
            cboSearchColumn.TabIndex = 13;
            // 
            // lblMinValue
            // 
            lblMinValue.AutoSize = true;
            lblMinValue.Font = new Font("微软雅黑", 10F);
            lblMinValue.ForeColor = Color.White;
            lblMinValue.Location = new Point(951, 47);
            lblMinValue.Name = "lblMinValue";
            lblMinValue.Size = new Size(77, 27);
            lblMinValue.TabIndex = 14;
            lblMinValue.Text = "最小值:";
            // 
            // txtMinValue
            // 
            txtMinValue.BackColor = Color.FromArgb(60, 60, 60);
            txtMinValue.ForeColor = Color.White;
            txtMinValue.Location = new Point(1044, 45);
            txtMinValue.Name = "txtMinValue";
            txtMinValue.Size = new Size(117, 30);
            txtMinValue.TabIndex = 15;
            // 
            // lblMaxValue
            // 
            lblMaxValue.AutoSize = true;
            lblMaxValue.Font = new Font("微软雅黑", 10F);
            lblMaxValue.ForeColor = Color.White;
            lblMaxValue.Location = new Point(1189, 47);
            lblMaxValue.Name = "lblMaxValue";
            lblMaxValue.Size = new Size(77, 27);
            lblMaxValue.TabIndex = 16;
            lblMaxValue.Text = "最大值:";
            // 
            // txtMaxValue
            // 
            txtMaxValue.BackColor = Color.FromArgb(60, 60, 60);
            txtMaxValue.ForeColor = Color.White;
            txtMaxValue.Location = new Point(1282, 45);
            txtMaxValue.Name = "txtMaxValue";
            txtMaxValue.Size = new Size(106, 30);
            txtMaxValue.TabIndex = 17;
            // 
            // btnSearch
            // 
            btnSearch.BackColor = Color.DarkSlateGray;
            btnSearch.FlatAppearance.BorderSize = 0;
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnSearch.ForeColor = Color.White;
            btnSearch.Location = new Point(1269, 89);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(116, 35);
            btnSearch.TabIndex = 18;
            btnSearch.Text = "搜索";
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += BtnSearch_Click;
            // 
            // btnResetData
            // 
            btnResetData.BackColor = Color.DarkSlateGray;
            btnResetData.FlatAppearance.BorderSize = 0;
            btnResetData.FlatStyle = FlatStyle.Flat;
            btnResetData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnResetData.ForeColor = Color.White;
            btnResetData.Location = new Point(1045, 89);
            btnResetData.Name = "btnResetData";
            btnResetData.Size = new Size(118, 35);
            btnResetData.TabIndex = 19;
            btnResetData.Text = "还原数据";
            btnResetData.UseVisualStyleBackColor = false;
            btnResetData.Click += BtnResetData_Click;
            // 
            // pnlDivider
            // 
            pnlDivider.BackColor = Color.FromArgb(80, 80, 80);
            pnlDivider.Location = new Point(680, 0);
            pnlDivider.Name = "pnlDivider";
            pnlDivider.Size = new Size(2, 132);
            pnlDivider.TabIndex = 21;
            // 
            // pnlData
            // 
            pnlData.BackColor = Color.FromArgb(45, 45, 45);
            pnlData.BorderStyle = BorderStyle.FixedSingle;
            pnlData.Controls.Add(lblDataTitle);
            pnlData.Controls.Add(btnImport);
            pnlData.Controls.Add(btnExport);
            pnlData.Controls.Add(dgvMineralData);
            pnlData.Location = new Point(20, 297);
            pnlData.Name = "pnlData";
            pnlData.Size = new Size(577, 620);
            pnlData.TabIndex = 5;
            // 
            // lblDataTitle
            // 
            lblDataTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            lblDataTitle.ForeColor = Color.White;
            lblDataTitle.Location = new Point(10, 5);
            lblDataTitle.Name = "lblDataTitle";
            lblDataTitle.Size = new Size(200, 37);
            lblDataTitle.TabIndex = 0;
            lblDataTitle.Text = "矿物组分数据";
            // 
            // btnImport
            // 
            btnImport.BackColor = Color.FromArgb(0, 120, 212);
            btnImport.FlatStyle = FlatStyle.Flat;
            btnImport.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnImport.ForeColor = Color.White;
            btnImport.Location = new Point(294, 5);
            btnImport.Name = "btnImport";
            btnImport.Size = new Size(110, 34);
            btnImport.TabIndex = 1;
            btnImport.Text = "导入数据";
            btnImport.UseVisualStyleBackColor = false;
            // 
            // btnExport
            // 
            btnExport.BackColor = Color.FromArgb(0, 120, 212);
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnExport.ForeColor = Color.White;
            btnExport.Location = new Point(439, 5);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(110, 37);
            btnExport.TabIndex = 2;
            btnExport.Text = "导出数据";
            btnExport.UseVisualStyleBackColor = false;
            // 
            // dgvMineralData
            // 
            dgvMineralData.AllowUserToAddRows = false;
            dgvMineralData.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(55, 55, 55);
            dataGridViewCellStyle1.Font = new Font("微软雅黑", 9F);
            dataGridViewCellStyle1.ForeColor = Color.White;
            dgvMineralData.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            dgvMineralData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvMineralData.BackgroundColor = Color.FromArgb(45, 45, 45);
            dgvMineralData.BorderStyle = BorderStyle.None;
            dataGridViewCellStyle2.BackColor = Color.FromArgb(60, 60, 60);
            dataGridViewCellStyle2.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dgvMineralData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            dgvMineralData.ColumnHeadersHeight = 34;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = Color.FromArgb(50, 50, 50);
            dataGridViewCellStyle3.Font = new Font("微软雅黑", 9F);
            dataGridViewCellStyle3.ForeColor = Color.White;
            dataGridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.False;
            dgvMineralData.DefaultCellStyle = dataGridViewCellStyle3;
            dgvMineralData.EnableHeadersVisualStyles = false;
            dgvMineralData.GridColor = Color.FromArgb(60, 60, 60);
            dgvMineralData.Location = new Point(10, 62);
            dgvMineralData.Name = "dgvMineralData";
            dgvMineralData.ReadOnly = true;
            dgvMineralData.RowHeadersVisible = false;
            dgvMineralData.RowHeadersWidth = 62;
            dgvMineralData.Size = new Size(539, 516);
            dgvMineralData.TabIndex = 3;
            // 
            // pnlChart
            // 
            pnlChart.BackColor = Color.FromArgb(45, 45, 45);
            pnlChart.BorderStyle = BorderStyle.FixedSingle;
            pnlChart.Controls.Add(lblChartTitle);
            pnlChart.Controls.Add(btnGenerateCurve);
            pnlChart.Controls.Add(btnDeletePoint);
            pnlChart.Controls.Add(btnReset);
            pnlChart.Controls.Add(btnSaveCurve);
            pnlChart.Location = new Point(603, 297);
            pnlChart.Name = "pnlChart";
            pnlChart.Size = new Size(818, 620);
            pnlChart.TabIndex = 6;
            pnlChart.Paint += pnlChart_Paint;
            // 
            // lblChartTitle
            // 
            lblChartTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            lblChartTitle.ForeColor = Color.White;
            lblChartTitle.Location = new Point(10, 5);
            lblChartTitle.Name = "lblChartTitle";
            lblChartTitle.Size = new Size(165, 37);
            lblChartTitle.TabIndex = 0;
            lblChartTitle.Text = "脆性指数曲线";
            lblChartTitle.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // btnGenerateCurve
            // 
            btnGenerateCurve.BackColor = Color.FromArgb(0, 120, 212);
            btnGenerateCurve.FlatStyle = FlatStyle.Flat;
            btnGenerateCurve.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnGenerateCurve.ForeColor = Color.White;
            btnGenerateCurve.Location = new Point(242, 6);
            btnGenerateCurve.Name = "btnGenerateCurve";
            btnGenerateCurve.Size = new Size(110, 36);
            btnGenerateCurve.TabIndex = 1;
            btnGenerateCurve.Text = "生成曲线";
            btnGenerateCurve.UseVisualStyleBackColor = false;
            // 
            // btnDeletePoint
            // 
            btnDeletePoint.BackColor = Color.FromArgb(0, 120, 212);
            btnDeletePoint.FlatStyle = FlatStyle.Flat;
            btnDeletePoint.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnDeletePoint.ForeColor = Color.White;
            btnDeletePoint.Location = new Point(389, 6);
            btnDeletePoint.Name = "btnDeletePoint";
            btnDeletePoint.Size = new Size(110, 36);
            btnDeletePoint.TabIndex = 2;
            btnDeletePoint.Text = "删除点";
            btnDeletePoint.UseVisualStyleBackColor = false;
            // 
            // btnReset
            // 
            btnReset.BackColor = Color.FromArgb(0, 120, 212);
            btnReset.FlatStyle = FlatStyle.Flat;
            btnReset.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnReset.ForeColor = Color.White;
            btnReset.Location = new Point(532, 6);
            btnReset.Name = "btnReset";
            btnReset.Size = new Size(110, 36);
            btnReset.TabIndex = 3;
            btnReset.Text = "重置视图";
            btnReset.UseVisualStyleBackColor = false;
            // 
            // btnSaveCurve
            // 
            btnSaveCurve.BackColor = Color.FromArgb(0, 120, 212);
            btnSaveCurve.FlatStyle = FlatStyle.Flat;
            btnSaveCurve.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnSaveCurve.ForeColor = Color.White;
            btnSaveCurve.Location = new Point(671, 6);
            btnSaveCurve.Name = "btnSaveCurve";
            btnSaveCurve.Size = new Size(110, 36);
            btnSaveCurve.TabIndex = 4;
            btnSaveCurve.Text = "保存图像";
            btnSaveCurve.UseVisualStyleBackColor = false;
            // 
            // btnEmergencyExit
            // 
            btnEmergencyExit.BackColor = Color.FromArgb(192, 0, 0);
            btnEmergencyExit.FlatStyle = FlatStyle.Flat;
            btnEmergencyExit.Font = new Font("微软雅黑", 9F, FontStyle.Bold);
            btnEmergencyExit.ForeColor = Color.White;
            btnEmergencyExit.Location = new Point(1275, 105);
            btnEmergencyExit.Name = "btnEmergencyExit";
            btnEmergencyExit.Size = new Size(134, 37);
            btnEmergencyExit.TabIndex = 7;
            btnEmergencyExit.Text = "存为对比图";
            btnEmergencyExit.UseVisualStyleBackColor = false;
            // 
            // lblWelcome
            // 
            lblWelcome.Font = new Font("微软雅黑", 12F);
            lblWelcome.ForeColor = Color.White;
            lblWelcome.Location = new Point(20, 60);
            lblWelcome.Name = "lblWelcome";
            lblWelcome.Size = new Size(300, 30);
            lblWelcome.TabIndex = 8;
            lblWelcome.Text = "欢迎使用矿物脆性指数分析系统";
            lblWelcome.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // btnAlgFormulaCal
            // 
            btnAlgFormulaCal.BackColor = Color.FromArgb(255, 128, 0);
            btnAlgFormulaCal.FlatStyle = FlatStyle.Flat;
            btnAlgFormulaCal.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnAlgFormulaCal.ForeColor = Color.White;
            btnAlgFormulaCal.Location = new Point(503, 100);
            btnAlgFormulaCal.Name = "btnAlgFormulaCal";
            btnAlgFormulaCal.Size = new Size(150, 40);
            btnAlgFormulaCal.TabIndex = 9;
            btnAlgFormulaCal.Text = "脆性指数计算器";
            btnAlgFormulaCal.UseVisualStyleBackColor = false;
            btnAlgFormulaCal.Click += BtnAlgFormulaCal_Click;
            // 
            // btnViewComparison
            // 
            btnViewComparison.BackColor = Color.FromArgb(0, 120, 212);
            btnViewComparison.FlatStyle = FlatStyle.Flat;
            btnViewComparison.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnViewComparison.ForeColor = Color.White;
            btnViewComparison.Location = new Point(899, 100);
            btnViewComparison.Name = "btnViewComparison";
            btnViewComparison.Size = new Size(150, 40);
            btnViewComparison.TabIndex = 10;
            btnViewComparison.Text = "查看对比图";
            btnViewComparison.UseVisualStyleBackColor = false;
            btnViewComparison.Click += BtnViewComparison_Click;
            // 
            // MineralogicalForm
            // 
            AutoScaleMode = AutoScaleMode.Inherit;
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(1439, 929);
            Controls.Add(btnEmergencyExit);
            Controls.Add(pnlChart);
            Controls.Add(pnlData);
            Controls.Add(pnlInput);
            Controls.Add(btnLogout);
            Controls.Add(btnBack);
            Controls.Add(btnViewComparison);
            Controls.Add(btnAlgFormulaCal);
            Controls.Add(lblWelcome);
            Controls.Add(lblTitle);
            MinimumSize = new Size(800, 600);
            Name = "MineralogicalForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "矿物脆性指数分析系统 (专业版)";
            Resize += MineralogicalForm_Resize;
            pnlInput.ResumeLayout(false);
            pnlInput.PerformLayout();
            pnlData.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvMineralData).EndInit();
            pnlChart.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private Label lblInputTitle;
        private Label lblQuartz;
        private Label lblFeldspar;
        private Label lblCarbonate;
        private Label lblClay;
        private Label lblDataTitle;
        private Label lblChartTitle;
        private Label lblResult;
        private Label lblWelcome;
        private Label lblSearchColumn;
        private ComboBox cboSearchColumn;
        private Label lblMinValue;
        private TextBox txtMinValue;
        private Label lblMaxValue;
        private TextBox txtMaxValue;
        private Label lblSearchTitle;
        private Panel pnlDivider; // 分割线
    }
}