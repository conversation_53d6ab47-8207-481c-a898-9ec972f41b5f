#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文件识别模块
功能：
1. 从PDF文件中提取文本
2. 识别文献引用
3. 支持用户选择识别区域
4. 处理多种PDF格式
"""

import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from loguru import logger

try:
    import PyPDF2
    import pdfplumber
    from PIL import Image
    import pytesseract
except ImportError as e:
    logger.error(f"PDF处理模块导入失败: {e}")
    raise

from config.settings import PDF_CONFIG, OCR_CONFIG, CITATION_PATTERNS


class PDFReader:
    """PDF文件读取和处理类"""
    
    def __init__(self):
        self.config = PDF_CONFIG
        self.ocr_config = OCR_CONFIG
        self.citation_patterns = CITATION_PATTERNS
        
        # 设置Tesseract路径
        if self.ocr_config.get('tesseract_cmd'):
            pytesseract.pytesseract.tesseract_cmd = self.ocr_config['tesseract_cmd']
    
    def extract_text_pypdf2(self, pdf_path: str, page_range: Optional[Tuple[int, int]] = None) -> str:
        """使用PyPDF2提取PDF文本"""
        try:
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                # 确定页面范围
                start_page = page_range[0] if page_range else 0
                end_page = min(page_range[1] if page_range else total_pages, 
                              total_pages, self.config['max_pages'])
                
                logger.info(f"提取PDF文本，页面范围: {start_page}-{end_page}")
                
                for page_num in range(start_page, end_page):
                    try:
                        page = pdf_reader.pages[page_num]
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- 第{page_num + 1}页 ---\n{page_text}\n"
                    except Exception as e:
                        logger.warning(f"提取第{page_num + 1}页失败: {e}")
                        continue
                        
            return text
            
        except Exception as e:
            logger.error(f"PyPDF2提取文本失败: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: str, page_range: Optional[Tuple[int, int]] = None) -> str:
        """使用pdfplumber提取PDF文本（更准确）"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                
                # 确定页面范围
                start_page = page_range[0] if page_range else 0
                end_page = min(page_range[1] if page_range else total_pages, 
                              total_pages, self.config['max_pages'])
                
                logger.info(f"使用pdfplumber提取PDF文本，页面范围: {start_page}-{end_page}")
                
                for page_num in range(start_page, end_page):
                    try:
                        page = pdf.pages[page_num]
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- 第{page_num + 1}页 ---\n{page_text}\n"
                    except Exception as e:
                        logger.warning(f"提取第{page_num + 1}页失败: {e}")
                        continue
                        
            return text
            
        except Exception as e:
            logger.error(f"pdfplumber提取文本失败: {e}")
            return ""
    
    def extract_text_ocr(self, pdf_path: str, page_range: Optional[Tuple[int, int]] = None) -> str:
        """使用OCR从PDF图像中提取文本"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                
                # 确定页面范围
                start_page = page_range[0] if page_range else 0
                end_page = min(page_range[1] if page_range else total_pages, 
                              total_pages, self.config['max_pages'])
                
                logger.info(f"使用OCR提取PDF文本，页面范围: {start_page}-{end_page}")
                
                for page_num in range(start_page, end_page):
                    try:
                        page = pdf.pages[page_num]
                        # 将页面转换为图像
                        img = page.to_image(resolution=300)
                        pil_img = img.original
                        
                        # 使用OCR识别文本
                        ocr_text = pytesseract.image_to_string(
                            pil_img, 
                            lang=self.ocr_config['language'],
                            config=self.ocr_config['config']
                        )
                        
                        if ocr_text.strip():
                            text += f"\n--- 第{page_num + 1}页 (OCR) ---\n{ocr_text}\n"
                            
                    except Exception as e:
                        logger.warning(f"OCR处理第{page_num + 1}页失败: {e}")
                        continue
                        
            return text
            
        except Exception as e:
            logger.error(f"OCR提取文本失败: {e}")
            return ""
    
    def extract_text(self, pdf_path: str, page_range: Optional[Tuple[int, int]] = None, 
                    method: str = 'auto') -> str:
        """提取PDF文本的主方法"""
        if not Path(pdf_path).exists():
            logger.error(f"PDF文件不存在: {pdf_path}")
            return ""
        
        logger.info(f"开始提取PDF文本: {pdf_path}")
        
        text = ""
        
        if method == 'auto' or method == 'pdfplumber':
            # 首先尝试pdfplumber
            text = self.extract_text_pdfplumber(pdf_path, page_range)
            
        if not text and (method == 'auto' or method == 'pypdf2'):
            # 如果pdfplumber失败，尝试PyPDF2
            logger.info("尝试使用PyPDF2提取文本")
            text = self.extract_text_pypdf2(pdf_path, page_range)
            
        if not text and (method == 'auto' or method == 'ocr'):
            # 如果都失败，使用OCR
            logger.info("尝试使用OCR提取文本")
            text = self.extract_text_ocr(pdf_path, page_range)
        
        if text:
            logger.info(f"成功提取文本，长度: {len(text)} 字符")
        else:
            logger.warning("未能提取到任何文本")
            
        return text
    
    def find_citations(self, text: str) -> List[Dict[str, str]]:
        """从文本中识别文献引用 - 优先使用References部分"""
        citations = []

        # 首先尝试找到References部分
        references_text = self.extract_references_section(text)
        if references_text:
            logger.info("找到References部分，专门分析该部分")
            citations = self.parse_references_section(references_text)

            # 如果References部分识别到了足够的引用，就不再查找全文
            if len(citations) > 10:  # 如果找到超过10个引用，认为References部分有效
                logger.info(f"References部分识别到 {len(citations)} 个引用，跳过全文搜索")
                return citations

        # 如果References部分没有找到足够的引用，才在全文中查找
        logger.info("References部分引用不足，在全文中查找其他引用")

        for pattern_name, pattern in self.citation_patterns.items():
            try:
                matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)

                for match in matches:
                    citation = {
                        'pattern_type': pattern_name,
                        'full_match': match.group(0),
                        'groups': match.groups(),
                        'start_pos': match.start(),
                        'end_pos': match.end()
                    }

                    # 根据不同的引用格式解析字段
                    if pattern_name == 'apa_style':
                        citation.update({
                            'authors': match.group(1).strip(),
                            'year': match.group(2).strip(),
                            'title': match.group(3).strip(),
                            'journal': match.group(4).strip()
                        })
                    elif pattern_name == 'doi_pattern':
                        citation.update({
                            'doi': match.group(1).strip()
                        })
                    elif pattern_name == 'isbn_pattern':
                        citation.update({
                            'isbn': match.group(1).strip()
                        })

                    citations.append(citation)

            except Exception as e:
                logger.warning(f"模式匹配失败 {pattern_name}: {e}")
                continue

        # 去重和排序
        unique_citations = []
        seen_citations = set()

        for citation in citations:
            citation_key = citation['full_match'].strip().lower()
            if citation_key not in seen_citations:
                seen_citations.add(citation_key)
                unique_citations.append(citation)

        # 按在文本中的位置排序
        unique_citations.sort(key=lambda x: x.get('start_pos', 0))

        logger.info(f"最终识别到 {len(unique_citations)} 个文献引用")
        return unique_citations

    def extract_references_section(self, text: str) -> str:
        """提取References部分的文本 - 专注于文档最后部分"""
        # 只在文档的最后30%部分查找References
        text_length = len(text)
        search_start = int(text_length * 0.7)  # 从70%位置开始查找
        search_text = text[search_start:]

        logger.info(f"在文档最后30%部分查找References (从位置 {search_start} 开始)")

        # 查找References部分的开始
        ref_patterns = [
            r'\bReferences\b',
            r'\bREFERENCES\b',
            r'\bBibliography\b',
            r'\b参考文献\b'
        ]

        references_start = -1
        for pattern in ref_patterns:
            match = re.search(pattern, search_text, re.IGNORECASE)
            if match:
                references_start = search_start + match.end()
                logger.info(f"找到References标题，绝对位置: {references_start}")
                break

        if references_start == -1:
            # 尝试查找第一个编号引用 [1]
            first_ref_patterns = [
                r'\[1\]\s*[A-Z]',  # [1] 后跟大写字母
                r'\[1\]\s*\w',     # [1] 后跟任何字母
            ]

            for pattern in first_ref_patterns:
                first_ref_match = re.search(pattern, search_text)
                if first_ref_match:
                    references_start = search_start + first_ref_match.start()
                    logger.info(f"通过[1]找到References部分，绝对位置: {references_start}")
                    break

            if references_start == -1:
                logger.warning("在文档最后30%部分未找到References")
                return ""

        # 从References开始到文档结束
        # 通常References就在文档最后，所以直接到文档结束
        references_text = text[references_start:]

        # 可选：移除可能的附录等内容
        end_patterns = [
            r'\n\s*Appendix\s*\n',
            r'\n\s*Acknowledgments?\s*\n',
            r'\n\s*Supplementary\s*\n',
            r'\n\s*附录\s*\n',
            r'\n\s*致谢\s*\n'
        ]

        for end_pattern in end_patterns:
            end_match = re.search(end_pattern, references_text, re.IGNORECASE)
            if end_match:
                references_text = references_text[:end_match.start()]
                logger.info(f"在References中找到结束标记，截断到该位置")
                break

        logger.info(f"提取的References部分长度: {len(references_text)} 字符")

        return references_text

    def parse_references_section(self, references_text: str) -> List[Dict[str, str]]:
        """解析References部分的引用"""
        citations = []

        # 专门处理 [数字] 格式的引用
        # 更精确的正则表达式，确保完整匹配每个引用
        numbered_pattern = r'\[(\d+)\]\s*(.*?)(?=\n\s*\[\d+\]|\n\s*$|$)'
        numbered_matches = list(re.finditer(numbered_pattern, references_text, re.DOTALL))

        logger.info(f"在References部分找到 {len(numbered_matches)} 个编号引用")

        for match in numbered_matches:
            ref_number = match.group(1)
            ref_content = match.group(2).strip()

            # 清理引用内容，移除多余的换行和空格
            ref_content = re.sub(r'\s+', ' ', ref_content)

            # 只处理有实际内容的引用
            if len(ref_content) > 20:  # 至少20个字符
                citation = self.parse_single_reference(ref_content)
                if citation:
                    citation['reference_number'] = ref_number
                    citation['pattern_type'] = 'numbered_reference'
                    citations.append(citation)
                    logger.debug(f"成功解析引用 [{ref_number}]: {ref_content[:50]}...")

        logger.info(f"成功解析 {len(citations)} 个有效引用")
        return citations

    def parse_single_reference(self, ref_text: str) -> Dict[str, str]:
        """解析单个引用 - 专门针对学术论文格式"""
        if not ref_text or len(ref_text.strip()) < 20:
            return None

        citation = {
            'pattern_type': 'reference_line',
            'full_match': ref_text.strip(),
            'start_pos': 0,
            'end_pos': len(ref_text),
            'authors': '',
            'title': '',
            'journal': '',
            'year': '',
            'doi': '',
            'url': ''
        }

        # 清理文本
        clean_text = ref_text.strip()

        # 1. 提取年份 - 按优先级顺序
        year_patterns = [
            r'\((\d{4})\)',  # (2021) - 最常见
            r'\b(\d{4})\b',  # 独立的年份
        ]

        for pattern in year_patterns:
            year_matches = re.findall(pattern, clean_text)
            for year in year_matches:
                if 1900 <= int(year) <= 2030:
                    citation['year'] = year
                    break
            if citation['year']:
                break

        # 2. 提取DOI
        doi_patterns = [
            r'https://doi\.org/(10\.\d+/[^\s,]+)',
            r'doi:\s*(10\.\d+/[^\s,]+)',
            r'DOI:\s*(10\.\d+/[^\s,]+)',
        ]

        for pattern in doi_patterns:
            doi_match = re.search(pattern, clean_text, re.IGNORECASE)
            if doi_match:
                citation['doi'] = doi_match.group(1)
                break

        # 3. 提取URL
        url_match = re.search(r'https?://[^\s,]+', clean_text)
        if url_match:
            citation['url'] = url_match.group(0)

        # 4. 解析作者 - 针对格式 "J. Devlin, M.W. Chang, K. Lee"
        # 作者通常在开头，直到第一个逗号后的标题
        author_patterns = [
            # 格式: J. Devlin, M.W. Chang, K. Lee, K. Toutanova,
            r'^([A-Z]\.\s*[A-Z][a-zA-Z\-\']*(?:,\s*[A-Z]\.\s*[A-Z][a-zA-Z\-\']*)*),\s*([^,]+),',
            # 格式: A. Smith, B. Jones,
            r'^([A-Z][a-zA-Z\-\']*(?:,\s*[A-Z][a-zA-Z\-\']*)*),\s*([^,]+),',
            # 更宽松的格式
            r'^([^,]+),\s*([^,]+),',
        ]

        for pattern in author_patterns:
            author_match = re.search(pattern, clean_text)
            if author_match:
                potential_authors = author_match.group(1).strip()
                potential_title = author_match.group(2).strip()

                # 验证作者格式 - 应该包含大写字母和可能的缩写
                if (re.search(r'[A-Z]', potential_authors) and
                    len(potential_authors) < 150 and
                    not re.search(r'\d{4}', potential_authors)):  # 不应该包含年份

                    citation['authors'] = potential_authors

                    # 如果第二部分看起来像标题，也提取它
                    if (len(potential_title) > 10 and
                        not re.match(r'^\d+$', potential_title) and
                        not re.search(r'^\d{4}$', potential_title)):
                        citation['title'] = potential_title
                    break

        # 5. 如果没有找到标题，尝试其他方法
        if not citation['title']:
            # 查找可能的标题模式
            title_patterns = [
                # 在作者后面，年份前面
                r',\s*([^,]+),\s*\d{4}',
                # 在引号中
                r'"([^"]+)"',
                # 在作者后面，期刊前面
                r',\s*([^,]+),\s*[A-Z][^,]*\s+\d+',
            ]

            for pattern in title_patterns:
                title_match = re.search(pattern, clean_text)
                if title_match:
                    title = title_match.group(1).strip()
                    if (len(title) > 10 and len(title) < 200 and
                        not re.match(r'^\d+$', title) and
                        not re.search(r'^\d{4}$', title)):
                        citation['title'] = title
                        break

        # 6. 提取期刊信息
        journal_patterns = [
            # 格式: Tour. Manag. 59 (2017)
            r',\s*([A-Z][a-zA-Z\s\.]+)\s+\d+\s*\(\d{4}\)',
            # 格式: Journal Name. 59
            r',\s*([A-Z][a-zA-Z\s\.]+)\.\s*\d+',
            # 格式: Journal Name, vol
            r',\s*([A-Z][a-zA-Z\s\.]+),\s*\d+',
        ]

        for pattern in journal_patterns:
            journal_match = re.search(pattern, clean_text)
            if journal_match:
                journal = journal_match.group(1).strip()
                # 验证期刊名 - 移除末尾的点
                journal = re.sub(r'\.$', '', journal)
                if (len(journal) > 3 and len(journal) < 100 and
                    not re.match(r'^\d+$', journal)):
                    citation['journal'] = journal
                    break

        # 7. 如果仍然没有作者，尝试更宽松的匹配
        if not citation['authors']:
            # 取第一个逗号前的内容作为作者
            first_part = clean_text.split(',')[0].strip()
            if (len(first_part) > 3 and len(first_part) < 100 and
                re.search(r'[A-Z]', first_part)):
                citation['authors'] = first_part

        return citation
    
    def extract_citations_from_pdf(self, pdf_path: str, page_range: Optional[Tuple[int, int]] = None,
                                  method: str = 'auto') -> List[Dict[str, str]]:
        """从PDF文件中提取文献引用的主方法"""
        try:
            # 提取文本
            text = self.extract_text(pdf_path, page_range, method)
            
            if not text:
                return []
            
            # 识别引用
            citations = self.find_citations(text)
            
            # 添加源文件信息
            for citation in citations:
                citation['source_file'] = pdf_path
                citation['extraction_method'] = method
            
            return citations
            
        except Exception as e:
            logger.error(f"从PDF提取引用失败: {e}")
            return []
    
    def get_pdf_info(self, pdf_path: str) -> Dict[str, any]:
        """获取PDF文件信息"""
        try:
            info = {
                'file_path': pdf_path,
                'file_size': Path(pdf_path).stat().st_size,
                'total_pages': 0,
                'title': '',
                'author': '',
                'subject': '',
                'creator': '',
                'creation_date': '',
            }
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                info['total_pages'] = len(pdf_reader.pages)
                
                # 获取元数据
                if pdf_reader.metadata:
                    metadata = pdf_reader.metadata
                    info['title'] = metadata.get('/Title', '') or ''
                    info['author'] = metadata.get('/Author', '') or ''
                    info['subject'] = metadata.get('/Subject', '') or ''
                    info['creator'] = metadata.get('/Creator', '') or ''
                    info['creation_date'] = str(metadata.get('/CreationDate', '')) or ''
            
            return info
            
        except Exception as e:
            logger.error(f"获取PDF信息失败: {e}")
            return {'file_path': pdf_path, 'error': str(e)}
