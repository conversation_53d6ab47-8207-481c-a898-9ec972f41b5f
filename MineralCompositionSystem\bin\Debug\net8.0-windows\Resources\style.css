/* 全局样式变量 */
:root {
    --primary-color: #2a5298;
    --secondary-color: #1e3c72;
    --accent-color: #45f3ff;
    --text-color: #333;
    --light-text: #666;
    --border-color: #e1e1e1;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --shadow-color: rgba(0, 0, 0, 0.15);
    --gradient-primary: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* 布局容器 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    animation: fadeIn 0.5s ease;
}

/* 头部样式 */
header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 10px var(--shadow-color);
}

header h1 {
    font-size: 1.8rem;
    margin: 0;
}

/* 导航栏 */
nav {
    background: white;
    padding: 0.5rem;
    box-shadow: 0 2px 5px var(--shadow-color);
}

nav ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

nav a:hover,
nav a.active {
    background: var(--primary-color);
    color: white;
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px var(--shadow-color);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(42, 82, 152, 0.4);
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.2);
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1rem;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
}

.table tr:hover {
    background-color: rgba(42, 82, 152, 0.05);
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in { animation: fadeIn 0.5s ease forwards; }
.slide-in { animation: slideIn 0.5s ease forwards; }

/* 响应式设计 */
@media (max-width: 768px) {
    .container { padding: 0.5rem; }
    
    nav ul {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table {
        display: block;
        overflow-x: auto;
    }
}

/* 状态样式 */
.status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status.danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }