#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查和安装脚本
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"✗ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("检查和安装依赖包...")
    print("=" * 50)
    
    # 必需的包列表
    required_packages = [
        ("loguru", "loguru"),
        ("selenium", "selenium"),
        ("beautifulsoup4", "bs4"),
        ("requests", "requests"),
        ("pdfplumber", "pdfplumber"),
        ("pytesseract", "pytesseract"),
        ("pillow", "PIL"),
        ("webdriver-manager", "webdriver_manager"),
    ]
    
    failed_packages = []
    
    for package_name, import_name in required_packages:
        if not check_and_install_package(package_name, import_name):
            failed_packages.append(package_name)
    
    print("\n" + "=" * 50)
    
    if failed_packages:
        print(f"✗ 以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("✓ 所有依赖包已安装完成")
    
    # 检查Chrome浏览器
    print("\n检查Chrome浏览器...")
    try:
        import os
        import shutil

        # Windows常见的Chrome安装路径
        windows_chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
        ]

        chrome_found = False

        # 首先检查PATH
        chrome_path = shutil.which("chrome") or shutil.which("google-chrome") or shutil.which("chromium")
        if chrome_path:
            print(f"✓ 在PATH中找到Chrome浏览器: {chrome_path}")
            chrome_found = True

        # 检查Windows常见路径
        if not chrome_found:
            for path in windows_chrome_paths:
                if os.path.exists(path):
                    print(f"✓ 找到Chrome浏览器: {path}")
                    chrome_found = True
                    break

        if not chrome_found:
            print("✗ 未找到Chrome浏览器")
            print("请从 https://www.google.com/chrome/ 下载并安装Chrome浏览器")

    except Exception as e:
        print(f"检查Chrome浏览器时出错: {e}")
    
    # 测试WebDriver
    print("\n测试WebDriver...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式测试
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.quit()
            print("✓ WebDriver测试成功（使用webdriver-manager）")
            
        except ImportError:
            driver = webdriver.Chrome(options=chrome_options)
            driver.quit()
            print("✓ WebDriver测试成功（使用系统ChromeDriver）")
            
    except Exception as e:
        print(f"✗ WebDriver测试失败: {e}")
        print("请确保Chrome浏览器已正确安装")
    
    print("\n" + "=" * 50)
    print("依赖检查完成")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
