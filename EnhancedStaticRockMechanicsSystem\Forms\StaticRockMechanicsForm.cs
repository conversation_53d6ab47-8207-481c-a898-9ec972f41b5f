using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Newtonsoft.Json;
using EnhancedStaticRockMechanicsSystem.Models;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    /// <summary>
    /// 增强版静态岩石力学参数法脆性指数计算窗体
    /// </summary>
    public partial class StaticRockMechanicsForm : Form
    {
        #region 字段和属性

        private readonly string username = "";
        private DataTable mechanicsData = new DataTable();
        private DataTable originalMechanicsData;
        private string currentExcelFile;

        // 图表缩放相关字段
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private const double MAX_ZOOM = 15.0;
        private const double MAX_X_ZOOM = 3.0;
        private const double MIN_ZOOM = 1.0;
        private const double ZOOM_FACTOR = 1.2;

        // 数据点和交互相关字段
        private List<BrittlenessDataPoint> dataPoints = new List<BrittlenessDataPoint>();
        private HashSet<int> selectedRows = new HashSet<int>();
        private bool showDataPoints = false;

        // 增强功能服务
        private UnifiedComparisonDataManager comparisonDataManager;
        private ImageAssociationService imageService;

        // 列名识别字典
        private readonly Dictionary<string, List<string>> columnPatterns = new Dictionary<string, List<string>>
        {
            ["深度"] = new List<string> { "深度", "depth", "depths", "顶深", "井深", "md", "tvd", "顶深/m", "深度/m", "深度(m)" },
            ["密度"] = new List<string> { "密度", "ρ", "rho", "rhob", "density", "den", "密度/(g/cm³)", "密度(g/cm³)", "岩石密度" },
            ["纵波速度"] = new List<string> { "纵波速度", "vp", "纵波", "p波", "dt", "纵波时差", "纵波速度/(m/s)", "纵波速度(m/s)", "vp(m/s)" },
            ["横波速度"] = new List<string> { "横波速度", "vs", "横波", "s波", "dts", "横波时差", "横波速度/(m/s)", "横波速度(m/s)", "vs(m/s)" }
        };

        #endregion

        #region 构造函数

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public StaticRockMechanicsForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎使用增强版静态岩石力学参数法, {username}";
            }
        }

        #endregion

        #region 初始化方法

        private void InitializeForm()
        {
            try
            {
                // 初始化配置
                AppConfig.Initialize();

                // 初始化服务
                comparisonDataManager = new UnifiedComparisonDataManager();
                imageService = new ImageAssociationService();

                // 初始化数据表
                InitializeDataTable();

                // 绑定事件
                Load += StaticRockMechanicsForm_Load;
                Resize += StaticRockMechanicsForm_Resize;
                FormClosing += StaticRockMechanicsForm_FormClosing;

                // 绑定单位选择事件
                BindUnitSelectionEvents();

                // 初始化增强功能
                InitializeEnhancedFeatures();

                LoggingService.Instance.Info("增强版静态岩石力学参数法窗体初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"窗体初始化失败: {ex.Message}");
                MessageBox.Show($"窗体初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("顶深/m", typeof(double));
            mechanicsData.Columns.Add("底深/m", typeof(double));
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数", typeof(double));
        }

        private void InitializeEnhancedFeatures()
        {
            try
            {
                // 添加增强功能按钮到界面
                AddEnhancedButtons();

                LoggingService.Instance.Info("增强功能初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"增强功能初始化失败: {ex.Message}");
            }
        }

        private void AddEnhancedButtons()
        {
            // 创建增强功能面板
            var enhancedPanel = new Panel
            {
                Name = "pnlEnhanced",
                Size = new Size(200, 150),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(45, 45, 45),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 批量导入按钮
            var btnBatchImport = new Button
            {
                Text = "批量导入数据",
                Size = new Size(180, 30),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnBatchImport.Click += BtnBatchImport_Click;

            // 查看对比图按钮
            var btnViewComparison = new Button
            {
                Text = "查看对比图",
                Size = new Size(180, 30),
                Location = new Point(10, 50),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnViewComparison.Click += BtnViewComparison_Click;

            // 保存对比数据按钮
            var btnSaveComparison = new Button
            {
                Text = "存为对比图",
                Size = new Size(180, 30),
                Location = new Point(10, 90),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSaveComparison.Click += BtnSaveComparison_Click;

            enhancedPanel.Controls.AddRange(new Control[] {
                btnBatchImport, btnViewComparison, btnSaveComparison
            });

            // 添加到主窗体
            this.Controls.Add(enhancedPanel);
            enhancedPanel.BringToFront();
        }

        #endregion

        #region 增强功能事件处理

        /// <summary>
        /// 批量导入数据按钮点击事件
        /// </summary>
        private async void BtnBatchImport_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始批量导入数据");

                // 显示批量导入向导
                var dataSets = await comparisonDataManager.ShowBatchImportWizard();

                if (dataSets.Count > 0)
                {
                    // 显示增强的对比图窗体
                    var enhancedForm = new EnhancedComparisonChartForm();
                    enhancedForm.LoadComparisonData(dataSets);
                    enhancedForm.ShowDialog();

                    LoggingService.Instance.Info($"批量导入完成，共导入 {dataSets.Count} 个数据集");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"批量导入失败: {ex.Message}");
                MessageBox.Show($"批量导入失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 查看对比图按钮点击事件
        /// </summary>
        private async void BtnViewComparison_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始查看对比图");

                // 从标准位置加载对比数据
                var dataSets = await comparisonDataManager.LoadFromStandardLocations();

                if (dataSets.Count > 0)
                {
                    // 显示增强的对比图窗体
                    var enhancedForm = new EnhancedComparisonChartForm();
                    enhancedForm.LoadComparisonData(dataSets);
                    enhancedForm.ShowDialog();

                    LoggingService.Instance.Info($"对比图显示完成，共显示 {dataSets.Count} 个数据集");
                }
                else
                {
                    MessageBox.Show("没有找到对比数据！请先在各个系统中保存图表数据，或使用批量导入功能。",
                        "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"查看对比图失败: {ex.Message}");
                MessageBox.Show($"查看对比图失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 存为对比图按钮点击事件
        /// </summary>
        private async void BtnSaveComparison_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有数据可以保存
                if (dataPoints.Count == 0)
                {
                    MessageBox.Show("没有计算结果可以保存为对比图！请先进行脆性指数计算。",
                        "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                LoggingService.Instance.Info("开始保存对比图数据");

                // 创建对比数据集
                var dataSet = new ComparisonDataSet
                {
                    SystemName = "增强版静态岩石力学参数法",
                    DataSource = currentExcelFile ?? "手动输入",
                    ImportTime = DateTime.Now,
                    DataPoints = dataPoints.ToList()
                };

                // 保存到标准位置
                var savedPath = await comparisonDataManager.SaveDataSetToStandardLocation(dataSet, "json");

                // 同时保存到传统位置以保持兼容性
                await SaveToLegacyLocation(dataSet);

                MessageBox.Show($"对比图数据已保存！\n保存位置: {savedPath}",
                    "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoggingService.Instance.Info($"对比图数据保存完成: {savedPath}");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"保存对比图数据失败: {ex.Message}");
                MessageBox.Show($"保存对比图数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存到传统位置以保持兼容性
        /// </summary>
        private async System.Threading.Tasks.Task SaveToLegacyLocation(ComparisonDataSet dataSet)
        {
            try
            {
                var chartDataPoints = dataSet.DataPoints.Select(dp => new
                {
                    TopDepth = dp.TopDepth,
                    BottomDepth = dp.BottomDepth,
                    BrittleIndex = dp.BrittleIndex
                }).ToList();

                var comparisonData = new
                {
                    SystemName = "增强版静态岩石力学参数法",
                    DataPoints = chartDataPoints,
                    SaveTime = DateTime.Now,
                    DataCount = chartDataPoints.Count,
                    Version = "3.0",
                    Description = "增强版静态岩石力学参数法对比数据"
                };

                string jsonData = JsonConvert.SerializeObject(comparisonData, Formatting.Indented);
                string tempPath = Path.Combine(AppConfig.TempDataPath, "BritSystem_StaticRockMechanicsData.json");
                await File.WriteAllTextAsync(tempPath, jsonData);

                LoggingService.Instance.Info($"兼容性数据已保存到: {tempPath}");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"保存兼容性数据失败: {ex.Message}");
            }
        }

        #endregion

        #region 基础事件处理

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("静态岩石力学参数法窗体加载完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"窗体加载失败: {ex.Message}");
            }
        }

        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            // 处理窗体大小变化
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("静态岩石力学参数法窗体关闭");
            }
            catch
            {
                // 忽略日志错误
            }
        }

        private void BindUnitSelectionEvents()
        {
            // 绑定单位选择事件的实现
        }

        /// <summary>
        /// 导入数据按钮点击事件
        /// </summary>
        private void BtnImport_Click(object sender, EventArgs e)
        {
            ImportExcelData();
        }

        /// <summary>
        /// 计算脆性指数按钮点击事件
        /// </summary>
        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            if (mechanicsData.Rows.Count == 0)
            {
                MessageBox.Show("请先导入数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            CalculateBrittlenessIndex();
        }

        /// <summary>
        /// 导出结果按钮点击事件
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            if (dataPoints.Count == 0)
            {
                MessageBox.Show("没有计算结果可以导出！请先进行脆性指数计算。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            ExportResults();
        }

        /// <summary>
        /// 导出计算结果
        /// </summary>
        private void ExportResults()
        {
            try
            {
                using (var sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel文件|*.xlsx|CSV文件|*.csv";
                    sfd.DefaultExt = "xlsx";
                    sfd.FileName = $"脆性指数计算结果_{DateTime.Now:yyyyMMdd_HHmmss}";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        if (Path.GetExtension(sfd.FileName).ToLower() == ".csv")
                        {
                            ExportToCsv(sfd.FileName);
                        }
                        else
                        {
                            ExportToExcel(sfd.FileName);
                        }

                        MessageBox.Show("导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoggingService.Instance.Info($"计算结果导出成功: {sfd.FileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导出结果失败: {ex.Message}");
                MessageBox.Show($"导出结果失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCsv(string filePath)
        {
            var lines = new List<string>
            {
                "序号,顶深(m),底深(m),脆性指数(%),GeoID"
            };

            for (int i = 0; i < dataPoints.Count; i++)
            {
                var point = dataPoints[i];
                lines.Add($"{i + 1},{point.TopDepth},{point.BottomDepth},{point.BrittleIndex:F2},{point.GeoID}");
            }

            File.WriteAllLines(filePath, lines);
        }

        private void ExportToExcel(string filePath)
        {
            // 简化的Excel导出实现
            var exportData = new DataTable();
            exportData.Columns.Add("序号", typeof(int));
            exportData.Columns.Add("顶深(m)", typeof(double));
            exportData.Columns.Add("底深(m)", typeof(double));
            exportData.Columns.Add("脆性指数(%)", typeof(double));
            exportData.Columns.Add("GeoID", typeof(string));

            for (int i = 0; i < dataPoints.Count; i++)
            {
                var point = dataPoints[i];
                exportData.Rows.Add(i + 1, point.TopDepth, point.BottomDepth, point.BrittleIndex, point.GeoID);
            }

            // 这里需要实际的Excel导出实现
            // 为了简化，暂时使用CSV格式
            ExportToCsv(filePath.Replace(".xlsx", ".csv"));
        }

        #endregion

        #region 脆性指数计算相关方法

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        private void CalculateBrittlenessIndex()
        {
            try
            {
                dataPoints.Clear();

                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (IsValidDataRow(row))
                    {
                        var dataPoint = CreateDataPointFromRow(row);
                        if (dataPoint != null)
                        {
                            dataPoints.Add(dataPoint);
                        }
                    }
                }

                // 更新图表显示
                UpdateChart();

                LoggingService.Instance.Info($"脆性指数计算完成，共计算 {dataPoints.Count} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"脆性指数计算失败: {ex.Message}");
                MessageBox.Show($"脆性指数计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool IsValidDataRow(DataRow row)
        {
            try
            {
                // 检查必要的数据是否存在
                var depth = Convert.ToDouble(row["顶深/m"]);
                var density = Convert.ToDouble(row["密度/(g/cm³)"]);
                var vp = Convert.ToDouble(row["纵波速度/(m/s)"]);
                var vs = Convert.ToDouble(row["横波速度/(m/s)"]);

                return depth > 0 && density > 0 && vp > 0 && vs > 0;
            }
            catch
            {
                return false;
            }
        }

        private BrittlenessDataPoint CreateDataPointFromRow(DataRow row)
        {
            try
            {
                var topDepth = Convert.ToDouble(row["顶深/m"]);
                var bottomDepth = row["底深/m"] != DBNull.Value ? Convert.ToDouble(row["底深/m"]) : topDepth;
                var density = Convert.ToDouble(row["密度/(g/cm³)"]);
                var vp = Convert.ToDouble(row["纵波速度/(m/s)"]);
                var vs = Convert.ToDouble(row["横波速度/(m/s)"]);

                // 计算动态弹性参数
                var dynamicYoung = CalculateDynamicYoungsModulus(density, vp, vs);
                var dynamicPoisson = CalculateDynamicPoissonRatio(vp, vs);

                // 计算脆性指数
                var brittlenessIndex = CalculateBrittlenessFromMechanics(dynamicYoung, dynamicPoisson);

                var dataPoint = new BrittlenessDataPoint
                {
                    TopDepth = topDepth,
                    BottomDepth = bottomDepth,
                    BrittleIndex = brittlenessIndex
                };

                dataPoint.GenerateGeoID();
                return dataPoint;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"创建数据点失败: {ex.Message}");
                return null;
            }
        }

        private double CalculateDynamicYoungsModulus(double density, double vp, double vs)
        {
            // E = ρ * vs² * (3vp² - 4vs²) / (vp² - vs²)
            // 单位转换：密度 g/cm³ -> kg/m³，速度 m/s，结果 Pa -> GPa
            double rho = density * 1000; // g/cm³ to kg/m³
            double numerator = 3 * vp * vp - 4 * vs * vs;
            double denominator = vp * vp - vs * vs;

            if (denominator == 0) return 0;

            double E = rho * vs * vs * numerator / denominator;
            return E / 1e9; // Pa to GPa
        }

        private double CalculateDynamicPoissonRatio(double vp, double vs)
        {
            // ν = (vp² - 2vs²) / (2(vp² - vs²))
            double numerator = vp * vp - 2 * vs * vs;
            double denominator = 2 * (vp * vp - vs * vs);

            if (denominator == 0) return 0;

            return numerator / denominator;
        }

        private double CalculateBrittlenessFromMechanics(double youngModulus, double poissonRatio)
        {
            // 使用归一化方法计算脆性指数
            // BI = (E_norm + (1-ν)_norm) / 2 * 100

            // 典型值范围：杨氏模量 10-80 GPa，泊松比 0.1-0.4
            double E_norm = Math.Min(Math.Max((youngModulus - 10) / (80 - 10), 0), 1);
            double nu_norm = Math.Min(Math.Max((0.4 - poissonRatio) / (0.4 - 0.1), 0), 1);

            return (E_norm + nu_norm) / 2 * 100;
        }

        private void UpdateChart()
        {
            // 更新图表显示的实现
            // 这里需要根据实际的图表控件来实现
        }

        #endregion

        #region 数据导入导出方法

        /// <summary>
        /// 导入Excel数据
        /// </summary>
        private void ImportExcelData()
        {
            try
            {
                using (var ofd = new OpenFileDialog())
                {
                    ofd.Filter = "Excel文件|*.xlsx;*.xls";
                    ofd.Title = "选择静态岩石力学参数数据文件";

                    if (ofd.ShowDialog() == DialogResult.OK)
                    {
                        currentExcelFile = ofd.FileName;
                        var importedData = ImportExcelFile(currentExcelFile);

                        if (importedData != null && importedData.Rows.Count > 0)
                        {
                            mechanicsData = importedData;
                            originalMechanicsData = mechanicsData.Copy();

                            MessageBox.Show($"成功导入 {mechanicsData.Rows.Count} 行数据",
                                "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            LoggingService.Instance.Info($"Excel数据导入成功: {currentExcelFile}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"Excel数据导入失败: {ex.Message}");
                MessageBox.Show($"Excel数据导入失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable ImportExcelFile(string filePath)
        {
            var dataTable = new DataTable();

            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workbook = Path.GetExtension(filePath).ToLower() == ".xlsx"
                    ? new XSSFWorkbook(fileStream)
                    : new HSSFWorkbook(fileStream);

                ISheet sheet = workbook.GetSheetAt(0);

                // 读取表头
                IRow headerRow = sheet.GetRow(0);
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    dataTable.Columns.Add(headerRow.GetCell(i)?.ToString() ?? $"Column{i}");
                }

                // 读取数据行
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row != null)
                    {
                        var dataRow = dataTable.NewRow();
                        for (int j = 0; j < dataTable.Columns.Count; j++)
                        {
                            var cell = row.GetCell(j);
                            dataRow[j] = GetCellValue(cell);
                        }
                        dataTable.Rows.Add(dataRow);
                    }
                }
            }

            return dataTable;
        }

        private object GetCellValue(ICell cell)
        {
            if (cell == null) return DBNull.Value;

            switch (cell.CellType)
            {
                case CellType.Numeric:
                    return cell.NumericCellValue;
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Formula:
                    try
                    {
                        return cell.NumericCellValue;
                    }
                    catch
                    {
                        return cell.StringCellValue;
                    }
                default:
                    return DBNull.Value;
            }
        }

        #endregion
    }
}
