﻿[2025-06-22 15:31:31.408] [INFO] [1] [LoggingService.Info] 日志服务已初始化
[2025-06-22 15:31:31.419] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统启动
[2025-06-22 15:34:13.060] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-06-22 15:34:13.060] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=79, 列数=11
[2025-06-22 15:34:13.061] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: 石英%: 27.16, 碳酸盐矿物%: 24.29, 斜长石%: 22.13, 钾长石（正长石）%: 0
[2025-06-22 15:34:13.061] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: 黏土矿物总量%: 26.83
[2025-06-22 15:34:13.061] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: 石英%: 27.16, 碳酸盐矿物%: 24.29, 斜长石%: 22.13, 钾长石（正长石）%: 0
[2025-06-22 15:34:13.062] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: 黏土矿物总量%: 26.83
[2025-06-22 15:34:13.063] [INFO] [1] [LoggingService.Info] 最终脆性矿物列: 石英%, 碳酸盐矿物%, 斜长石%, 钾长石（正长石）%
[2025-06-22 15:34:13.063] [INFO] [1] [LoggingService.Info] 最终塑性矿物列: 黏土矿物总量%
[2025-06-22 15:34:13.063] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-06-22 15:34:14.898] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-06-22 15:34:14.899] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=79, 列数=11
[2025-06-22 15:34:14.899] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: 石英%: 27.16, 碳酸盐矿物%: 24.29, 斜长石%: 22.13, 钾长石（正长石）%: 0
[2025-06-22 15:34:14.900] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: 黏土矿物总量%: 26.83
[2025-06-22 15:34:14.900] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: 石英%: 27.16, 碳酸盐矿物%: 24.29, 斜长石%: 22.13, 钾长石（正长石）%: 0
[2025-06-22 15:34:14.900] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: 黏土矿物总量%: 26.83
[2025-06-22 15:34:14.900] [INFO] [1] [LoggingService.Info] 最终脆性矿物列: 石英%, 碳酸盐矿物%, 斜长石%, 钾长石（正长石）%
[2025-06-22 15:34:14.901] [INFO] [1] [LoggingService.Info] 最终塑性矿物列: 黏土矿物总量%
[2025-06-22 15:34:14.901] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-06-22 15:35:20.088] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统关闭
[2025-06-22 15:57:55.913] [INFO] [1] [LoggingService.Info] 日志服务已初始化
[2025-06-22 15:57:55.922] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统启动
[2025-06-22 16:08:47.746] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-06-22 16:08:47.747] [WARNING] [1] [LoggingService.Warning] 没有计算结果数据可以可视化
[2025-06-22 16:09:22.147] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-06-22 16:09:22.148] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=79, 列数=11
[2025-06-22 16:09:22.149] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: 石英%: 27.16, 碳酸盐矿物%: 24.29, 斜长石%: 22.13, 钾长石（正长石）%: 0
[2025-06-22 16:09:22.149] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: 黏土矿物总量%: 26.83
[2025-06-22 16:09:22.149] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: 石英%: 27.16, 碳酸盐矿物%: 24.29, 斜长石%: 22.13, 钾长石（正长石）%: 0
[2025-06-22 16:09:22.150] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: 黏土矿物总量%: 26.83
[2025-06-22 16:09:22.151] [INFO] [1] [LoggingService.Info] 最终脆性矿物列: 石英%, 碳酸盐矿物%, 斜长石%, 钾长石（正长石）%
[2025-06-22 16:09:22.151] [INFO] [1] [LoggingService.Info] 最终塑性矿物列: 黏土矿物总量%
[2025-06-22 16:09:22.152] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-06-22 16:09:22.154] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数开始 =====
[2025-06-22 16:09:22.154] [INFO] [1] [LoggingService.Info] 接收到数据: 行数=79, 脆性矿物=4, 塑性矿物=1
[2025-06-22 16:09:22.155] [INFO] [1] [LoggingService.Info] 脆性矿物列表: 石英%, 碳酸盐矿物%, 斜长石%, 钾长石（正长石）%
[2025-06-22 16:09:22.155] [INFO] [1] [LoggingService.Info] 塑性矿物列表: 黏土矿物总量%
[2025-06-22 16:09:22.155] [INFO] [1] [LoggingService.Info] 调用InitializeComponent...
[2025-06-22 16:09:22.184] [INFO] [1] [LoggingService.Info] InitializeComponent完成
[2025-06-22 16:09:22.185] [INFO] [1] [LoggingService.Info] 调用InitializeDepthData...
[2025-06-22 16:09:22.191] [INFO] [1] [LoggingService.Info] InitializeDepthData完成
[2025-06-22 16:09:22.192] [INFO] [1] [LoggingService.Info] 调用InitializeControls...
[2025-06-22 16:09:22.193] [INFO] [1] [LoggingService.Info] ===== 初始化饼状图控件 =====
[2025-06-22 16:09:22.193] [INFO] [1] [LoggingService.Info] 深度滑动条设置: 范围0-78
[2025-06-22 16:09:22.193] [INFO] [1] [LoggingService.Info] 开始初始化chartPie控件
[2025-06-22 16:09:22.194] [INFO] [1] [LoggingService.Info] ChartArea已创建
[2025-06-22 16:09:22.195] [INFO] [1] [LoggingService.Info] Legend已创建
[2025-06-22 16:09:22.195] [INFO] [1] [LoggingService.Info] Title已创建
[2025-06-22 16:09:22.196] [INFO] [1] [LoggingService.Info] chartPie控件初始化完成
[2025-06-22 16:09:22.196] [INFO] [1] [LoggingService.Info] ===== 饼状图控件初始化完成 =====
[2025-06-22 16:09:22.200] [INFO] [1] [LoggingService.Info] ===== 初始化坐标轴控制面板 =====
[2025-06-22 16:09:22.201] [INFO] [1] [LoggingService.Info] X轴默认值设置完成
[2025-06-22 16:09:22.201] [INFO] [1] [LoggingService.Info] 坐标轴控制面板已初始化，但不会修改图表的坐标轴设置
[2025-06-22 16:09:22.202] [INFO] [1] [LoggingService.Info] 复选框状态: chkXAutoRange.Checked=False
[2025-06-22 16:09:22.202] [INFO] [1] [LoggingService.Info] 事件绑定完成
[2025-06-22 16:09:22.203] [INFO] [1] [LoggingService.Info] ===== 坐标轴控制面板初始化完成 =====
[2025-06-22 16:09:22.203] [INFO] [1] [LoggingService.Info] 已更新搜索下拉框，共 11 个列
[2025-06-22 16:09:22.204] [INFO] [1] [LoggingService.Info] InitializeControls完成
[2025-06-22 16:09:22.204] [INFO] [1] [LoggingService.Info] 调用LoadData...
[2025-06-22 16:09:22.204] [INFO] [1] [LoggingService.Info] ===== 开始加载数据 =====
[2025-06-22 16:09:22.205] [INFO] [1] [LoggingService.Info] 数据行数: 79
[2025-06-22 16:09:22.205] [INFO] [1] [LoggingService.Info] 深度数量: 79
[2025-06-22 16:09:22.251] [INFO] [1] [LoggingService.Info] 堆叠柱状图数据已设置
[2025-06-22 16:09:22.252] [INFO] [1] [LoggingService.Info] 深度滑动条最大值设置为: 78
[2025-06-22 16:09:22.253] [INFO] [1] [LoggingService.Info] 开始更新饼状图...
[2025-06-22 16:09:22.255] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:22.255] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:22.255] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-06-22 16:09:22.255] [INFO] [1] [LoggingService.Info] 当前深度: 724.43m
[2025-06-22 16:09:22.256] [INFO] [1] [LoggingService.Info] 结果数据行数: 79
[2025-06-22 16:09:22.256] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 4, 塑性矿物数量: 1
[2025-06-22 16:09:22.256] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 724.43m
[2025-06-22 16:09:22.257] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-06-22 16:09:22.259] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:22.259] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:22.259] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:22.260] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:22.260] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:22.260] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:22.261] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:22.261] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:22.262] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:22.262] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:22.262] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:22.262] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:22.263] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:22.263] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:22.264] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 27.16%
[2025-06-22 16:09:22.264] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 24.29%
[2025-06-22 16:09:22.265] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 22.13%
[2025-06-22 16:09:22.265] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:22.265] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 73.58%
[2025-06-22 16:09:22.266] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:22.266] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 26.83%
[2025-06-22 16:09:22.266] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 26.83%
[2025-06-22 16:09:22.267] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:22.267] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:22.268] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 73.6%, 颜色: Color [Blue]
[2025-06-22 16:09:22.268] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 26.8%, 颜色: Color [Green]
[2025-06-22 16:09:22.268] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:22.269] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:22.269] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:22.270] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-06-22 16:09:22.270] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 40462633
[2025-06-22 16:09:22.271] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-06-22 16:09:22.271] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 40462633
[2025-06-22 16:09:22.271] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-06-22 16:09:22.272] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-06-22 16:09:22.272] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-06-22 16:09:22.272] [INFO] [1] [LoggingService.Info] 饼状图更新完成
[2025-06-22 16:09:22.273] [INFO] [1] [LoggingService.Info] ===== 数据加载完成 =====
[2025-06-22 16:09:22.273] [INFO] [1] [LoggingService.Info] LoadData完成
[2025-06-22 16:09:22.273] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数完成 =====
[2025-06-22 16:09:22.274] [INFO] [1] [LoggingService.Info] VisualizationForm创建成功，准备显示窗口...
[2025-06-22 16:09:22.310] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 开始 =====
[2025-06-22 16:09:22.311] [INFO] [1] [LoggingService.Info] 深度数据数量: 79
[2025-06-22 16:09:22.311] [INFO] [1] [LoggingService.Info] 设置当前深度索引: 0
[2025-06-22 16:09:22.311] [INFO] [1] [LoggingService.Info] 调用UpdateCurrentDepthLabel...
[2025-06-22 16:09:22.312] [INFO] [1] [LoggingService.Info] 调用UpdatePieChart...
[2025-06-22 16:09:22.312] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:22.312] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:22.313] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-06-22 16:09:22.313] [INFO] [1] [LoggingService.Info] 当前深度: 724.43m
[2025-06-22 16:09:22.313] [INFO] [1] [LoggingService.Info] 结果数据行数: 79
[2025-06-22 16:09:22.314] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 4, 塑性矿物数量: 1
[2025-06-22 16:09:22.314] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 724.43m
[2025-06-22 16:09:22.315] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-06-22 16:09:22.315] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:22.316] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:22.316] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:22.316] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:22.317] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:22.317] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:22.317] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:22.318] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:22.318] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:22.318] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:22.319] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:22.319] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:22.319] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:22.320] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:22.320] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 27.16%
[2025-06-22 16:09:22.321] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 24.29%
[2025-06-22 16:09:22.321] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 22.13%
[2025-06-22 16:09:22.321] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:22.322] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 73.58%
[2025-06-22 16:09:22.322] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:22.322] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 26.83%
[2025-06-22 16:09:22.323] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 26.83%
[2025-06-22 16:09:22.323] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:22.324] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:22.324] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 73.6%, 颜色: Color [Blue]
[2025-06-22 16:09:22.324] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 26.8%, 颜色: Color [Green]
[2025-06-22 16:09:22.325] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:22.325] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:22.326] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:22.326] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-06-22 16:09:22.326] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 40462633
[2025-06-22 16:09:22.327] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-06-22 16:09:22.327] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 40462633
[2025-06-22 16:09:22.388] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-06-22 16:09:22.389] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-06-22 16:09:22.389] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-06-22 16:09:22.389] [INFO] [1] [LoggingService.Info] UpdatePieChart完成
[2025-06-22 16:09:22.390] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 完成 =====
[2025-06-22 16:09:22.393] [INFO] [1] [LoggingService.Info] VisualizationForm.Show()调用完成
[2025-06-22 16:09:24.571] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:24.572] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1046, Y=527
[2025-06-22 16:09:24.572] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-06-22 16:09:24.573] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:24.573] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:24.574] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:24.574] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:24.583] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:24.584] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:24.585] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-06-22 16:09:24.585] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-06-22 16:09:24.585] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物, LegendText=脆性矿物, Value=73.58
[2025-06-22 16:09:24.586] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-06-22 16:09:24.586] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-06-22 16:09:24.586] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 脆性矿物，开始分裂
[2025-06-22 16:09:24.589] [INFO] [1] [LoggingService.Info] ===== 开始分裂饼状图: 脆性矿物 =====
[2025-06-22 16:09:24.590] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=79
[2025-06-22 16:09:24.590] [INFO] [1] [LoggingService.Info] 当前深度: 724.43m
[2025-06-22 16:09:24.591] [INFO] [1] [LoggingService.Info] 找到数据行，开始分裂处理
[2025-06-22 16:09:24.591] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:24.591] [INFO] [1] [LoggingService.Info] 系列数据点数量: 2, 点击索引: 0
[2025-06-22 16:09:24.592] [INFO] [1] [LoggingService.Info] 原始点信息: Tag=脆性矿物, LegendText=脆性矿物, Value=73.58
[2025-06-22 16:09:24.592] [INFO] [1] [LoggingService.Info] 原始点信息已保存
[2025-06-22 16:09:24.592] [INFO] [1] [LoggingService.Info] 要分裂的矿物类型: 脆性矿物, 矿物列表数量: 4
[2025-06-22 16:09:24.593] [INFO] [1] [LoggingService.Info] 矿物列表包含: 石英%
[2025-06-22 16:09:24.593] [INFO] [1] [LoggingService.Info] 矿物列表包含: 碳酸盐矿物%
[2025-06-22 16:09:24.594] [INFO] [1] [LoggingService.Info] 矿物列表包含: 斜长石%
[2025-06-22 16:09:24.594] [INFO] [1] [LoggingService.Info] 矿物列表包含: 钾长石（正长石）%
[2025-06-22 16:09:24.594] [INFO] [1] [LoggingService.Info] 检查矿物: 石英%
[2025-06-22 16:09:24.595] [INFO] [1] [LoggingService.Info] 数据表包含列: 石英%
[2025-06-22 16:09:24.595] [INFO] [1] [LoggingService.Info] 矿物 石英% 的值: 27.16
[2025-06-22 16:09:24.596] [INFO] [1] [LoggingService.Info] 添加子矿物: 石英% = 27.16, 类别总值: 27.16
[2025-06-22 16:09:24.596] [INFO] [1] [LoggingService.Info] 检查矿物: 碳酸盐矿物%
[2025-06-22 16:09:24.596] [INFO] [1] [LoggingService.Info] 数据表包含列: 碳酸盐矿物%
[2025-06-22 16:09:24.597] [INFO] [1] [LoggingService.Info] 矿物 碳酸盐矿物% 的值: 24.29
[2025-06-22 16:09:24.597] [INFO] [1] [LoggingService.Info] 添加子矿物: 碳酸盐矿物% = 24.29, 类别总值: 51.45
[2025-06-22 16:09:24.598] [INFO] [1] [LoggingService.Info] 检查矿物: 斜长石%
[2025-06-22 16:09:24.598] [INFO] [1] [LoggingService.Info] 数据表包含列: 斜长石%
[2025-06-22 16:09:24.598] [INFO] [1] [LoggingService.Info] 矿物 斜长石% 的值: 22.13
[2025-06-22 16:09:24.599] [INFO] [1] [LoggingService.Info] 添加子矿物: 斜长石% = 22.13, 类别总值: 73.58
[2025-06-22 16:09:24.599] [INFO] [1] [LoggingService.Info] 检查矿物: 钾长石（正长石）%
[2025-06-22 16:09:24.599] [INFO] [1] [LoggingService.Info] 数据表包含列: 钾长石（正长石）%
[2025-06-22 16:09:24.600] [INFO] [1] [LoggingService.Info] 矿物 钾长石（正长石）% 的值: 0
[2025-06-22 16:09:24.600] [INFO] [1] [LoggingService.Info] 矿物 钾长石（正长石）% 的值为0，跳过
[2025-06-22 16:09:24.600] [INFO] [1] [LoggingService.Info] 子矿物总数: 3, 类别总值: 73.58
[2025-06-22 16:09:24.601] [INFO] [1] [LoggingService.Info] 调整因子: 1 (原始值: 73.58, 类别总值: 73.58)
[2025-06-22 16:09:24.601] [INFO] [1] [LoggingService.Info] 调整子矿物值: 石英% 从 27.2% 调整为 27.2%
[2025-06-22 16:09:24.602] [INFO] [1] [LoggingService.Info] 调整子矿物值: 碳酸盐矿物% 从 24.3% 调整为 24.3%
[2025-06-22 16:09:24.602] [INFO] [1] [LoggingService.Info] 调整子矿物值: 斜长石% 从 22.1% 调整为 22.1%
[2025-06-22 16:09:24.602] [INFO] [1] [LoggingService.Info] 分裂饼状图扇形: 脆性矿物
[2025-06-22 16:09:24.603] [INFO] [1] [LoggingService.Info] 插入子矿物: 石英%, 调整后值: 27.2%, 在类别中占比: 36.9%
[2025-06-22 16:09:24.603] [INFO] [1] [LoggingService.Info] 插入子矿物: 碳酸盐矿物%, 调整后值: 24.3%, 在类别中占比: 33.0%
[2025-06-22 16:09:24.604] [INFO] [1] [LoggingService.Info] 插入子矿物: 斜长石%, 调整后值: 22.1%, 在类别中占比: 30.1%
[2025-06-22 16:09:24.604] [INFO] [1] [LoggingService.Info] 设置分裂状态
[2025-06-22 16:09:24.604] [INFO] [1] [LoggingService.Info] 强制重绘图表
[2025-06-22 16:09:24.605] [INFO] [1] [LoggingService.Info] ===== 饼状图分裂完成: 脆性矿物 =====
[2025-06-22 16:09:24.605] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:25.243] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:25.244] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1046, Y=514
[2025-06-22 16:09:25.244] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-06-22 16:09:25.244] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:25.245] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:25.245] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:25.245] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:25.256] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:25.256] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=PlottingArea
[2025-06-22 16:09:25.257] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=-1
[2025-06-22 16:09:25.257] [INFO] [1] [LoggingService.Info] HitTest结果: Series=null
[2025-06-22 16:09:25.257] [INFO] [1] [LoggingService.Info] 点击了其他图表元素: PlottingArea
[2025-06-22 16:09:25.257] [INFO] [1] [LoggingService.Info] 点击空白区域，当前有分裂或分散状态，执行还原
[2025-06-22 16:09:25.259] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:26.014] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:26.015] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1219, Y=636
[2025-06-22 16:09:26.016] [INFO] [1] [LoggingService.Info] 鼠标按键: Right
[2025-06-22 16:09:26.016] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:26.016] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:26.017] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:26.017] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:26.025] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:26.025] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:26.026] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-06-22 16:09:26.026] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-06-22 16:09:26.026] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物, LegendText=脆性矿物, Value=73.58
[2025-06-22 16:09:26.027] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-06-22 16:09:26.027] [INFO] [1] [LoggingService.Info] 处理右键点击
[2025-06-22 16:09:26.027] [INFO] [1] [LoggingService.Info] 右键点击饼状图，点击的是: 脆性矿物
[2025-06-22 16:09:26.028] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 脆性矿物，开始分散
[2025-06-22 16:09:26.030] [INFO] [1] [LoggingService.Info] ===== 开始分散饼状图: 脆性矿物 =====
[2025-06-22 16:09:26.030] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=79
[2025-06-22 16:09:26.030] [INFO] [1] [LoggingService.Info] 当前深度: 724.43m
[2025-06-22 16:09:26.031] [INFO] [1] [LoggingService.Info] 找到数据行，开始分散处理
[2025-06-22 16:09:26.031] [INFO] [1] [LoggingService.Info] 当前图表系列数量: 1
[2025-06-22 16:09:26.032] [INFO] [1] [LoggingService.Info] 清除现有系列
[2025-06-22 16:09:26.032] [INFO] [1] [LoggingService.Info] 系列已清除，当前系列数量: 0
[2025-06-22 16:09:26.032] [INFO] [1] [LoggingService.Info] 创建新的饼状图系列
[2025-06-22 16:09:26.033] [INFO] [1] [LoggingService.Info] 饼状图系列样式已设置（右键分散模式）
[2025-06-22 16:09:26.033] [INFO] [1] [LoggingService.Info] 开始分散显示，只保留选中的矿物类型: 脆性矿物
[2025-06-22 16:09:26.034] [INFO] [1] [LoggingService.Info] 脆性矿物总值: 73.58，具体矿物数量: 3
[2025-06-22 16:09:26.034] [INFO] [1] [LoggingService.Info] 添加具体矿物（条纹图案）: 石英% = 36.9%
[2025-06-22 16:09:26.035] [INFO] [1] [LoggingService.Info] 添加具体矿物（条纹图案）: 碳酸盐矿物% = 33.0%
[2025-06-22 16:09:26.035] [INFO] [1] [LoggingService.Info] 添加具体矿物（条纹图案）: 斜长石% = 30.1%
[2025-06-22 16:09:26.035] [INFO] [1] [LoggingService.Info] 添加系列到图表，系列点数: 3
[2025-06-22 16:09:26.036] [INFO] [1] [LoggingService.Info] 系列已添加，图表系列数量: 1
[2025-06-22 16:09:26.036] [INFO] [1] [LoggingService.Info] 更新图表标题
[2025-06-22 16:09:26.036] [INFO] [1] [LoggingService.Info] 图表标题已更新: 脆性矿物分类视图 (深度: 724m)
[2025-06-22 16:09:26.037] [INFO] [1] [LoggingService.Info] 设置分散状态
[2025-06-22 16:09:26.037] [INFO] [1] [LoggingService.Info] ===== 饼状图分散完成: 脆性矿物 =====
[2025-06-22 16:09:26.037] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:26.764] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:26.765] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1166, Y=555
[2025-06-22 16:09:26.765] [INFO] [1] [LoggingService.Info] 鼠标按键: Right
[2025-06-22 16:09:26.765] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:26.766] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:26.766] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:26.766] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:26.774] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:26.775] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:26.776] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=1
[2025-06-22 16:09:26.776] [INFO] [1] [LoggingService.Info] HitTest结果: Series=DispersedMineralRatio
[2025-06-22 16:09:26.776] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物_dispersed_碳酸盐矿物%, LegendText=碳酸盐矿物%, Value=33.011687958684426
[2025-06-22 16:09:26.777] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=True
[2025-06-22 16:09:26.777] [INFO] [1] [LoggingService.Info] 处理右键点击
[2025-06-22 16:09:26.777] [INFO] [1] [LoggingService.Info] 右键点击饼状图，点击的是: 脆性矿物_dispersed_碳酸盐矿物%
[2025-06-22 16:09:26.778] [INFO] [1] [LoggingService.Info] 点击了分散后的子矿物: 脆性矿物_dispersed_碳酸盐矿物%，开始还原
[2025-06-22 16:09:26.778] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:26.778] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:26.779] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:26.779] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:26.779] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:26.780] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:26.780] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:26.780] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:26.780] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:26.781] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:26.781] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:26.781] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:26.782] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:26.782] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:26.782] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 27.16%
[2025-06-22 16:09:26.782] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 24.29%
[2025-06-22 16:09:26.783] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 22.13%
[2025-06-22 16:09:26.783] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:26.783] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 73.58%
[2025-06-22 16:09:26.784] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:26.784] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 26.83%
[2025-06-22 16:09:26.784] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 26.83%
[2025-06-22 16:09:26.785] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:26.785] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:26.785] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 73.6%, 颜色: Color [Blue]
[2025-06-22 16:09:26.786] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 26.8%, 颜色: Color [Green]
[2025-06-22 16:09:26.786] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:26.786] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:26.787] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:26.787] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:27.552] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:27.553] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1402, Y=439
[2025-06-22 16:09:27.553] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-06-22 16:09:27.554] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:27.554] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:27.554] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:27.555] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:27.562] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:27.563] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:27.563] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=1
[2025-06-22 16:09:27.564] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-06-22 16:09:27.564] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=塑性矿物, LegendText=塑性矿物, Value=26.83
[2025-06-22 16:09:27.564] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-06-22 16:09:27.565] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-06-22 16:09:27.565] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 塑性矿物，开始分裂
[2025-06-22 16:09:27.565] [INFO] [1] [LoggingService.Info] ===== 开始分裂饼状图: 塑性矿物 =====
[2025-06-22 16:09:27.566] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=79
[2025-06-22 16:09:27.566] [INFO] [1] [LoggingService.Info] 当前深度: 724.43m
[2025-06-22 16:09:27.566] [INFO] [1] [LoggingService.Info] 找到数据行，开始分裂处理
[2025-06-22 16:09:27.566] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:27.567] [INFO] [1] [LoggingService.Info] 系列数据点数量: 2, 点击索引: 1
[2025-06-22 16:09:27.567] [INFO] [1] [LoggingService.Info] 原始点信息: Tag=塑性矿物, LegendText=塑性矿物, Value=26.83
[2025-06-22 16:09:27.567] [INFO] [1] [LoggingService.Info] 原始点信息已保存
[2025-06-22 16:09:27.568] [INFO] [1] [LoggingService.Info] 要分裂的矿物类型: 塑性矿物, 矿物列表数量: 1
[2025-06-22 16:09:27.568] [INFO] [1] [LoggingService.Info] 矿物列表包含: 黏土矿物总量%
[2025-06-22 16:09:27.568] [INFO] [1] [LoggingService.Info] 检查矿物: 黏土矿物总量%
[2025-06-22 16:09:27.569] [INFO] [1] [LoggingService.Info] 数据表包含列: 黏土矿物总量%
[2025-06-22 16:09:27.569] [INFO] [1] [LoggingService.Info] 矿物 黏土矿物总量% 的值: 26.83
[2025-06-22 16:09:27.569] [INFO] [1] [LoggingService.Info] 添加子矿物: 黏土矿物总量% = 26.83, 类别总值: 26.83
[2025-06-22 16:09:27.570] [INFO] [1] [LoggingService.Info] 子矿物总数: 1, 类别总值: 26.83
[2025-06-22 16:09:27.570] [INFO] [1] [LoggingService.Info] 调整因子: 1 (原始值: 26.83, 类别总值: 26.83)
[2025-06-22 16:09:27.570] [INFO] [1] [LoggingService.Info] 调整子矿物值: 黏土矿物总量% 从 26.8% 调整为 26.8%
[2025-06-22 16:09:27.570] [INFO] [1] [LoggingService.Info] 分裂饼状图扇形: 塑性矿物
[2025-06-22 16:09:27.571] [INFO] [1] [LoggingService.Info] 插入子矿物: 黏土矿物总量%, 调整后值: 26.8%, 在类别中占比: 100.0%
[2025-06-22 16:09:27.571] [INFO] [1] [LoggingService.Info] 设置分裂状态
[2025-06-22 16:09:27.571] [INFO] [1] [LoggingService.Info] 强制重绘图表
[2025-06-22 16:09:27.572] [INFO] [1] [LoggingService.Info] ===== 饼状图分裂完成: 塑性矿物 =====
[2025-06-22 16:09:27.572] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:28.123] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:28.124] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1402, Y=439
[2025-06-22 16:09:28.124] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-06-22 16:09:28.124] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:28.125] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:28.125] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:28.125] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:28.133] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:28.134] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:28.134] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=1
[2025-06-22 16:09:28.134] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-06-22 16:09:28.135] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=塑性矿物_split_黏土矿物总量%, LegendText=黏土矿物总量%, Value=26.83
[2025-06-22 16:09:28.135] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=True, _isDispersed=False
[2025-06-22 16:09:28.135] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-06-22 16:09:28.136] [INFO] [1] [LoggingService.Info] 点击了分裂后的子矿物: 塑性矿物_split_黏土矿物总量%，开始还原
[2025-06-22 16:09:28.136] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:28.583] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:28.584] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1402, Y=439
[2025-06-22 16:09:28.584] [INFO] [1] [LoggingService.Info] 鼠标按键: Right
[2025-06-22 16:09:28.584] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:28.585] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:28.585] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:28.585] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:28.593] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:28.593] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:28.595] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=1
[2025-06-22 16:09:28.596] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-06-22 16:09:28.596] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=塑性矿物, LegendText=塑性矿物, Value=26.83
[2025-06-22 16:09:28.597] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-06-22 16:09:28.597] [INFO] [1] [LoggingService.Info] 处理右键点击
[2025-06-22 16:09:28.598] [INFO] [1] [LoggingService.Info] 右键点击饼状图，点击的是: 塑性矿物
[2025-06-22 16:09:28.598] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 塑性矿物，开始分散
[2025-06-22 16:09:28.598] [INFO] [1] [LoggingService.Info] ===== 开始分散饼状图: 塑性矿物 =====
[2025-06-22 16:09:28.599] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=79
[2025-06-22 16:09:28.599] [INFO] [1] [LoggingService.Info] 当前深度: 724.43m
[2025-06-22 16:09:28.599] [INFO] [1] [LoggingService.Info] 找到数据行，开始分散处理
[2025-06-22 16:09:28.600] [INFO] [1] [LoggingService.Info] 当前图表系列数量: 1
[2025-06-22 16:09:28.600] [INFO] [1] [LoggingService.Info] 清除现有系列
[2025-06-22 16:09:28.601] [INFO] [1] [LoggingService.Info] 系列已清除，当前系列数量: 0
[2025-06-22 16:09:28.601] [INFO] [1] [LoggingService.Info] 创建新的饼状图系列
[2025-06-22 16:09:28.602] [INFO] [1] [LoggingService.Info] 饼状图系列样式已设置（右键分散模式）
[2025-06-22 16:09:28.602] [INFO] [1] [LoggingService.Info] 开始分散显示，只保留选中的矿物类型: 塑性矿物
[2025-06-22 16:09:28.602] [INFO] [1] [LoggingService.Info] 塑性矿物总值: 26.83，具体矿物数量: 1
[2025-06-22 16:09:28.603] [INFO] [1] [LoggingService.Info] 添加具体矿物（条纹图案）: 黏土矿物总量% = 100.0%
[2025-06-22 16:09:28.603] [INFO] [1] [LoggingService.Info] 添加系列到图表，系列点数: 1
[2025-06-22 16:09:28.603] [INFO] [1] [LoggingService.Info] 系列已添加，图表系列数量: 1
[2025-06-22 16:09:28.604] [INFO] [1] [LoggingService.Info] 更新图表标题
[2025-06-22 16:09:28.604] [INFO] [1] [LoggingService.Info] 图表标题已更新: 塑性矿物分类视图 (深度: 724m)
[2025-06-22 16:09:28.605] [INFO] [1] [LoggingService.Info] 设置分散状态
[2025-06-22 16:09:28.605] [INFO] [1] [LoggingService.Info] ===== 饼状图分散完成: 塑性矿物 =====
[2025-06-22 16:09:28.606] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:29.129] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-06-22 16:09:29.130] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1405, Y=439
[2025-06-22 16:09:29.130] [INFO] [1] [LoggingService.Info] 鼠标按键: Right
[2025-06-22 16:09:29.130] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-06-22 16:09:29.131] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-06-22 16:09:29.131] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-06-22 16:09:29.131] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-06-22 16:09:29.139] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-06-22 16:09:29.140] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-06-22 16:09:29.141] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-06-22 16:09:29.141] [INFO] [1] [LoggingService.Info] HitTest结果: Series=DispersedMineralRatio
[2025-06-22 16:09:29.141] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=塑性矿物_dispersed_黏土矿物总量%, LegendText=黏土矿物总量%, Value=100
[2025-06-22 16:09:29.142] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=True
[2025-06-22 16:09:29.142] [INFO] [1] [LoggingService.Info] 处理右键点击
[2025-06-22 16:09:29.142] [INFO] [1] [LoggingService.Info] 右键点击饼状图，点击的是: 塑性矿物_dispersed_黏土矿物总量%
[2025-06-22 16:09:29.143] [INFO] [1] [LoggingService.Info] 点击了分散后的子矿物: 塑性矿物_dispersed_黏土矿物总量%，开始还原
[2025-06-22 16:09:29.143] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:29.143] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:29.144] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:29.144] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:29.144] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:29.145] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:29.145] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:29.145] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:29.145] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:29.146] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:29.146] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:29.146] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:29.147] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:29.147] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:29.147] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 27.16%
[2025-06-22 16:09:29.147] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 24.29%
[2025-06-22 16:09:29.148] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 22.13%
[2025-06-22 16:09:29.148] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:29.148] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 73.58%
[2025-06-22 16:09:29.149] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:29.149] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 26.83%
[2025-06-22 16:09:29.149] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 26.83%
[2025-06-22 16:09:29.150] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:29.150] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:29.150] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 73.6%, 颜色: Color [Blue]
[2025-06-22 16:09:29.151] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 26.8%, 颜色: Color [Green]
[2025-06-22 16:09:29.151] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:29.151] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:29.152] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:29.152] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-06-22 16:09:30.415] [INFO] [1] [LoggingService.Info] ===== 深度滑动条值改变: 7 =====
[2025-06-22 16:09:30.415] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:30.416] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:30.416] [INFO] [1] [LoggingService.Info] 当前深度索引: 7
[2025-06-22 16:09:30.417] [INFO] [1] [LoggingService.Info] 当前深度: 727.23m
[2025-06-22 16:09:30.417] [INFO] [1] [LoggingService.Info] 结果数据行数: 79
[2025-06-22 16:09:30.417] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 4, 塑性矿物数量: 1
[2025-06-22 16:09:30.417] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 727.23m
[2025-06-22 16:09:30.418] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-06-22 16:09:30.418] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:30.418] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:30.419] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:30.419] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:30.419] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:30.420] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:30.420] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:30.420] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:30.421] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:30.421] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:30.421] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:30.421] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:30.422] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:30.422] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:30.422] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 30.77%
[2025-06-22 16:09:30.423] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 0%
[2025-06-22 16:09:30.423] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 34.7%
[2025-06-22 16:09:30.423] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:30.424] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 65.47%
[2025-06-22 16:09:30.424] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:30.424] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 35.93%
[2025-06-22 16:09:30.424] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 35.93%
[2025-06-22 16:09:30.425] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:30.425] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:30.426] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 65.5%, 颜色: Color [Blue]
[2025-06-22 16:09:30.426] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 35.9%, 颜色: Color [Green]
[2025-06-22 16:09:30.426] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:30.426] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:30.427] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:30.427] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-06-22 16:09:30.428] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 40462633
[2025-06-22 16:09:30.428] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-06-22 16:09:30.428] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 40462633
[2025-06-22 16:09:30.441] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-06-22 16:09:30.442] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-06-22 16:09:30.442] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-06-22 16:09:30.546] [INFO] [1] [LoggingService.Info] ===== 深度滑动条值改变: 14 =====
[2025-06-22 16:09:30.547] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:30.548] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:30.548] [INFO] [1] [LoggingService.Info] 当前深度索引: 14
[2025-06-22 16:09:30.548] [INFO] [1] [LoggingService.Info] 当前深度: 729.77m
[2025-06-22 16:09:30.549] [INFO] [1] [LoggingService.Info] 结果数据行数: 79
[2025-06-22 16:09:30.549] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 4, 塑性矿物数量: 1
[2025-06-22 16:09:30.549] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 729.77m
[2025-06-22 16:09:30.550] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-06-22 16:09:30.550] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:30.550] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:30.551] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:30.551] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:30.551] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:30.552] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:30.552] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:30.552] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:30.552] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:30.553] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:30.553] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:30.553] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:30.554] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:30.554] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:30.554] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 18.47%
[2025-06-22 16:09:30.555] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 29.61%
[2025-06-22 16:09:30.555] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 12.84%
[2025-06-22 16:09:30.555] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:30.555] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.92%
[2025-06-22 16:09:30.556] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:30.556] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 41%
[2025-06-22 16:09:30.556] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 41%
[2025-06-22 16:09:30.556] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:30.557] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:30.557] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.9%, 颜色: Color [Blue]
[2025-06-22 16:09:30.557] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 41.0%, 颜色: Color [Green]
[2025-06-22 16:09:30.558] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:30.558] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:30.558] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:30.559] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-06-22 16:09:30.559] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 40462633
[2025-06-22 16:09:30.559] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-06-22 16:09:30.560] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 40462633
[2025-06-22 16:09:30.572] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-06-22 16:09:30.573] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-06-22 16:09:30.573] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-06-22 16:09:30.605] [INFO] [1] [LoggingService.Info] ===== 深度滑动条值改变: 21 =====
[2025-06-22 16:09:30.606] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:30.606] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:30.607] [INFO] [1] [LoggingService.Info] 当前深度索引: 21
[2025-06-22 16:09:30.607] [INFO] [1] [LoggingService.Info] 当前深度: 732.32m
[2025-06-22 16:09:30.607] [INFO] [1] [LoggingService.Info] 结果数据行数: 79
[2025-06-22 16:09:30.607] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 4, 塑性矿物数量: 1
[2025-06-22 16:09:30.608] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 732.32m
[2025-06-22 16:09:30.608] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-06-22 16:09:30.608] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:30.609] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:30.609] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:30.609] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:30.610] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:30.610] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:30.610] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:30.611] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:30.611] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:30.611] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:30.611] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:30.612] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:30.612] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:30.612] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:30.613] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 31.78%
[2025-06-22 16:09:30.613] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 25.49%
[2025-06-22 16:09:30.613] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 18.27%
[2025-06-22 16:09:30.614] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:30.614] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 75.53999999999999%
[2025-06-22 16:09:30.614] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:30.614] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 19.65%
[2025-06-22 16:09:30.615] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 19.65%
[2025-06-22 16:09:30.616] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:30.616] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:30.616] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 75.5%, 颜色: Color [Blue]
[2025-06-22 16:09:30.617] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 19.6%, 颜色: Color [Green]
[2025-06-22 16:09:30.617] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:30.617] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:30.618] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:30.618] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-06-22 16:09:30.618] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 40462633
[2025-06-22 16:09:30.619] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-06-22 16:09:30.619] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 40462633
[2025-06-22 16:09:30.631] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-06-22 16:09:30.631] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-06-22 16:09:30.631] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-06-22 16:09:30.807] [INFO] [1] [LoggingService.Info] ===== 深度滑动条值改变: 28 =====
[2025-06-22 16:09:30.808] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:30.809] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:30.809] [INFO] [1] [LoggingService.Info] 当前深度索引: 28
[2025-06-22 16:09:30.809] [INFO] [1] [LoggingService.Info] 当前深度: 735.43m
[2025-06-22 16:09:30.810] [INFO] [1] [LoggingService.Info] 结果数据行数: 79
[2025-06-22 16:09:30.810] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 4, 塑性矿物数量: 1
[2025-06-22 16:09:30.810] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 735.43m
[2025-06-22 16:09:30.810] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-06-22 16:09:30.811] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-06-22 16:09:30.811] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-06-22 16:09:30.811] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-06-22 16:09:30.812] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-06-22 16:09:30.812] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-06-22 16:09:30.812] [INFO] [1] [LoggingService.Info]   列名: '石英%'
[2025-06-22 16:09:30.813] [INFO] [1] [LoggingService.Info]   列名: '碳酸盐矿物%'
[2025-06-22 16:09:30.813] [INFO] [1] [LoggingService.Info]   列名: '斜长石%'
[2025-06-22 16:09:30.813] [INFO] [1] [LoggingService.Info]   列名: '钾长石（正长石）%'
[2025-06-22 16:09:30.813] [INFO] [1] [LoggingService.Info]   列名: '黏土矿物总量%'
[2025-06-22 16:09:30.814] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-06-22 16:09:30.814] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-06-22 16:09:30.814] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-06-22 16:09:30.815] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-06-22 16:09:30.815] [INFO] [1] [LoggingService.Info]   石英% (列名: 石英%): 13.17%
[2025-06-22 16:09:30.816] [INFO] [1] [LoggingService.Info]   碳酸盐矿物% (列名: 碳酸盐矿物%): 39.92%
[2025-06-22 16:09:30.816] [INFO] [1] [LoggingService.Info]   斜长石% (列名: 斜长石%): 1.43%
[2025-06-22 16:09:30.816] [INFO] [1] [LoggingService.Info]   钾长石（正长石）% (列名: 钾长石（正长石）%): 0%
[2025-06-22 16:09:30.817] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 54.52%
[2025-06-22 16:09:30.817] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-06-22 16:09:30.817] [INFO] [1] [LoggingService.Info]   黏土矿物总量% (列名: 黏土矿物总量%): 43.01%
[2025-06-22 16:09:30.818] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 43.01%
[2025-06-22 16:09:30.818] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-06-22 16:09:30.818] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-06-22 16:09:30.818] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 54.5%, 颜色: Color [Blue]
[2025-06-22 16:09:30.819] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 43.0%, 颜色: Color [Green]
[2025-06-22 16:09:30.819] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-06-22 16:09:30.819] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-06-22 16:09:30.820] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-06-22 16:09:30.820] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-06-22 16:09:30.820] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 40462633
[2025-06-22 16:09:30.821] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-06-22 16:09:30.821] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 40462633
[2025-06-22 16:09:30.833] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-06-22 16:09:30.834] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-06-22 16:09:30.834] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-06-22 16:09:44.157] [INFO] [1] [LoggingService.Info] ===== 开始加载数据 =====
[2025-06-22 16:09:44.158] [INFO] [1] [LoggingService.Info] 数据行数: 15
[2025-06-22 16:09:44.158] [INFO] [1] [LoggingService.Info] 深度数量: 15
[2025-06-22 16:09:44.266] [INFO] [1] [LoggingService.Info] 堆叠柱状图数据已设置
[2025-06-22 16:09:44.267] [INFO] [1] [LoggingService.Info] 深度滑动条最大值设置为: 14
[2025-06-22 16:09:44.267] [INFO] [1] [LoggingService.Info] 开始更新饼状图...
[2025-06-22 16:09:44.268] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-06-22 16:09:44.268] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-06-22 16:09:44.268] [INFO] [1] [LoggingService.Info] 错误: 深度索引无效 - _currentDepthIndex=28, _depths.Count=15
[2025-06-22 16:09:44.269] [INFO] [1] [LoggingService.Info] 饼状图更新完成
[2025-06-22 16:09:44.269] [INFO] [1] [LoggingService.Info] ===== 数据加载完成 =====
[2025-06-22 16:10:01.394] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统关闭
[2025-07-01 10:58:47.325] [INFO] [1] [LoggingService.Info] 日志服务已初始化
[2025-07-01 10:58:47.335] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统启动
[2025-07-01 11:09:08.988] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统关闭
[2025-07-01 11:26:24.044] [INFO] [1] [LoggingService.Info] 日志服务已初始化
[2025-07-01 11:26:24.053] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统启动
[2025-07-01 15:13:24.227] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-07-01 15:13:24.228] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=21, 列数=12
[2025-07-01 15:13:24.229] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:13:24.229] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:13:24.230] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:13:24.230] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:13:24.231] [INFO] [1] [LoggingService.Info] 最终脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:13:24.231] [INFO] [1] [LoggingService.Info] 最终塑性矿物列: Column41
[2025-07-01 15:13:24.232] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-07-01 15:13:24.233] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数开始 =====
[2025-07-01 15:13:24.234] [INFO] [1] [LoggingService.Info] 接收到数据: 行数=21, 脆性矿物=5, 塑性矿物=1
[2025-07-01 15:13:24.234] [INFO] [1] [LoggingService.Info] 脆性矿物列表: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:13:24.235] [INFO] [1] [LoggingService.Info] 塑性矿物列表: Column41
[2025-07-01 15:13:24.235] [INFO] [1] [LoggingService.Info] 调用InitializeComponent...
[2025-07-01 15:13:24.264] [INFO] [1] [LoggingService.Info] InitializeComponent完成
[2025-07-01 15:13:24.265] [INFO] [1] [LoggingService.Info] 调用InitializeDepthData...
[2025-07-01 15:13:24.271] [INFO] [1] [LoggingService.Info] InitializeDepthData完成
[2025-07-01 15:13:24.271] [INFO] [1] [LoggingService.Info] 调用InitializeControls...
[2025-07-01 15:13:24.272] [INFO] [1] [LoggingService.Info] ===== 初始化饼状图控件 =====
[2025-07-01 15:13:24.273] [INFO] [1] [LoggingService.Info] 深度滑动条设置: 范围0-20
[2025-07-01 15:13:24.273] [INFO] [1] [LoggingService.Info] 开始初始化chartPie控件
[2025-07-01 15:13:24.274] [INFO] [1] [LoggingService.Info] ChartArea已创建
[2025-07-01 15:13:24.274] [INFO] [1] [LoggingService.Info] Legend已创建
[2025-07-01 15:13:24.275] [INFO] [1] [LoggingService.Info] Title已创建
[2025-07-01 15:13:24.275] [INFO] [1] [LoggingService.Info] chartPie控件初始化完成
[2025-07-01 15:13:24.276] [INFO] [1] [LoggingService.Info] ===== 饼状图控件初始化完成 =====
[2025-07-01 15:13:24.279] [INFO] [1] [LoggingService.Info] ===== 初始化坐标轴控制面板 =====
[2025-07-01 15:13:24.280] [INFO] [1] [LoggingService.Info] X轴默认值设置完成
[2025-07-01 15:13:24.281] [INFO] [1] [LoggingService.Info] 坐标轴控制面板已初始化，但不会修改图表的坐标轴设置
[2025-07-01 15:13:24.281] [INFO] [1] [LoggingService.Info] 复选框状态: chkXAutoRange.Checked=False
[2025-07-01 15:13:24.281] [INFO] [1] [LoggingService.Info] 事件绑定完成
[2025-07-01 15:13:24.282] [INFO] [1] [LoggingService.Info] ===== 坐标轴控制面板初始化完成 =====
[2025-07-01 15:13:24.283] [INFO] [1] [LoggingService.Info] 已更新搜索下拉框，共 12 个列
[2025-07-01 15:13:24.283] [INFO] [1] [LoggingService.Info] InitializeControls完成
[2025-07-01 15:13:24.283] [INFO] [1] [LoggingService.Info] 调用LoadData...
[2025-07-01 15:13:24.284] [INFO] [1] [LoggingService.Info] ===== 开始加载数据 =====
[2025-07-01 15:13:24.284] [INFO] [1] [LoggingService.Info] 数据行数: 21
[2025-07-01 15:13:24.284] [INFO] [1] [LoggingService.Info] 深度数量: 21
[2025-07-01 15:13:24.310] [INFO] [1] [LoggingService.Info] 堆叠柱状图数据已设置
[2025-07-01 15:13:24.311] [INFO] [1] [LoggingService.Info] 深度滑动条最大值设置为: 20
[2025-07-01 15:13:24.312] [INFO] [1] [LoggingService.Info] 开始更新饼状图...
[2025-07-01 15:13:24.313] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:13:24.314] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:13:24.314] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:13:24.314] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:13:24.315] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:13:24.315] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:13:24.316] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:13:24.316] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:13:24.318] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:13:24.318] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:13:24.318] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:13:24.319] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:13:24.319] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:13:24.319] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:13:24.320] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:13:24.320] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:13:24.320] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:13:24.321] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:13:24.321] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:13:24.321] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:13:24.322] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:13:24.322] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:13:24.322] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:13:24.323] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:13:24.324] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:13:24.324] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:13:24.324] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:13:24.325] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:13:24.325] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:13:24.325] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:13:24.326] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:13:24.326] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:13:24.327] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:13:24.327] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:13:24.328] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:13:24.328] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:13:24.329] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:13:24.329] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:13:24.329] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:13:24.330] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:13:24.330] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 39784704
[2025-07-01 15:13:24.331] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:13:24.331] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 39784704
[2025-07-01 15:13:24.331] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:13:24.332] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:13:24.332] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:13:24.332] [INFO] [1] [LoggingService.Info] 饼状图更新完成
[2025-07-01 15:13:24.333] [INFO] [1] [LoggingService.Info] ===== 数据加载完成 =====
[2025-07-01 15:13:24.333] [INFO] [1] [LoggingService.Info] LoadData完成
[2025-07-01 15:13:24.333] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数完成 =====
[2025-07-01 15:13:24.334] [INFO] [1] [LoggingService.Info] VisualizationForm创建成功，准备显示窗口...
[2025-07-01 15:13:24.365] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 开始 =====
[2025-07-01 15:13:24.366] [INFO] [1] [LoggingService.Info] 深度数据数量: 21
[2025-07-01 15:13:24.366] [INFO] [1] [LoggingService.Info] 设置当前深度索引: 0
[2025-07-01 15:13:24.366] [INFO] [1] [LoggingService.Info] 调用UpdateCurrentDepthLabel...
[2025-07-01 15:13:24.367] [INFO] [1] [LoggingService.Info] 调用UpdatePieChart...
[2025-07-01 15:13:24.367] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:13:24.368] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:13:24.368] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:13:24.368] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:13:24.369] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:13:24.369] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:13:24.369] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:13:24.370] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:13:24.370] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:13:24.371] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:13:24.371] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:13:24.371] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:13:24.372] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:13:24.372] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:13:24.373] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:13:24.373] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:13:24.373] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:13:24.374] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:13:24.374] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:13:24.374] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:13:24.375] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:13:24.375] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:13:24.376] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:13:24.376] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:13:24.377] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:13:24.377] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:13:24.377] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:13:24.378] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:13:24.378] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:13:24.379] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:13:24.379] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:13:24.379] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:13:24.380] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:13:24.380] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:13:24.381] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:13:24.381] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:13:24.381] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:13:24.382] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:13:24.382] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:13:24.383] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:13:24.383] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 39784704
[2025-07-01 15:13:24.383] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:13:24.384] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 39784704
[2025-07-01 15:13:24.440] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:13:24.441] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:13:24.441] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:13:24.442] [INFO] [1] [LoggingService.Info] UpdatePieChart完成
[2025-07-01 15:13:24.442] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 完成 =====
[2025-07-01 15:13:24.445] [INFO] [1] [LoggingService.Info] VisualizationForm.Show()调用完成
[2025-07-01 15:13:43.008] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:13:43.009] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1307, Y=734
[2025-07-01 15:13:43.009] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:13:43.010] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:13:43.010] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:13:43.010] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:13:43.011] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:13:43.020] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:13:43.021] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:13:43.022] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:13:43.022] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:13:43.023] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:13:43.023] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-07-01 15:13:43.024] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:13:43.024] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 脆性矿物，开始分裂
[2025-07-01 15:13:43.028] [INFO] [1] [LoggingService.Info] ===== 开始分裂饼状图: 脆性矿物 =====
[2025-07-01 15:13:43.028] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=21
[2025-07-01 15:13:43.029] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:13:43.030] [INFO] [1] [LoggingService.Info] 找到数据行，开始分裂处理
[2025-07-01 15:13:43.030] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:13:43.031] [INFO] [1] [LoggingService.Info] 系列数据点数量: 2, 点击索引: 0
[2025-07-01 15:13:43.031] [INFO] [1] [LoggingService.Info] 原始点信息: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:13:43.032] [INFO] [1] [LoggingService.Info] 原始点信息已保存
[2025-07-01 15:13:43.032] [INFO] [1] [LoggingService.Info] 要分裂的矿物类型: 脆性矿物, 矿物列表数量: 5
[2025-07-01 15:13:43.033] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column42
[2025-07-01 15:13:43.033] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column43
[2025-07-01 15:13:43.034] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column44
[2025-07-01 15:13:43.034] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column45
[2025-07-01 15:13:43.034] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column46
[2025-07-01 15:13:43.035] [INFO] [1] [LoggingService.Info] 检查矿物: Column42
[2025-07-01 15:13:43.036] [INFO] [1] [LoggingService.Info] 数据表包含列: Column42
[2025-07-01 15:13:43.036] [INFO] [1] [LoggingService.Info] 矿物 Column42 的值: 35.01
[2025-07-01 15:13:43.037] [INFO] [1] [LoggingService.Info] 添加子矿物: Column42 = 35.01, 类别总值: 35.01
[2025-07-01 15:13:43.037] [INFO] [1] [LoggingService.Info] 检查矿物: Column43
[2025-07-01 15:13:43.037] [INFO] [1] [LoggingService.Info] 数据表包含列: Column43
[2025-07-01 15:13:43.038] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值: 0
[2025-07-01 15:13:43.038] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值为0，跳过
[2025-07-01 15:13:43.038] [INFO] [1] [LoggingService.Info] 检查矿物: Column44
[2025-07-01 15:13:43.039] [INFO] [1] [LoggingService.Info] 数据表包含列: Column44
[2025-07-01 15:13:43.039] [INFO] [1] [LoggingService.Info] 矿物 Column44 的值: 19.25
[2025-07-01 15:13:43.039] [INFO] [1] [LoggingService.Info] 添加子矿物: Column44 = 19.25, 类别总值: 54.26
[2025-07-01 15:13:43.040] [INFO] [1] [LoggingService.Info] 检查矿物: Column45
[2025-07-01 15:13:43.040] [INFO] [1] [LoggingService.Info] 数据表包含列: Column45
[2025-07-01 15:13:43.040] [INFO] [1] [LoggingService.Info] 矿物 Column45 的值: 5.96
[2025-07-01 15:13:43.041] [INFO] [1] [LoggingService.Info] 添加子矿物: Column45 = 5.96, 类别总值: 60.22
[2025-07-01 15:13:43.041] [INFO] [1] [LoggingService.Info] 检查矿物: Column46
[2025-07-01 15:13:43.041] [INFO] [1] [LoggingService.Info] 数据表包含列: Column46
[2025-07-01 15:13:43.042] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值: 0
[2025-07-01 15:13:43.042] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值为0，跳过
[2025-07-01 15:13:43.043] [INFO] [1] [LoggingService.Info] 子矿物总数: 3, 类别总值: 60.22
[2025-07-01 15:13:43.043] [INFO] [1] [LoggingService.Info] 调整因子: 1 (原始值: 60.22, 类别总值: 60.22)
[2025-07-01 15:13:43.043] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column42 从 35.0% 调整为 35.0%
[2025-07-01 15:13:43.044] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column44 从 19.2% 调整为 19.2%
[2025-07-01 15:13:43.044] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column45 从 6.0% 调整为 6.0%
[2025-07-01 15:13:43.044] [INFO] [1] [LoggingService.Info] 分裂饼状图扇形: 脆性矿物
[2025-07-01 15:13:43.045] [INFO] [1] [LoggingService.Info] 插入子矿物: Column42, 调整后值: 35.0%, 在类别中占比: 58.1%
[2025-07-01 15:13:43.045] [INFO] [1] [LoggingService.Info] 插入子矿物: Column44, 调整后值: 19.2%, 在类别中占比: 32.0%
[2025-07-01 15:13:43.046] [INFO] [1] [LoggingService.Info] 插入子矿物: Column45, 调整后值: 6.0%, 在类别中占比: 9.9%
[2025-07-01 15:13:43.046] [INFO] [1] [LoggingService.Info] 设置分裂状态
[2025-07-01 15:13:43.047] [INFO] [1] [LoggingService.Info] 强制重绘图表
[2025-07-01 15:13:43.047] [INFO] [1] [LoggingService.Info] ===== 饼状图分裂完成: 脆性矿物 =====
[2025-07-01 15:13:43.047] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:13:44.509] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:13:44.509] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1324, Y=727
[2025-07-01 15:13:44.510] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:13:44.510] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:13:44.510] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:13:44.511] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:13:44.511] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:13:44.519] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:13:44.519] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:13:44.520] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:13:44.520] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:13:44.521] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物_split_Column42, LegendText=Column42, Value=35.01
[2025-07-01 15:13:44.521] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=True, _isDispersed=False
[2025-07-01 15:13:44.522] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:13:44.522] [INFO] [1] [LoggingService.Info] 点击了分裂后的子矿物: 脆性矿物_split_Column42，开始还原
[2025-07-01 15:13:44.524] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:13:44.889] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:13:44.890] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1128, Y=685
[2025-07-01 15:13:44.890] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:13:44.891] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:13:44.891] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:13:44.892] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:13:44.892] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:13:44.904] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:13:44.905] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:13:44.906] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:13:44.906] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:13:44.907] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:13:44.907] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-07-01 15:13:44.908] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:13:44.908] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 脆性矿物，开始分裂
[2025-07-01 15:13:44.909] [INFO] [1] [LoggingService.Info] ===== 开始分裂饼状图: 脆性矿物 =====
[2025-07-01 15:13:44.909] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=21
[2025-07-01 15:13:44.910] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:13:44.910] [INFO] [1] [LoggingService.Info] 找到数据行，开始分裂处理
[2025-07-01 15:13:44.910] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:13:44.911] [INFO] [1] [LoggingService.Info] 系列数据点数量: 2, 点击索引: 0
[2025-07-01 15:13:44.911] [INFO] [1] [LoggingService.Info] 原始点信息: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:13:44.912] [INFO] [1] [LoggingService.Info] 原始点信息已保存
[2025-07-01 15:13:44.912] [INFO] [1] [LoggingService.Info] 要分裂的矿物类型: 脆性矿物, 矿物列表数量: 5
[2025-07-01 15:13:44.912] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column42
[2025-07-01 15:13:44.913] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column43
[2025-07-01 15:13:44.914] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column44
[2025-07-01 15:13:44.914] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column45
[2025-07-01 15:13:44.914] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column46
[2025-07-01 15:13:44.915] [INFO] [1] [LoggingService.Info] 检查矿物: Column42
[2025-07-01 15:13:44.915] [INFO] [1] [LoggingService.Info] 数据表包含列: Column42
[2025-07-01 15:13:44.915] [INFO] [1] [LoggingService.Info] 矿物 Column42 的值: 35.01
[2025-07-01 15:13:44.916] [INFO] [1] [LoggingService.Info] 添加子矿物: Column42 = 35.01, 类别总值: 35.01
[2025-07-01 15:13:44.916] [INFO] [1] [LoggingService.Info] 检查矿物: Column43
[2025-07-01 15:13:44.916] [INFO] [1] [LoggingService.Info] 数据表包含列: Column43
[2025-07-01 15:13:44.917] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值: 0
[2025-07-01 15:13:44.917] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值为0，跳过
[2025-07-01 15:13:44.917] [INFO] [1] [LoggingService.Info] 检查矿物: Column44
[2025-07-01 15:13:44.918] [INFO] [1] [LoggingService.Info] 数据表包含列: Column44
[2025-07-01 15:13:44.918] [INFO] [1] [LoggingService.Info] 矿物 Column44 的值: 19.25
[2025-07-01 15:13:44.918] [INFO] [1] [LoggingService.Info] 添加子矿物: Column44 = 19.25, 类别总值: 54.26
[2025-07-01 15:13:44.919] [INFO] [1] [LoggingService.Info] 检查矿物: Column45
[2025-07-01 15:13:44.919] [INFO] [1] [LoggingService.Info] 数据表包含列: Column45
[2025-07-01 15:13:44.919] [INFO] [1] [LoggingService.Info] 矿物 Column45 的值: 5.96
[2025-07-01 15:13:44.920] [INFO] [1] [LoggingService.Info] 添加子矿物: Column45 = 5.96, 类别总值: 60.22
[2025-07-01 15:13:44.920] [INFO] [1] [LoggingService.Info] 检查矿物: Column46
[2025-07-01 15:13:44.920] [INFO] [1] [LoggingService.Info] 数据表包含列: Column46
[2025-07-01 15:13:44.921] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值: 0
[2025-07-01 15:13:44.921] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值为0，跳过
[2025-07-01 15:13:44.922] [INFO] [1] [LoggingService.Info] 子矿物总数: 3, 类别总值: 60.22
[2025-07-01 15:13:44.922] [INFO] [1] [LoggingService.Info] 调整因子: 1 (原始值: 60.22, 类别总值: 60.22)
[2025-07-01 15:13:44.922] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column42 从 35.0% 调整为 35.0%
[2025-07-01 15:13:44.923] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column44 从 19.2% 调整为 19.2%
[2025-07-01 15:13:44.923] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column45 从 6.0% 调整为 6.0%
[2025-07-01 15:13:44.924] [INFO] [1] [LoggingService.Info] 分裂饼状图扇形: 脆性矿物
[2025-07-01 15:13:44.924] [INFO] [1] [LoggingService.Info] 插入子矿物: Column42, 调整后值: 35.0%, 在类别中占比: 58.1%
[2025-07-01 15:13:44.925] [INFO] [1] [LoggingService.Info] 插入子矿物: Column44, 调整后值: 19.2%, 在类别中占比: 32.0%
[2025-07-01 15:13:44.925] [INFO] [1] [LoggingService.Info] 插入子矿物: Column45, 调整后值: 6.0%, 在类别中占比: 9.9%
[2025-07-01 15:13:44.925] [INFO] [1] [LoggingService.Info] 设置分裂状态
[2025-07-01 15:13:44.926] [INFO] [1] [LoggingService.Info] 强制重绘图表
[2025-07-01 15:13:44.926] [INFO] [1] [LoggingService.Info] ===== 饼状图分裂完成: 脆性矿物 =====
[2025-07-01 15:13:44.926] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:13:45.518] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:13:45.519] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1354, Y=702
[2025-07-01 15:13:45.519] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:13:45.519] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:13:45.519] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:13:45.520] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:13:45.520] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:13:45.528] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:13:45.529] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:13:45.529] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:13:45.529] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:13:45.530] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物_split_Column42, LegendText=Column42, Value=35.01
[2025-07-01 15:13:45.530] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=True, _isDispersed=False
[2025-07-01 15:13:45.530] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:13:45.531] [INFO] [1] [LoggingService.Info] 点击了分裂后的子矿物: 脆性矿物_split_Column42，开始还原
[2025-07-01 15:13:45.531] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:13:56.092] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统关闭
[2025-07-01 15:43:04.376] [INFO] [1] [LoggingService.Info] 日志服务已初始化
[2025-07-01 15:43:04.386] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统启动
[2025-07-01 15:46:24.061] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-07-01 15:46:24.061] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=21, 列数=12
[2025-07-01 15:46:24.062] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:46:24.063] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:46:24.063] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:46:24.063] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:46:24.064] [INFO] [1] [LoggingService.Info] BrittlenessCalculator生成的脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:46:24.064] [INFO] [1] [LoggingService.Info] BrittlenessCalculator生成的塑性矿物列: Column41
[2025-07-01 15:46:24.065] [INFO] [1] [LoggingService.Info] 结果数据表中的所有列: GeoID, 顶深/m, 底深/m, Column42, Column43, Column44, Column45, Column46, Column41, 脆性指数, 脆性矿物总量, 塑性矿物总量
[2025-07-01 15:46:24.066] [INFO] [1] [LoggingService.Info] 验证后的脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:46:24.066] [INFO] [1] [LoggingService.Info] 验证后的塑性矿物列: Column41
[2025-07-01 15:46:24.067] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-07-01 15:46:24.068] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数开始 =====
[2025-07-01 15:46:24.069] [INFO] [1] [LoggingService.Info] 接收到数据: 行数=21, 脆性矿物=5, 塑性矿物=1
[2025-07-01 15:46:24.069] [INFO] [1] [LoggingService.Info] 脆性矿物列表: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:46:24.069] [INFO] [1] [LoggingService.Info] 塑性矿物列表: Column41
[2025-07-01 15:46:24.070] [INFO] [1] [LoggingService.Info] 调用InitializeComponent...
[2025-07-01 15:46:24.096] [INFO] [1] [LoggingService.Info] InitializeComponent完成
[2025-07-01 15:46:24.097] [INFO] [1] [LoggingService.Info] 调用InitializeDepthData...
[2025-07-01 15:46:24.105] [INFO] [1] [LoggingService.Info] InitializeDepthData完成
[2025-07-01 15:46:24.106] [INFO] [1] [LoggingService.Info] 调用InitializeControls...
[2025-07-01 15:46:24.107] [INFO] [1] [LoggingService.Info] ===== 初始化饼状图控件 =====
[2025-07-01 15:46:24.107] [INFO] [1] [LoggingService.Info] 深度滑动条设置: 范围0-20
[2025-07-01 15:46:24.108] [INFO] [1] [LoggingService.Info] 开始初始化chartPie控件
[2025-07-01 15:46:24.108] [INFO] [1] [LoggingService.Info] ChartArea已创建
[2025-07-01 15:46:24.109] [INFO] [1] [LoggingService.Info] Legend已创建
[2025-07-01 15:46:24.109] [INFO] [1] [LoggingService.Info] Title已创建
[2025-07-01 15:46:24.110] [INFO] [1] [LoggingService.Info] chartPie控件初始化完成
[2025-07-01 15:46:24.110] [INFO] [1] [LoggingService.Info] ===== 饼状图控件初始化完成 =====
[2025-07-01 15:46:24.113] [INFO] [1] [LoggingService.Info] ===== 初始化坐标轴控制面板 =====
[2025-07-01 15:46:24.114] [INFO] [1] [LoggingService.Info] X轴默认值设置完成
[2025-07-01 15:46:24.114] [INFO] [1] [LoggingService.Info] 坐标轴控制面板已初始化，但不会修改图表的坐标轴设置
[2025-07-01 15:46:24.115] [INFO] [1] [LoggingService.Info] 复选框状态: chkXAutoRange.Checked=False
[2025-07-01 15:46:24.115] [INFO] [1] [LoggingService.Info] 事件绑定完成
[2025-07-01 15:46:24.116] [INFO] [1] [LoggingService.Info] ===== 坐标轴控制面板初始化完成 =====
[2025-07-01 15:46:24.116] [INFO] [1] [LoggingService.Info] 已更新搜索下拉框，共 12 个列
[2025-07-01 15:46:24.117] [INFO] [1] [LoggingService.Info] InitializeControls完成
[2025-07-01 15:46:24.117] [INFO] [1] [LoggingService.Info] 调用LoadData...
[2025-07-01 15:46:24.118] [INFO] [1] [LoggingService.Info] ===== 开始加载数据 =====
[2025-07-01 15:46:24.118] [INFO] [1] [LoggingService.Info] 数据行数: 21
[2025-07-01 15:46:24.118] [INFO] [1] [LoggingService.Info] 深度数量: 21
[2025-07-01 15:46:24.145] [INFO] [1] [LoggingService.Info] 堆叠柱状图数据已设置
[2025-07-01 15:46:24.146] [INFO] [1] [LoggingService.Info] 深度滑动条最大值设置为: 20
[2025-07-01 15:46:24.147] [INFO] [1] [LoggingService.Info] 开始更新饼状图...
[2025-07-01 15:46:24.148] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:46:24.148] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:46:24.149] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:46:24.149] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:46:24.149] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:46:24.150] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:46:24.150] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:46:24.151] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:46:24.152] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:46:24.152] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:46:24.153] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:46:24.153] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:46:24.153] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:46:24.154] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:46:24.154] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:46:24.154] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:46:24.155] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:46:24.155] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:46:24.155] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:46:24.156] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:46:24.156] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:46:24.156] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:46:24.157] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:46:24.157] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column42 -> Column42
[2025-07-01 15:46:24.158] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:46:24.158] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column43 -> Column43
[2025-07-01 15:46:24.158] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:46:24.159] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column44 -> Column44
[2025-07-01 15:46:24.159] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:46:24.159] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column45 -> Column45
[2025-07-01 15:46:24.160] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:46:24.160] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column46 -> Column46
[2025-07-01 15:46:24.160] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:46:24.161] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:46:24.161] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:46:24.161] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column41 -> Column41
[2025-07-01 15:46:24.162] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:46:24.162] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:46:24.162] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:46:24.163] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:46:24.163] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:46:24.164] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:46:24.164] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:46:24.164] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:46:24.165] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:46:24.165] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:46:24.166] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 22681099
[2025-07-01 15:46:24.166] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:46:24.166] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 22681099
[2025-07-01 15:46:24.167] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:46:24.167] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:46:24.167] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:46:24.168] [INFO] [1] [LoggingService.Info] 饼状图更新完成
[2025-07-01 15:46:24.168] [INFO] [1] [LoggingService.Info] ===== 数据加载完成 =====
[2025-07-01 15:46:24.168] [INFO] [1] [LoggingService.Info] LoadData完成
[2025-07-01 15:46:24.168] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数完成 =====
[2025-07-01 15:46:24.169] [INFO] [1] [LoggingService.Info] VisualizationForm创建成功，准备显示窗口...
[2025-07-01 15:46:24.192] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 开始 =====
[2025-07-01 15:46:24.193] [INFO] [1] [LoggingService.Info] 深度数据数量: 21
[2025-07-01 15:46:24.193] [INFO] [1] [LoggingService.Info] 设置当前深度索引: 0
[2025-07-01 15:46:24.193] [INFO] [1] [LoggingService.Info] 调用UpdateCurrentDepthLabel...
[2025-07-01 15:46:24.194] [INFO] [1] [LoggingService.Info] 调用UpdatePieChart...
[2025-07-01 15:46:24.194] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:46:24.195] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:46:24.195] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:46:24.195] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:46:24.196] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:46:24.196] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:46:24.196] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:46:24.197] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:46:24.197] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:46:24.197] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:46:24.198] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:46:24.198] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:46:24.198] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:46:24.199] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:46:24.199] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:46:24.199] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:46:24.200] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:46:24.200] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:46:24.200] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:46:24.201] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:46:24.201] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:46:24.201] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:46:24.202] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:46:24.202] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column42 -> Column42
[2025-07-01 15:46:24.202] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:46:24.203] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column43 -> Column43
[2025-07-01 15:46:24.203] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:46:24.203] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column44 -> Column44
[2025-07-01 15:46:24.204] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:46:24.204] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column45 -> Column45
[2025-07-01 15:46:24.204] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:46:24.205] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column46 -> Column46
[2025-07-01 15:46:24.205] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:46:24.205] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:46:24.206] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:46:24.206] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column41 -> Column41
[2025-07-01 15:46:24.206] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:46:24.207] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:46:24.207] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:46:24.207] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:46:24.208] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:46:24.208] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:46:24.208] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:46:24.209] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:46:24.209] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:46:24.210] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:46:24.210] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 22681099
[2025-07-01 15:46:24.210] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:46:24.211] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 22681099
[2025-07-01 15:46:24.266] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:46:24.266] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:46:24.267] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:46:24.267] [INFO] [1] [LoggingService.Info] UpdatePieChart完成
[2025-07-01 15:46:24.268] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 完成 =====
[2025-07-01 15:46:24.270] [INFO] [1] [LoggingService.Info] VisualizationForm.Show()调用完成
[2025-07-01 15:47:24.915] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-07-01 15:47:24.916] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=21, 列数=12
[2025-07-01 15:47:24.916] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:47:24.917] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:47:24.917] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:47:24.917] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:47:24.917] [INFO] [1] [LoggingService.Info] BrittlenessCalculator生成的脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:47:24.918] [INFO] [1] [LoggingService.Info] BrittlenessCalculator生成的塑性矿物列: Column41
[2025-07-01 15:47:24.918] [INFO] [1] [LoggingService.Info] 结果数据表中的所有列: GeoID, 顶深/m, 底深/m, Column42, Column43, Column44, Column45, Column46, Column41, 脆性指数, 脆性矿物总量, 塑性矿物总量
[2025-07-01 15:47:24.918] [INFO] [1] [LoggingService.Info] 验证后的脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:47:24.919] [INFO] [1] [LoggingService.Info] 验证后的塑性矿物列: Column41
[2025-07-01 15:47:24.919] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-07-01 15:47:24.919] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数开始 =====
[2025-07-01 15:47:24.920] [INFO] [1] [LoggingService.Info] 接收到数据: 行数=21, 脆性矿物=5, 塑性矿物=1
[2025-07-01 15:47:24.920] [INFO] [1] [LoggingService.Info] 脆性矿物列表: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:47:24.920] [INFO] [1] [LoggingService.Info] 塑性矿物列表: Column41
[2025-07-01 15:47:24.921] [INFO] [1] [LoggingService.Info] 调用InitializeComponent...
[2025-07-01 15:47:24.924] [INFO] [1] [LoggingService.Info] InitializeComponent完成
[2025-07-01 15:47:24.924] [INFO] [1] [LoggingService.Info] 调用InitializeDepthData...
[2025-07-01 15:47:24.924] [INFO] [1] [LoggingService.Info] InitializeDepthData完成
[2025-07-01 15:47:24.925] [INFO] [1] [LoggingService.Info] 调用InitializeControls...
[2025-07-01 15:47:24.925] [INFO] [1] [LoggingService.Info] ===== 初始化饼状图控件 =====
[2025-07-01 15:47:24.925] [INFO] [1] [LoggingService.Info] 深度滑动条设置: 范围0-20
[2025-07-01 15:47:24.926] [INFO] [1] [LoggingService.Info] 开始初始化chartPie控件
[2025-07-01 15:47:24.926] [INFO] [1] [LoggingService.Info] ChartArea已创建
[2025-07-01 15:47:24.926] [INFO] [1] [LoggingService.Info] Legend已创建
[2025-07-01 15:47:24.927] [INFO] [1] [LoggingService.Info] Title已创建
[2025-07-01 15:47:24.927] [INFO] [1] [LoggingService.Info] chartPie控件初始化完成
[2025-07-01 15:47:24.927] [INFO] [1] [LoggingService.Info] ===== 饼状图控件初始化完成 =====
[2025-07-01 15:47:24.928] [INFO] [1] [LoggingService.Info] ===== 初始化坐标轴控制面板 =====
[2025-07-01 15:47:24.928] [INFO] [1] [LoggingService.Info] X轴默认值设置完成
[2025-07-01 15:47:24.929] [INFO] [1] [LoggingService.Info] 坐标轴控制面板已初始化，但不会修改图表的坐标轴设置
[2025-07-01 15:47:24.929] [INFO] [1] [LoggingService.Info] 复选框状态: chkXAutoRange.Checked=False
[2025-07-01 15:47:24.931] [INFO] [1] [LoggingService.Info] 事件绑定完成
[2025-07-01 15:47:24.932] [INFO] [1] [LoggingService.Info] ===== 坐标轴控制面板初始化完成 =====
[2025-07-01 15:47:24.932] [INFO] [1] [LoggingService.Info] 已更新搜索下拉框，共 12 个列
[2025-07-01 15:47:24.933] [INFO] [1] [LoggingService.Info] InitializeControls完成
[2025-07-01 15:47:24.933] [INFO] [1] [LoggingService.Info] 调用LoadData...
[2025-07-01 15:47:24.933] [INFO] [1] [LoggingService.Info] ===== 开始加载数据 =====
[2025-07-01 15:47:24.933] [INFO] [1] [LoggingService.Info] 数据行数: 21
[2025-07-01 15:47:24.934] [INFO] [1] [LoggingService.Info] 深度数量: 21
[2025-07-01 15:47:24.943] [INFO] [1] [LoggingService.Info] 堆叠柱状图数据已设置
[2025-07-01 15:47:24.943] [INFO] [1] [LoggingService.Info] 深度滑动条最大值设置为: 20
[2025-07-01 15:47:24.944] [INFO] [1] [LoggingService.Info] 开始更新饼状图...
[2025-07-01 15:47:24.945] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:47:24.945] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:47:24.945] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:47:24.946] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:47:24.946] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:47:24.946] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:47:24.947] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:47:24.947] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:47:24.947] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:47:24.948] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:47:24.948] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:47:24.948] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:47:24.948] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:47:24.949] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:47:24.949] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:47:24.950] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:47:24.950] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:47:24.950] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:47:24.950] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:47:24.951] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:47:24.951] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:47:24.951] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:47:24.952] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:47:24.952] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column42 -> Column42
[2025-07-01 15:47:24.952] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:47:24.953] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column43 -> Column43
[2025-07-01 15:47:24.953] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:47:24.953] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column44 -> Column44
[2025-07-01 15:47:24.954] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:47:24.954] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column45 -> Column45
[2025-07-01 15:47:24.954] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:47:24.954] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column46 -> Column46
[2025-07-01 15:47:24.955] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:47:24.955] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:47:24.955] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:47:24.956] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column41 -> Column41
[2025-07-01 15:47:24.956] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:47:24.956] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:47:24.957] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:47:24.957] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:47:24.957] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:47:24.958] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:47:24.958] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:47:24.958] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:47:24.959] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:47:24.959] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:47:24.959] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 64605626
[2025-07-01 15:47:24.959] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:47:24.960] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 64605626
[2025-07-01 15:47:24.960] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:47:24.961] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:47:24.961] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:47:24.961] [INFO] [1] [LoggingService.Info] 饼状图更新完成
[2025-07-01 15:47:24.961] [INFO] [1] [LoggingService.Info] ===== 数据加载完成 =====
[2025-07-01 15:47:24.962] [INFO] [1] [LoggingService.Info] LoadData完成
[2025-07-01 15:47:24.962] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数完成 =====
[2025-07-01 15:47:24.962] [INFO] [1] [LoggingService.Info] VisualizationForm创建成功，准备显示窗口...
[2025-07-01 15:47:24.981] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 开始 =====
[2025-07-01 15:47:24.982] [INFO] [1] [LoggingService.Info] 深度数据数量: 21
[2025-07-01 15:47:24.982] [INFO] [1] [LoggingService.Info] 设置当前深度索引: 0
[2025-07-01 15:47:24.983] [INFO] [1] [LoggingService.Info] 调用UpdateCurrentDepthLabel...
[2025-07-01 15:47:24.983] [INFO] [1] [LoggingService.Info] 调用UpdatePieChart...
[2025-07-01 15:47:24.983] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:47:24.984] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:47:24.984] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:47:24.984] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:47:24.985] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:47:24.985] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:47:24.985] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:47:24.986] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:47:24.986] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:47:24.986] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:47:24.987] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:47:24.987] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:47:24.987] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:47:24.988] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:47:24.988] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:47:24.988] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:47:24.989] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:47:24.989] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:47:24.989] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:47:24.990] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:47:24.990] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:47:24.990] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:47:24.991] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:47:24.991] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column42 -> Column42
[2025-07-01 15:47:24.991] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:47:24.992] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column43 -> Column43
[2025-07-01 15:47:24.992] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:47:24.992] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column44 -> Column44
[2025-07-01 15:47:24.993] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:47:24.993] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column45 -> Column45
[2025-07-01 15:47:24.993] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:47:24.994] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column46 -> Column46
[2025-07-01 15:47:24.994] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:47:24.994] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:47:24.995] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:47:24.995] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column41 -> Column41
[2025-07-01 15:47:24.996] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:47:24.996] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:47:24.996] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:47:24.997] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:47:24.997] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:47:24.997] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:47:24.998] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:47:24.998] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:47:24.999] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:47:24.999] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:47:24.999] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 64605626
[2025-07-01 15:47:25.000] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:47:25.000] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 64605626
[2025-07-01 15:47:25.015] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:47:25.016] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:47:25.016] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:47:25.017] [INFO] [1] [LoggingService.Info] UpdatePieChart完成
[2025-07-01 15:47:25.017] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 完成 =====
[2025-07-01 15:47:25.020] [INFO] [1] [LoggingService.Info] VisualizationForm.Show()调用完成
[2025-07-01 15:47:38.071] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:47:38.072] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1486, Y=712
[2025-07-01 15:47:38.072] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:47:38.073] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:47:38.073] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:47:38.073] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:47:38.074] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:47:38.083] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:47:38.085] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:47:38.085] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:47:38.086] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:47:38.086] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:47:38.086] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-07-01 15:47:38.087] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:47:38.087] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 脆性矿物，开始分裂
[2025-07-01 15:47:38.090] [INFO] [1] [LoggingService.Info] ===== 开始分裂饼状图: 脆性矿物 =====
[2025-07-01 15:47:38.090] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=21
[2025-07-01 15:47:38.090] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:47:38.091] [INFO] [1] [LoggingService.Info] 找到数据行，开始分裂处理
[2025-07-01 15:47:38.091] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:47:38.091] [INFO] [1] [LoggingService.Info] 系列数据点数量: 2, 点击索引: 0
[2025-07-01 15:47:38.092] [INFO] [1] [LoggingService.Info] 原始点信息: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:47:38.092] [INFO] [1] [LoggingService.Info] 原始点信息已保存
[2025-07-01 15:47:38.092] [INFO] [1] [LoggingService.Info] 要分裂的矿物类型: 脆性矿物, 矿物列表数量: 5
[2025-07-01 15:47:38.093] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column42
[2025-07-01 15:47:38.093] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column43
[2025-07-01 15:47:38.093] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column44
[2025-07-01 15:47:38.094] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column45
[2025-07-01 15:47:38.094] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column46
[2025-07-01 15:47:38.094] [INFO] [1] [LoggingService.Info] 检查矿物: Column42
[2025-07-01 15:47:38.095] [INFO] [1] [LoggingService.Info] 数据表包含列: Column42
[2025-07-01 15:47:38.095] [INFO] [1] [LoggingService.Info] 矿物 Column42 的值: 35.01
[2025-07-01 15:47:38.096] [INFO] [1] [LoggingService.Info] 添加子矿物: Column42 = 35.01, 类别总值: 35.01
[2025-07-01 15:47:38.096] [INFO] [1] [LoggingService.Info] 检查矿物: Column43
[2025-07-01 15:47:38.096] [INFO] [1] [LoggingService.Info] 数据表包含列: Column43
[2025-07-01 15:47:38.097] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值: 0
[2025-07-01 15:47:38.097] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值为0，跳过
[2025-07-01 15:47:38.097] [INFO] [1] [LoggingService.Info] 检查矿物: Column44
[2025-07-01 15:47:38.097] [INFO] [1] [LoggingService.Info] 数据表包含列: Column44
[2025-07-01 15:47:38.098] [INFO] [1] [LoggingService.Info] 矿物 Column44 的值: 19.25
[2025-07-01 15:47:38.098] [INFO] [1] [LoggingService.Info] 添加子矿物: Column44 = 19.25, 类别总值: 54.26
[2025-07-01 15:47:38.098] [INFO] [1] [LoggingService.Info] 检查矿物: Column45
[2025-07-01 15:47:38.099] [INFO] [1] [LoggingService.Info] 数据表包含列: Column45
[2025-07-01 15:47:38.099] [INFO] [1] [LoggingService.Info] 矿物 Column45 的值: 5.96
[2025-07-01 15:47:38.099] [INFO] [1] [LoggingService.Info] 添加子矿物: Column45 = 5.96, 类别总值: 60.22
[2025-07-01 15:47:38.100] [INFO] [1] [LoggingService.Info] 检查矿物: Column46
[2025-07-01 15:47:38.100] [INFO] [1] [LoggingService.Info] 数据表包含列: Column46
[2025-07-01 15:47:38.100] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值: 0
[2025-07-01 15:47:38.100] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值为0，跳过
[2025-07-01 15:47:38.101] [INFO] [1] [LoggingService.Info] 子矿物总数: 3, 类别总值: 60.22
[2025-07-01 15:47:38.101] [INFO] [1] [LoggingService.Info] 调整因子: 1 (原始值: 60.22, 类别总值: 60.22)
[2025-07-01 15:47:38.101] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column42 从 35.0% 调整为 35.0%
[2025-07-01 15:47:38.102] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column44 从 19.2% 调整为 19.2%
[2025-07-01 15:47:38.102] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column45 从 6.0% 调整为 6.0%
[2025-07-01 15:47:38.102] [INFO] [1] [LoggingService.Info] 分裂饼状图扇形: 脆性矿物
[2025-07-01 15:47:38.103] [INFO] [1] [LoggingService.Info] 插入子矿物: Column42, 调整后值: 35.0%, 在类别中占比: 58.1%
[2025-07-01 15:47:38.103] [INFO] [1] [LoggingService.Info] 插入子矿物: Column44, 调整后值: 19.2%, 在类别中占比: 32.0%
[2025-07-01 15:47:38.104] [INFO] [1] [LoggingService.Info] 插入子矿物: Column45, 调整后值: 6.0%, 在类别中占比: 9.9%
[2025-07-01 15:47:38.104] [INFO] [1] [LoggingService.Info] 设置分裂状态
[2025-07-01 15:47:38.104] [INFO] [1] [LoggingService.Info] 强制重绘图表
[2025-07-01 15:47:38.105] [INFO] [1] [LoggingService.Info] ===== 饼状图分裂完成: 脆性矿物 =====
[2025-07-01 15:47:38.105] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:47:53.033] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统关闭
[2025-07-01 15:58:28.990] [INFO] [1] [LoggingService.Info] 日志服务已初始化
[2025-07-01 15:58:28.999] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统启动
[2025-07-01 15:59:10.663] [INFO] [1] [LoggingService.Info] ===== 可视化按钮被点击 =====
[2025-07-01 15:59:10.664] [INFO] [1] [LoggingService.Info] 结果数据检查通过: 行数=21, 列数=12
[2025-07-01 15:59:10.664] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:59:10.665] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:59:10.665] [INFO] [1] [LoggingService.Info] 原始脆性矿物列: Column42: 石英%, Column43: 白云石%, Column44: 菱铁矿%, Column45: 斜长石%, Column46: 钾长石（正长石）%
[2025-07-01 15:59:10.665] [INFO] [1] [LoggingService.Info] 原始塑性矿物列: Column41: 黏土矿物总量%
[2025-07-01 15:59:10.666] [INFO] [1] [LoggingService.Info] BrittlenessCalculator生成的脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:59:10.666] [INFO] [1] [LoggingService.Info] BrittlenessCalculator生成的塑性矿物列: Column41
[2025-07-01 15:59:10.668] [INFO] [1] [LoggingService.Info] 结果数据表中的所有列: GeoID, 顶深/m, 底深/m, Column42, Column43, Column44, Column45, Column46, Column41, 脆性指数, 脆性矿物总量, 塑性矿物总量
[2025-07-01 15:59:10.668] [INFO] [1] [LoggingService.Info] 验证后的脆性矿物列: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:59:10.669] [INFO] [1] [LoggingService.Info] 验证后的塑性矿物列: Column41
[2025-07-01 15:59:10.669] [INFO] [1] [LoggingService.Info] 开始创建VisualizationForm...
[2025-07-01 15:59:10.671] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数开始 =====
[2025-07-01 15:59:10.671] [INFO] [1] [LoggingService.Info] 接收到数据: 行数=21, 脆性矿物=5, 塑性矿物=1
[2025-07-01 15:59:10.672] [INFO] [1] [LoggingService.Info] 脆性矿物列表: Column42, Column43, Column44, Column45, Column46
[2025-07-01 15:59:10.672] [INFO] [1] [LoggingService.Info] 塑性矿物列表: Column41
[2025-07-01 15:59:10.673] [INFO] [1] [LoggingService.Info] 调用InitializeComponent...
[2025-07-01 15:59:10.701] [INFO] [1] [LoggingService.Info] InitializeComponent完成
[2025-07-01 15:59:10.702] [INFO] [1] [LoggingService.Info] 调用InitializeDepthData...
[2025-07-01 15:59:10.710] [INFO] [1] [LoggingService.Info] InitializeDepthData完成
[2025-07-01 15:59:10.710] [INFO] [1] [LoggingService.Info] 调用InitializeControls...
[2025-07-01 15:59:10.712] [INFO] [1] [LoggingService.Info] ===== 初始化饼状图控件 =====
[2025-07-01 15:59:10.712] [INFO] [1] [LoggingService.Info] 深度滑动条设置: 范围0-20
[2025-07-01 15:59:10.712] [INFO] [1] [LoggingService.Info] 开始初始化chartPie控件
[2025-07-01 15:59:10.713] [INFO] [1] [LoggingService.Info] ChartArea已创建
[2025-07-01 15:59:10.713] [INFO] [1] [LoggingService.Info] Legend已创建
[2025-07-01 15:59:10.714] [INFO] [1] [LoggingService.Info] Title已创建
[2025-07-01 15:59:10.714] [INFO] [1] [LoggingService.Info] chartPie控件初始化完成
[2025-07-01 15:59:10.715] [INFO] [1] [LoggingService.Info] ===== 饼状图控件初始化完成 =====
[2025-07-01 15:59:10.718] [INFO] [1] [LoggingService.Info] ===== 初始化坐标轴控制面板 =====
[2025-07-01 15:59:10.719] [INFO] [1] [LoggingService.Info] X轴默认值设置完成
[2025-07-01 15:59:10.719] [INFO] [1] [LoggingService.Info] 坐标轴控制面板已初始化，但不会修改图表的坐标轴设置
[2025-07-01 15:59:10.720] [INFO] [1] [LoggingService.Info] 复选框状态: chkXAutoRange.Checked=False
[2025-07-01 15:59:10.720] [INFO] [1] [LoggingService.Info] 事件绑定完成
[2025-07-01 15:59:10.721] [INFO] [1] [LoggingService.Info] ===== 坐标轴控制面板初始化完成 =====
[2025-07-01 15:59:10.721] [INFO] [1] [LoggingService.Info] 已更新搜索下拉框，共 12 个列
[2025-07-01 15:59:10.722] [INFO] [1] [LoggingService.Info] InitializeControls完成
[2025-07-01 15:59:10.722] [INFO] [1] [LoggingService.Info] 调用LoadData...
[2025-07-01 15:59:10.723] [INFO] [1] [LoggingService.Info] ===== 开始加载数据 =====
[2025-07-01 15:59:10.723] [INFO] [1] [LoggingService.Info] 数据行数: 21
[2025-07-01 15:59:10.724] [INFO] [1] [LoggingService.Info] 深度数量: 21
[2025-07-01 15:59:10.749] [INFO] [1] [LoggingService.Info] 堆叠柱状图数据已设置
[2025-07-01 15:59:10.750] [INFO] [1] [LoggingService.Info] 深度滑动条最大值设置为: 20
[2025-07-01 15:59:10.751] [INFO] [1] [LoggingService.Info] 开始更新饼状图...
[2025-07-01 15:59:10.752] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:59:10.753] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:59:10.753] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:59:10.753] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:59:10.754] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:59:10.754] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:59:10.754] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:59:10.755] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:59:10.756] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:59:10.757] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:59:10.757] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:59:10.757] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:59:10.758] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:59:10.758] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:59:10.758] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:59:10.759] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:59:10.759] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:59:10.759] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:59:10.760] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:59:10.760] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:59:10.760] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:59:10.761] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:59:10.761] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:59:10.762] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column42 -> Column42
[2025-07-01 15:59:10.762] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:59:10.762] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column43 -> Column43
[2025-07-01 15:59:10.762] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:59:10.763] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column44 -> Column44
[2025-07-01 15:59:10.763] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:59:10.763] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column45 -> Column45
[2025-07-01 15:59:10.764] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:59:10.764] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column46 -> Column46
[2025-07-01 15:59:10.764] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:59:10.765] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:59:10.765] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:59:10.765] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column41 -> Column41
[2025-07-01 15:59:10.766] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:59:10.766] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:59:10.766] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:59:10.767] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:59:10.767] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:59:10.768] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:59:10.768] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:59:10.768] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:59:10.769] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:59:10.769] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:59:10.770] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 62417850
[2025-07-01 15:59:10.770] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:59:10.770] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 62417850
[2025-07-01 15:59:10.771] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:59:10.771] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:59:10.771] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:59:10.771] [INFO] [1] [LoggingService.Info] 饼状图更新完成
[2025-07-01 15:59:10.772] [INFO] [1] [LoggingService.Info] ===== 数据加载完成 =====
[2025-07-01 15:59:10.772] [INFO] [1] [LoggingService.Info] LoadData完成
[2025-07-01 15:59:10.772] [INFO] [1] [LoggingService.Info] ===== VisualizationForm 构造函数完成 =====
[2025-07-01 15:59:10.773] [INFO] [1] [LoggingService.Info] VisualizationForm创建成功，准备显示窗口...
[2025-07-01 15:59:10.795] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 开始 =====
[2025-07-01 15:59:10.795] [INFO] [1] [LoggingService.Info] 深度数据数量: 21
[2025-07-01 15:59:10.796] [INFO] [1] [LoggingService.Info] 设置当前深度索引: 0
[2025-07-01 15:59:10.796] [INFO] [1] [LoggingService.Info] 调用UpdateCurrentDepthLabel...
[2025-07-01 15:59:10.796] [INFO] [1] [LoggingService.Info] 调用UpdatePieChart...
[2025-07-01 15:59:10.797] [INFO] [1] [LoggingService.Info] ===== 开始更新饼状图 =====
[2025-07-01 15:59:10.797] [INFO] [1] [LoggingService.Info] chartPie已初始化
[2025-07-01 15:59:10.797] [INFO] [1] [LoggingService.Info] 当前深度索引: 0
[2025-07-01 15:59:10.798] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:59:10.798] [INFO] [1] [LoggingService.Info] 结果数据行数: 21
[2025-07-01 15:59:10.798] [INFO] [1] [LoggingService.Info] 脆性矿物数量: 5, 塑性矿物数量: 1
[2025-07-01 15:59:10.799] [INFO] [1] [LoggingService.Info] 找到匹配的数据行，深度: 705.46m
[2025-07-01 15:59:10.799] [INFO] [1] [LoggingService.Info] 创建基本饼状图（脆性/塑性矿物比例）
[2025-07-01 15:59:10.800] [INFO] [1] [LoggingService.Info] ===== 开始创建基本饼状图 =====
[2025-07-01 15:59:10.800] [INFO] [1] [LoggingService.Info] 数据表列名:
[2025-07-01 15:59:10.800] [INFO] [1] [LoggingService.Info]   列名: 'GeoID'
[2025-07-01 15:59:10.800] [INFO] [1] [LoggingService.Info]   列名: '顶深/m'
[2025-07-01 15:59:10.801] [INFO] [1] [LoggingService.Info]   列名: '底深/m'
[2025-07-01 15:59:10.801] [INFO] [1] [LoggingService.Info]   列名: 'Column42'
[2025-07-01 15:59:10.802] [INFO] [1] [LoggingService.Info]   列名: 'Column43'
[2025-07-01 15:59:10.802] [INFO] [1] [LoggingService.Info]   列名: 'Column44'
[2025-07-01 15:59:10.802] [INFO] [1] [LoggingService.Info]   列名: 'Column45'
[2025-07-01 15:59:10.803] [INFO] [1] [LoggingService.Info]   列名: 'Column46'
[2025-07-01 15:59:10.803] [INFO] [1] [LoggingService.Info]   列名: 'Column41'
[2025-07-01 15:59:10.803] [INFO] [1] [LoggingService.Info]   列名: '脆性指数'
[2025-07-01 15:59:10.804] [INFO] [1] [LoggingService.Info]   列名: '脆性矿物总量'
[2025-07-01 15:59:10.804] [INFO] [1] [LoggingService.Info]   列名: '塑性矿物总量'
[2025-07-01 15:59:10.804] [INFO] [1] [LoggingService.Info] 计算脆性矿物总量:
[2025-07-01 15:59:10.805] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column42 -> Column42
[2025-07-01 15:59:10.805] [INFO] [1] [LoggingService.Info]   Column42 (列名: Column42): 35.01%
[2025-07-01 15:59:10.805] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column43 -> Column43
[2025-07-01 15:59:10.806] [INFO] [1] [LoggingService.Info]   Column43 (列名: Column43): 0%
[2025-07-01 15:59:10.806] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column44 -> Column44
[2025-07-01 15:59:10.806] [INFO] [1] [LoggingService.Info]   Column44 (列名: Column44): 19.25%
[2025-07-01 15:59:10.807] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column45 -> Column45
[2025-07-01 15:59:10.807] [INFO] [1] [LoggingService.Info]   Column45 (列名: Column45): 5.96%
[2025-07-01 15:59:10.807] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column46 -> Column46
[2025-07-01 15:59:10.808] [INFO] [1] [LoggingService.Info]   Column46 (列名: Column46): 0%
[2025-07-01 15:59:10.808] [INFO] [1] [LoggingService.Info] 脆性矿物总量: 60.22%
[2025-07-01 15:59:10.808] [INFO] [1] [LoggingService.Info] 计算塑性矿物总量:
[2025-07-01 15:59:10.809] [INFO] [1] [LoggingService.Info] 找到精确匹配: Column41 -> Column41
[2025-07-01 15:59:10.809] [INFO] [1] [LoggingService.Info]   Column41 (列名: Column41): 38.47%
[2025-07-01 15:59:10.809] [INFO] [1] [LoggingService.Info] 塑性矿物总量: 38.47%
[2025-07-01 15:59:10.810] [INFO] [1] [LoggingService.Info] 创建饼状图系列...
[2025-07-01 15:59:10.810] [INFO] [1] [LoggingService.Info] 饼状图系列已创建
[2025-07-01 15:59:10.810] [INFO] [1] [LoggingService.Info] 添加脆性矿物数据点: 60.2%, 颜色: Color [Blue]
[2025-07-01 15:59:10.811] [INFO] [1] [LoggingService.Info] 添加塑性矿物数据点: 38.5%, 颜色: Color [Green]
[2025-07-01 15:59:10.811] [INFO] [1] [LoggingService.Info] 总共添加了 2 个数据点
[2025-07-01 15:59:10.811] [INFO] [1] [LoggingService.Info] 饼状图系列已添加到图表
[2025-07-01 15:59:10.812] [INFO] [1] [LoggingService.Info] ===== 基本饼状图创建完成 =====
[2025-07-01 15:59:10.813] [INFO] [1] [LoggingService.Info] 已设置饼状图图例
[2025-07-01 15:59:10.813] [INFO] [1] [LoggingService.Info] 绑定鼠标事件前 - chartPie对象: 62417850
[2025-07-01 15:59:10.813] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标事件已绑定 =====
[2025-07-01 15:59:10.814] [INFO] [1] [LoggingService.Info] 绑定鼠标事件后 - chartPie对象: 62417850
[2025-07-01 15:59:10.867] [INFO] [1] [LoggingService.Info] 饼状图系列数量: 1
[2025-07-01 15:59:10.868] [INFO] [1] [LoggingService.Info] 第一个系列数据点数量: 2
[2025-07-01 15:59:10.869] [INFO] [1] [LoggingService.Info] ===== 饼状图更新完成 =====
[2025-07-01 15:59:10.869] [INFO] [1] [LoggingService.Info] UpdatePieChart完成
[2025-07-01 15:59:10.869] [INFO] [1] [LoggingService.Info] ===== VisualizationForm_Load 完成 =====
[2025-07-01 15:59:10.873] [INFO] [1] [LoggingService.Info] VisualizationForm.Show()调用完成
[2025-07-01 15:59:11.772] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:59:11.773] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1380, Y=753
[2025-07-01 15:59:11.773] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:59:11.773] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:59:11.774] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:59:11.774] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:59:11.775] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:59:11.782] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:59:11.784] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:59:11.784] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:59:11.784] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:59:11.785] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:59:11.785] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=False, _isDispersed=False
[2025-07-01 15:59:11.785] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:59:11.786] [INFO] [1] [LoggingService.Info] 点击了基本矿物类型: 脆性矿物，开始分裂
[2025-07-01 15:59:11.788] [INFO] [1] [LoggingService.Info] ===== 开始分裂饼状图: 脆性矿物 =====
[2025-07-01 15:59:11.789] [INFO] [1] [LoggingService.Info] 深度检查: _currentDepthIndex=0, _depths.Count=21
[2025-07-01 15:59:11.789] [INFO] [1] [LoggingService.Info] 当前深度: 705.46m
[2025-07-01 15:59:11.789] [INFO] [1] [LoggingService.Info] 找到数据行，开始分裂处理
[2025-07-01 15:59:11.790] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:59:11.790] [INFO] [1] [LoggingService.Info] 系列数据点数量: 2, 点击索引: 0
[2025-07-01 15:59:11.791] [INFO] [1] [LoggingService.Info] 原始点信息: Tag=脆性矿物, LegendText=脆性矿物, Value=60.22
[2025-07-01 15:59:11.791] [INFO] [1] [LoggingService.Info] 原始点信息已保存
[2025-07-01 15:59:11.791] [INFO] [1] [LoggingService.Info] 要分裂的矿物类型: 脆性矿物, 矿物列表数量: 5
[2025-07-01 15:59:11.792] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column42
[2025-07-01 15:59:11.792] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column43
[2025-07-01 15:59:11.792] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column44
[2025-07-01 15:59:11.793] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column45
[2025-07-01 15:59:11.793] [INFO] [1] [LoggingService.Info] 矿物列表包含: Column46
[2025-07-01 15:59:11.794] [INFO] [1] [LoggingService.Info] 检查矿物: Column42
[2025-07-01 15:59:11.794] [INFO] [1] [LoggingService.Info] 数据表包含列: Column42
[2025-07-01 15:59:11.794] [INFO] [1] [LoggingService.Info] 矿物 Column42 的值: 35.01
[2025-07-01 15:59:11.795] [INFO] [1] [LoggingService.Info] 添加子矿物: Column42 = 35.01, 类别总值: 35.01
[2025-07-01 15:59:11.795] [INFO] [1] [LoggingService.Info] 检查矿物: Column43
[2025-07-01 15:59:11.796] [INFO] [1] [LoggingService.Info] 数据表包含列: Column43
[2025-07-01 15:59:11.796] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值: 0
[2025-07-01 15:59:11.797] [INFO] [1] [LoggingService.Info] 矿物 Column43 的值为0，跳过
[2025-07-01 15:59:11.797] [INFO] [1] [LoggingService.Info] 检查矿物: Column44
[2025-07-01 15:59:11.797] [INFO] [1] [LoggingService.Info] 数据表包含列: Column44
[2025-07-01 15:59:11.798] [INFO] [1] [LoggingService.Info] 矿物 Column44 的值: 19.25
[2025-07-01 15:59:11.798] [INFO] [1] [LoggingService.Info] 添加子矿物: Column44 = 19.25, 类别总值: 54.26
[2025-07-01 15:59:11.798] [INFO] [1] [LoggingService.Info] 检查矿物: Column45
[2025-07-01 15:59:11.799] [INFO] [1] [LoggingService.Info] 数据表包含列: Column45
[2025-07-01 15:59:11.799] [INFO] [1] [LoggingService.Info] 矿物 Column45 的值: 5.96
[2025-07-01 15:59:11.799] [INFO] [1] [LoggingService.Info] 添加子矿物: Column45 = 5.96, 类别总值: 60.22
[2025-07-01 15:59:11.800] [INFO] [1] [LoggingService.Info] 检查矿物: Column46
[2025-07-01 15:59:11.800] [INFO] [1] [LoggingService.Info] 数据表包含列: Column46
[2025-07-01 15:59:11.800] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值: 0
[2025-07-01 15:59:11.801] [INFO] [1] [LoggingService.Info] 矿物 Column46 的值为0，跳过
[2025-07-01 15:59:11.801] [INFO] [1] [LoggingService.Info] 子矿物总数: 3, 类别总值: 60.22
[2025-07-01 15:59:11.801] [INFO] [1] [LoggingService.Info] 调整因子: 1 (原始值: 60.22, 类别总值: 60.22)
[2025-07-01 15:59:11.802] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column42 从 35.0% 调整为 35.0%
[2025-07-01 15:59:11.802] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column44 从 19.2% 调整为 19.2%
[2025-07-01 15:59:11.802] [INFO] [1] [LoggingService.Info] 调整子矿物值: Column45 从 6.0% 调整为 6.0%
[2025-07-01 15:59:11.803] [INFO] [1] [LoggingService.Info] 分裂饼状图扇形: 脆性矿物
[2025-07-01 15:59:11.803] [INFO] [1] [LoggingService.Info] 插入子矿物: Column42, 调整后值: 35.0%, 在类别中占比: 58.1%
[2025-07-01 15:59:11.804] [INFO] [1] [LoggingService.Info] 插入子矿物: Column44, 调整后值: 19.2%, 在类别中占比: 32.0%
[2025-07-01 15:59:11.804] [INFO] [1] [LoggingService.Info] 插入子矿物: Column45, 调整后值: 6.0%, 在类别中占比: 9.9%
[2025-07-01 15:59:11.804] [INFO] [1] [LoggingService.Info] 设置分裂状态
[2025-07-01 15:59:11.805] [INFO] [1] [LoggingService.Info] 强制重绘图表
[2025-07-01 15:59:11.805] [INFO] [1] [LoggingService.Info] ===== 饼状图分裂完成: 脆性矿物 =====
[2025-07-01 15:59:11.805] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:59:12.724] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件开始 =====
[2025-07-01 15:59:12.724] [INFO] [1] [LoggingService.Info] 鼠标位置: X=1430, Y=676
[2025-07-01 15:59:12.725] [INFO] [1] [LoggingService.Info] 鼠标按键: Left
[2025-07-01 15:59:12.725] [INFO] [1] [LoggingService.Info] 图表控件状态: Enabled=True, Visible=True
[2025-07-01 15:59:12.726] [INFO] [1] [LoggingService.Info] 图表系列数量: 1
[2025-07-01 15:59:12.726] [INFO] [1] [LoggingService.Info] chartPie对象检查: chartPie=True
[2025-07-01 15:59:12.726] [INFO] [1] [LoggingService.Info] 准备执行HitTest...
[2025-07-01 15:59:12.737] [INFO] [1] [LoggingService.Info] HitTest执行完成
[2025-07-01 15:59:12.738] [INFO] [1] [LoggingService.Info] HitTest结果: ChartElementType=DataPoint
[2025-07-01 15:59:12.738] [INFO] [1] [LoggingService.Info] HitTest结果: PointIndex=0
[2025-07-01 15:59:12.738] [INFO] [1] [LoggingService.Info] HitTest结果: Series=MineralRatio
[2025-07-01 15:59:12.739] [INFO] [1] [LoggingService.Info] 点击的数据点: Tag=脆性矿物_split_Column42, LegendText=Column42, Value=35.01
[2025-07-01 15:59:12.739] [INFO] [1] [LoggingService.Info] 当前状态: _isExpanded=True, _isDispersed=False
[2025-07-01 15:59:12.739] [INFO] [1] [LoggingService.Info] 处理左键点击
[2025-07-01 15:59:12.740] [INFO] [1] [LoggingService.Info] 点击了分裂后的子矿物: 脆性矿物_split_Column42，开始还原
[2025-07-01 15:59:12.741] [INFO] [1] [LoggingService.Info] ===== 饼状图鼠标点击事件结束 =====
[2025-07-01 15:59:32.304] [INFO] [1] [LoggingService.Info] 矿物组分法脆性指数分析系统关闭
