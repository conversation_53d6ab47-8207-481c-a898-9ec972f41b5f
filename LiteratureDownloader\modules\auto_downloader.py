#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化文献下载器
自动打开Web of Science，搜索文献，下载PDF
"""

import os
import time
import re
from pathlib import Path
from typing import List, Dict, Optional
from loguru import logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
except ImportError as e:
    logger.error(f"自动下载器导入失败: {e}")
    raise

from config.settings import WOS_CONFIG, NETWORK_CONFIG


class AutoDownloader:
    """自动化文献下载器"""
    
    def __init__(self, download_dir: str):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        self.config = WOS_CONFIG
        self.network_config = NETWORK_CONFIG
        self.drivers = []  # 存储多个浏览器实例
        self.max_windows = 5  # 最大同时打开的窗口数
        
    def setup_driver(self) -> webdriver.Chrome:
        """设置单个WebDriver实例"""
        try:
            chrome_options = Options()

            # 设置下载目录 - 专门用于EndNote文件
            prefs = {
                "download.default_directory": str(self.download_dir),
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 基本配置，不使用用户配置避免冲突
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1200,800')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 尝试使用webdriver-manager
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                from selenium.webdriver.chrome.service import Service

                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)

            except ImportError:
                driver = webdriver.Chrome(options=chrome_options)

            # 隐藏自动化特征
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("WebDriver实例创建成功")
            return driver

        except Exception as e:
            logger.error(f"WebDriver创建失败: {e}")
            return None
    
    def search_and_download_citation(self, driver: webdriver.Chrome, citation: Dict[str, str]) -> bool:
        """搜索并下载单个文献的EndNote引用"""
        try:
            ref_num = citation.get('reference_number', '?')
            title = citation.get('title', '').strip()
            authors = citation.get('authors', '')
            year = citation.get('year', '').strip()

            # 检查是否有足够的信息
            if (not title or title == '未识别') and (not authors or authors == '未识别'):
                logger.warning(f"跳过文献 [{ref_num}]: 标题和作者都未识别")
                return False

            logger.info(f"开始搜索文献 [{ref_num}]: {title[:50]}...")

            # 打开Web of Science
            driver.get("https://webofscience.clarivate.cn/wos/alldb/smart-search")
            time.sleep(5)

            # 查找搜索框 - 使用更通用的选择器
            search_selectors = [
                'input[data-ta="search-input"]',
                'input[placeholder*="搜索"]',
                'input[placeholder*="Search"]',
                'textarea[data-ta="search-input"]',
                '#search-input',
                '.search-input',
                'input[type="text"]'
            ]

            search_box = None
            for selector in search_selectors:
                try:
                    search_box = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not search_box:
                logger.error(f"文献 [{ref_num}] 找不到搜索框")
                return False

            # 构建搜索查询
            query_parts = []
            if title and title != '未识别' and len(title) > 10:
                # 使用标题搜索，去掉特殊字符
                clean_title = re.sub(r'[^\w\s]', ' ', title).strip()
                query_parts.append(f'TI="{clean_title}"')
            elif authors and authors != '未识别':
                if isinstance(authors, list):
                    authors = ', '.join(authors[:2])
                clean_authors = re.sub(r'[^\w\s,.]', ' ', str(authors)).strip()
                query_parts.append(f'AU="{clean_authors}"')

            if year and year != '未识别' and year.isdigit():
                query_parts.append(f'PY={year}')

            search_query = ' AND '.join(query_parts)
            logger.info(f"搜索查询: {search_query}")

            # 输入搜索查询
            search_box.clear()
            search_box.send_keys(search_query)
            time.sleep(2)

            # 执行搜索
            search_box.send_keys(Keys.RETURN)
            time.sleep(8)  # 等待搜索结果加载

            # 查找搜索结果并点击第一个结果
            try:
                # 查找结果标题链接
                result_selectors = [
                    'a[data-ta="result-title-link"]',
                    '.search-results-item a',
                    '.record-title a',
                    '.result-item a',
                    'h3 a',
                    '.title a'
                ]

                first_result = None
                for selector in result_selectors:
                    try:
                        results = driver.find_elements(By.CSS_SELECTOR, selector)
                        if results:
                            first_result = results[0]
                            break
                    except:
                        continue

                if not first_result:
                    logger.warning(f"文献 [{ref_num}] 未找到搜索结果")
                    return False

                logger.info(f"文献 [{ref_num}] 找到搜索结果，点击进入详情页")

                # 点击进入详情页
                driver.execute_script("arguments[0].click();", first_result)
                time.sleep(5)

                # 查找并点击EndNote Desktop下载
                return self.download_endnote_citation(driver, ref_num)

            except Exception as e:
                logger.warning(f"文献 [{ref_num}] 搜索结果处理失败: {e}")
                return False

        except Exception as e:
            logger.error(f"搜索文献失败: {e}")
            return False
    
    def download_endnote_citation(self, driver: webdriver.Chrome, ref_num: str) -> bool:
        """下载EndNote引用信息"""
        try:
            logger.info(f"查找文献 [{ref_num}] 的EndNote下载选项")

            # 查找导出/下载按钮
            export_selectors = [
                'button[data-ta="export-button"]',
                '.export-button',
                'button[title*="导出"]',
                'button[title*="Export"]',
                'a[title*="导出"]',
                'a[title*="Export"]',
                '.export',
                '[data-ta="export"]'
            ]

            export_button = None
            for selector in export_selectors:
                try:
                    export_button = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not export_button:
                # 尝试查找包含"导出"或"Export"文本的按钮
                try:
                    export_button = driver.find_element(By.XPATH,
                        "//button[contains(text(), '导出') or contains(text(), 'Export') or contains(text(), '出版')]")
                except:
                    try:
                        export_button = driver.find_element(By.XPATH,
                            "//a[contains(text(), '导出') or contains(text(), 'Export')]")
                    except:
                        pass

            if not export_button:
                logger.warning(f"文献 [{ref_num}] 找不到导出按钮")
                return False

            # 点击导出按钮
            logger.info(f"文献 [{ref_num}] 点击导出按钮")
            driver.execute_script("arguments[0].click();", export_button)
            time.sleep(3)

            # 查找EndNote Desktop选项
            endnote_selectors = [
                'a[title*="EndNote Desktop"]',
                'button[title*="EndNote Desktop"]',
                'a[data-value="endnote"]',
                'button[data-value="endnote"]',
                '.endnote-desktop',
                '[data-format="endnote"]'
            ]

            endnote_option = None
            for selector in endnote_selectors:
                try:
                    endnote_option = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not endnote_option:
                # 尝试查找包含"EndNote"文本的选项
                try:
                    endnote_option = driver.find_element(By.XPATH,
                        "//a[contains(text(), 'EndNote') or contains(@title, 'EndNote')]")
                except:
                    try:
                        endnote_option = driver.find_element(By.XPATH,
                            "//button[contains(text(), 'EndNote') or contains(@title, 'EndNote')]")
                    except:
                        pass

            if not endnote_option:
                logger.warning(f"文献 [{ref_num}] 找不到EndNote Desktop选项")
                return False

            # 点击EndNote Desktop选项
            logger.info(f"文献 [{ref_num}] 点击EndNote Desktop下载")
            driver.execute_script("arguments[0].click();", endnote_option)
            time.sleep(3)

            # 检查是否有下载确认按钮
            try:
                download_button = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH,
                        "//button[contains(text(), '下载') or contains(text(), 'Download')]"))
                )
                driver.execute_script("arguments[0].click();", download_button)
                logger.info(f"文献 [{ref_num}] 点击下载确认")
                time.sleep(2)
            except:
                pass  # 可能直接下载，无需确认

            logger.info(f"文献 [{ref_num}] EndNote引用下载完成")
            return True

        except Exception as e:
            logger.error(f"下载EndNote引用失败: {e}")
            return False
    
    def batch_download(self, citations: List[Dict[str, str]]) -> Dict[str, int]:
        """批量下载文献"""
        results = {
            'total': len(citations),
            'processed': 0,
            'downloaded': 0,
            'skipped': 0,
            'failed': 0
        }
        
        # 过滤掉信息不足的文献
        valid_citations = []
        for citation in citations:
            title = citation.get('title', '').strip()
            authors = citation.get('authors', '')
            
            if (title and title != '未识别') or (authors and authors != '未识别'):
                valid_citations.append(citation)
            else:
                results['skipped'] += 1
                ref_num = citation.get('reference_number', '?')
                logger.info(f"跳过文献 [{ref_num}]: 信息不足")
        
        if not valid_citations:
            logger.warning("没有有效的文献可以下载")
            return results
        
        logger.info(f"开始批量下载，有效文献: {len(valid_citations)} 个")
        
        # 分批处理，每批最多5个
        batch_size = min(self.max_windows, len(valid_citations))
        
        for i in range(0, len(valid_citations), batch_size):
            batch = valid_citations[i:i + batch_size]
            logger.info(f"处理第 {i//batch_size + 1} 批，共 {len(batch)} 个文献")
            
            # 为这批文献创建WebDriver实例
            drivers = []
            for j in range(len(batch)):
                driver = self.setup_driver()
                if driver:
                    drivers.append(driver)
                else:
                    logger.error(f"无法创建第 {j+1} 个WebDriver实例")
            
            if not drivers:
                logger.error("无法创建任何WebDriver实例")
                break
            
            # 并行处理这批文献
            for j, citation in enumerate(batch):
                if j < len(drivers):
                    try:
                        success = self.search_and_download_citation(drivers[j], citation)
                        results['processed'] += 1
                        
                        if success:
                            results['downloaded'] += 1
                        else:
                            results['failed'] += 1
                            
                    except Exception as e:
                        logger.error(f"处理文献失败: {e}")
                        results['failed'] += 1
                        results['processed'] += 1
            
            # 等待下载完成
            time.sleep(10)
            
            # 关闭这批的WebDriver实例
            for driver in drivers:
                try:
                    driver.quit()
                except Exception as e:
                    logger.warning(f"关闭WebDriver失败: {e}")
            
            # 如果还有更多批次，询问用户是否继续
            if i + batch_size < len(valid_citations):
                remaining = len(valid_citations) - (i + batch_size)
                logger.info(f"已完成一批下载，还有 {remaining} 个文献待处理")
                time.sleep(5)  # 给用户一些时间查看结果
        
        logger.info(f"批量下载完成: 总计{results['total']}, 处理{results['processed']}, "
                   f"下载{results['downloaded']}, 跳过{results['skipped']}, 失败{results['failed']}")
        
        return results
    
    def close_all_drivers(self):
        """关闭所有WebDriver实例"""
        for driver in self.drivers:
            try:
                driver.quit()
            except Exception as e:
                logger.warning(f"关闭WebDriver失败: {e}")
        self.drivers.clear()
    
    def __del__(self):
        """析构函数"""
        self.close_all_drivers()
