# 文献识别下载自动化程序

## 功能特性

- **PDF文件识别**: 从PDF文件中识别文献引用，支持用户选择识别区域
- **截图识别**: 通过OCR技术从截图中提取文献引用信息
- **智能搜索**: 根据用户提供的网址自动搜索文献
- **自动下载**: 自动下载找到的文献文件
- **智能匹配**: 通过作者名或DOI/ISBN号验证文献准确性
- **自动重命名**: 根据文献标题自动重命名下载的文件
- **目录管理**: 按用户指定目录保存下载的文献

## 项目结构

```
LiteratureDownloader/
├── main.py                 # 主程序入口
├── gui/                    # 用户界面模块
│   ├── __init__.py
│   └── main_window.py      # 主窗口界面
├── modules/                # 核心功能模块
│   ├── __init__.py
│   ├── pdf_reader.py       # PDF文件识别模块
│   ├── ocr_reader.py       # 截图OCR识别模块
│   ├── citation_parser.py  # 文献信息解析模块
│   ├── web_searcher.py     # 网站搜索模块
│   ├── downloader.py       # 文件下载模块
│   ├── matcher.py          # 文献匹配验证模块
│   └── file_manager.py     # 文件管理和重命名模块
├── config/                 # 配置文件
│   ├── __init__.py
│   └── settings.py         # 程序设置
├── utils/                  # 工具函数
│   ├── __init__.py
│   └── helpers.py          # 辅助函数
├── tests/                  # 测试文件
│   └── __init__.py
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明
```

## 安装和使用

1. 安装依赖包：

   ```bash
   pip install -r requirements.txt
   ```

2. 运行程序：

   ```bash
   python main.py
   ```

## 依赖包

- tkinter: GUI界面
- PyPDF2/pdfplumber: PDF文件处理
- pytesseract: OCR文字识别
- Pillow: 图像处理
- requests: 网络请求
- beautifulsoup4: 网页解析
- selenium: 网页自动化（如需要）
- fuzzywuzzy: 字符串匹配
- python-Levenshtein: 字符串相似度计算

## 开发计划

- [x] 项目结构设计
- [x] PDF文件识别模块
- [x] 截图OCR识别模块
- [x] 文献信息解析模块
- [x] 网站搜索和下载模块
- [x] 文献匹配验证模块
- [x] 文件重命名管理模块
- [x] 用户界面开发
- [x] 测试和优化

## 使用说明

### 1. 环境准备

#### 安装Python依赖

```bash
pip install -r requirements.txt
```

#### 安装Tesseract OCR

- Windows: 下载并安装 [Tesseract OCR](https://github.com/UB-Mannheim/tesseract/wiki)
- 安装后在 `config/settings.py` 中配置正确的路径

### 2. 运行程序

```bash
python main.py
```

### 3. 功能使用

#### PDF文件识别

1. 点击"选择PDF文件"按钮选择要识别的PDF文件
2. 点击"识别引用"开始识别文献引用
3. 识别结果将显示在下方的表格中

#### 截图识别

1. 点击"选择截图文件"按钮选择包含文献引用的截图
2. 点击"识别引用"开始OCR识别
3. 识别结果将显示在表格中

#### 搜索和下载

1. 在识别结果中选择要搜索的文献
2. 设置搜索网址（默认为Google Scholar）
3. 点击"搜索选中文献"开始搜索
4. 在搜索结果中选择要下载的文献
5. 点击"下载选中文献"开始下载

#### 设置配置

1. 在"设置"选项卡中配置下载目录
2. 查看运行日志了解程序状态

### 4. 注意事项

- 确保网络连接正常，用于搜索和下载文献
- OCR识别效果取决于图像质量，建议使用清晰的截图
- 某些学术网站可能有访问限制，请遵守相关规定
- 下载的文献仅供学术研究使用

### 5. 故障排除

#### 常见问题

1. **OCR识别失败**: 检查Tesseract安装路径是否正确
2. **网络请求失败**: 检查网络连接和防火墙设置
3. **文件下载失败**: 检查下载目录权限和磁盘空间

#### 日志查看

程序运行日志保存在 `logs/` 目录中，可以查看详细的错误信息。

### 6. 测试

运行基本功能测试：

```bash
python tests/test_basic.py
```
