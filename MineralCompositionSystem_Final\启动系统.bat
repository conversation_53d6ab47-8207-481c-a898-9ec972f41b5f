@echo off
chcp 65001 >nul
echo ========================================
echo    矿物组分法脆性指数分析系统 V1.0
echo ========================================
echo.
echo 正在启动系统，请稍候...
echo.

REM 检查是否安装了.NET 8
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未检测到 .NET 8 运行时
    echo 请先安装 .NET 8 Desktop Runtime
    echo 下载地址：https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM 编译并运行项目
echo 正在编译项目...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo 编译失败，请检查项目文件
    pause
    exit /b 1
)

echo 启动矿物组分法脆性指数分析系统...
echo.
dotnet run --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo 系统运行出现错误
    pause
)
