#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理和重命名模块
功能：
1. 根据文献信息自动重命名文件
2. 组织文件到指定目录
3. 避免文件名冲突
4. 文件备份和恢复
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
from loguru import logger

from config.settings import NAMING_RULES, DOWNLOAD_DIR


class FileManager:
    """文件管理器"""
    
    def __init__(self, base_dir: str = None):
        self.base_dir = Path(base_dir) if base_dir else DOWNLOAD_DIR
        self.base_dir.mkdir(exist_ok=True)
        self.naming_rules = NAMING_RULES
    
    def generate_filename(self, literature_info: Dict[str, str], 
                         original_filename: str = None) -> str:
        """根据文献信息生成文件名"""
        try:
            # 提取信息
            title = literature_info.get('title', '').strip()
            authors = literature_info.get('authors', [])
            year = literature_info.get('year', '').strip()
            
            # 处理作者信息
            if isinstance(authors, list) and authors:
                first_author = authors[0]
            elif isinstance(authors, str):
                author_list = re.split(r'[,;&]|\sand\s', authors)
                first_author = author_list[0].strip() if author_list else ''
            else:
                first_author = ''
            
            # 清理字段
            title = self.clean_text_for_filename(title)
            first_author = self.clean_text_for_filename(first_author)
            year = self.clean_text_for_filename(year)
            
            # 使用模板生成文件名
            template = self.naming_rules['format_template']
            
            # 替换占位符
            filename = template.format(
                title=title[:50] if title else 'Unknown_Title',
                first_author=first_author[:30] if first_author else 'Unknown_Author',
                year=year if year else 'Unknown_Year'
            )
            
            # 获取原始文件扩展名
            if original_filename:
                original_ext = Path(original_filename).suffix
            else:
                original_ext = '.pdf'
            
            # 确保扩展名
            if not filename.endswith(original_ext):
                filename += original_ext
            
            # 限制长度
            max_length = self.naming_rules['max_filename_length']
            if len(filename) > max_length:
                name_part = filename[:max_length - len(original_ext)]
                filename = name_part + original_ext
            
            return filename
            
        except Exception as e:
            logger.error(f"生成文件名失败: {e}")
            # 回退到默认命名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"literature_{timestamp}.pdf"
    
    def clean_text_for_filename(self, text: str) -> str:
        """清理文本用于文件名"""
        if not text:
            return ""
        
        # 移除或替换非法字符
        invalid_chars = self.naming_rules['invalid_chars']
        replacement_char = self.naming_rules['replacement_char']
        
        cleaned = re.sub(invalid_chars, replacement_char, text)
        
        # 移除多余的空格和下划线
        cleaned = re.sub(r'[_\s]+', '_', cleaned)
        
        # 移除开头和结尾的下划线
        cleaned = cleaned.strip('_')
        
        return cleaned
    
    def rename_file(self, old_path: str, literature_info: Dict[str, str], 
                   target_dir: str = None) -> Optional[str]:
        """重命名文件"""
        try:
            old_path = Path(old_path)
            if not old_path.exists():
                logger.error(f"源文件不存在: {old_path}")
                return None
            
            # 确定目标目录
            if target_dir:
                target_dir = Path(target_dir)
                target_dir.mkdir(parents=True, exist_ok=True)
            else:
                target_dir = old_path.parent
            
            # 生成新文件名
            new_filename = self.generate_filename(literature_info, old_path.name)
            new_path = target_dir / new_filename
            
            # 处理文件名冲突
            new_path = self.resolve_filename_conflict(new_path)
            
            # 移动并重命名文件
            shutil.move(str(old_path), str(new_path))
            
            logger.info(f"文件重命名成功: {old_path.name} -> {new_path.name}")
            return str(new_path)
            
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return None
    
    def resolve_filename_conflict(self, file_path: Path) -> Path:
        """解决文件名冲突"""
        if not file_path.exists():
            return file_path
        
        # 添加数字后缀
        counter = 1
        stem = file_path.stem
        suffix = file_path.suffix
        parent = file_path.parent
        
        while True:
            new_name = f"{stem}_{counter}{suffix}"
            new_path = parent / new_name
            
            if not new_path.exists():
                return new_path
            
            counter += 1
            
            # 防止无限循环
            if counter > 1000:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_name = f"{stem}_{timestamp}{suffix}"
                return parent / new_name
    
    def organize_files_by_category(self, files: List[Dict[str, str]], 
                                  base_dir: str = None) -> Dict[str, List[str]]:
        """按类别组织文件"""
        if not base_dir:
            base_dir = self.base_dir
        else:
            base_dir = Path(base_dir)
            base_dir.mkdir(parents=True, exist_ok=True)
        
        organized = {
            'by_year': [],
            'by_author': [],
            'by_journal': [],
            'uncategorized': []
        }
        
        for file_info in files:
            try:
                file_path = file_info.get('file_path', '')
                literature_info = file_info.get('literature_info', {})
                
                if not file_path or not Path(file_path).exists():
                    continue
                
                # 按年份组织
                year = literature_info.get('year', '').strip()
                if year:
                    year_dir = base_dir / 'by_year' / year
                    year_dir.mkdir(parents=True, exist_ok=True)
                    new_path = self.copy_file_to_directory(file_path, year_dir, literature_info)
                    if new_path:
                        organized['by_year'].append(new_path)
                
                # 按作者组织
                authors = literature_info.get('authors', [])
                if authors:
                    first_author = authors[0] if isinstance(authors, list) else str(authors).split(',')[0]
                    author_name = self.clean_text_for_filename(first_author.strip())
                    if author_name:
                        author_dir = base_dir / 'by_author' / author_name
                        author_dir.mkdir(parents=True, exist_ok=True)
                        new_path = self.copy_file_to_directory(file_path, author_dir, literature_info)
                        if new_path:
                            organized['by_author'].append(new_path)
                
                # 按期刊组织
                journal = literature_info.get('journal', '').strip()
                if journal:
                    journal_name = self.clean_text_for_filename(journal)
                    if journal_name:
                        journal_dir = base_dir / 'by_journal' / journal_name
                        journal_dir.mkdir(parents=True, exist_ok=True)
                        new_path = self.copy_file_to_directory(file_path, journal_dir, literature_info)
                        if new_path:
                            organized['by_journal'].append(new_path)
                
            except Exception as e:
                logger.error(f"组织文件失败: {e}")
                organized['uncategorized'].append(file_path)
        
        return organized
    
    def copy_file_to_directory(self, source_path: str, target_dir: Path, 
                              literature_info: Dict[str, str]) -> Optional[str]:
        """复制文件到指定目录"""
        try:
            source_path = Path(source_path)
            if not source_path.exists():
                return None
            
            # 生成目标文件名
            new_filename = self.generate_filename(literature_info, source_path.name)
            target_path = target_dir / new_filename
            
            # 解决冲突
            target_path = self.resolve_filename_conflict(target_path)
            
            # 复制文件
            shutil.copy2(str(source_path), str(target_path))
            
            return str(target_path)
            
        except Exception as e:
            logger.error(f"复制文件失败: {e}")
            return None
    
    def create_literature_index(self, files: List[Dict[str, str]], 
                               output_file: str = None) -> str:
        """创建文献索引文件"""
        try:
            if not output_file:
                output_file = self.base_dir / 'literature_index.txt'
            else:
                output_file = Path(output_file)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("文献索引\n")
                f.write("=" * 50 + "\n\n")
                
                for i, file_info in enumerate(files, 1):
                    literature_info = file_info.get('literature_info', {})
                    file_path = file_info.get('file_path', '')
                    
                    f.write(f"{i}. {literature_info.get('title', 'Unknown Title')}\n")
                    f.write(f"   作者: {literature_info.get('authors', 'Unknown')}\n")
                    f.write(f"   年份: {literature_info.get('year', 'Unknown')}\n")
                    f.write(f"   期刊: {literature_info.get('journal', 'Unknown')}\n")
                    f.write(f"   文件: {Path(file_path).name if file_path else 'Unknown'}\n")
                    
                    if literature_info.get('doi'):
                        f.write(f"   DOI: {literature_info['doi']}\n")
                    
                    f.write("\n")
            
            logger.info(f"文献索引创建完成: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"创建文献索引失败: {e}")
            return ""
    
    def backup_files(self, files: List[str], backup_dir: str = None) -> bool:
        """备份文件"""
        try:
            if not backup_dir:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_dir = self.base_dir / f'backup_{timestamp}'
            else:
                backup_dir = Path(backup_dir)
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            for file_path in files:
                source = Path(file_path)
                if source.exists():
                    target = backup_dir / source.name
                    target = self.resolve_filename_conflict(target)
                    shutil.copy2(str(source), str(target))
            
            logger.info(f"文件备份完成: {backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"备份文件失败: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Dict[str, any]:
        """获取文件信息"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'error': '文件不存在'}
            
            stat = path.stat()
            
            return {
                'name': path.name,
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'extension': path.suffix,
                'is_pdf': path.suffix.lower() == '.pdf'
            }
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {'error': str(e)}
