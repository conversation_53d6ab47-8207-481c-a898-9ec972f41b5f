using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using MineralCompositionSystem.Core;
using MineralCompositionSystem.Services;
using MineralCompositionSystem.Forms;

namespace MineralCompositionSystem
{
    internal static class Program
    {
        [DllImport("kernel32.dll")]
        static extern bool AllocConsole();

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            // 检查是否是测试模式
            if (args.Length > 0 && args[0] == "--test")
            {
                // 分配控制台窗口
                AllocConsole();
                Console.WriteLine("启动测试模式...");
                // 运行测试
                RunBrittlenessCalculatorTest();
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }

            Console.WriteLine("启动正常模式...");

            // 使用现代的应用程序配置初始化方法，自动处理高DPI设置
            ApplicationConfiguration.Initialize();

            // 设置默认字体以确保在高DPI下显示清晰
            Application.SetDefaultFont(new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point));

            try
            {
                // 初始化日志服务
                LoggingService.Instance.Info("矿物组分法脆性指数分析系统启动");

                // 创建并运行登录窗体
                bool loginSuccess;
                string username;
                DialogResult loginResult = ShowLoginForm(out loginSuccess, out username);

                if (loginSuccess)
                {
                    // 登录成功，显示仪表盘
                    Application.Run(new DashboardForm(username));
                }
            }
            catch (Exception ex)
            {
                // 记录未处理的异常
                LoggingService.Instance.Exception(ex, "应用程序发生未处理的异常");

                // 向用户显示友好的错误消息
                MessageBox.Show(
                    "应用程序发生了未知错误，即将关闭。请联系技术支持。\n\n错误详情已保存到日志文件中。",
                    "应用程序错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                // 记录应用程序关闭
                LoggingService.Instance.Info("矿物组分法脆性指数分析系统关闭");
            }
        }

        /// <summary>
        /// 运行BrittlenessCalculator测试
        /// </summary>
        private static void RunBrittlenessCalculatorTest()
        {
            Console.WriteLine("=== BrittlenessCalculator 测试开始 ===");
            Console.WriteLine("测试时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            Console.WriteLine();

            try
            {
                TestUserFriendlyColumnNames();

                Console.WriteLine();
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("=== BrittlenessCalculator 测试结束 ===");
        }

        /// <summary>
        /// 测试用户友好列名映射功能
        /// </summary>
        private static void TestUserFriendlyColumnNames()
        {
            Console.WriteLine("开始测试用户友好列名映射功能...");

            // 创建测试数据表，模拟原始Excel数据
            // 这次我们直接使用用户友好的列名来简化测试
            System.Data.DataTable sourceData = new System.Data.DataTable();
            sourceData.Columns.Add("Column1", typeof(double)); // 顶深
            sourceData.Columns.Add("Column2", typeof(double)); // 底深
            sourceData.Columns.Add("石英", typeof(double)); // 石英列（用户友好列名）
            sourceData.Columns.Add("长石", typeof(double)); // 长石列（用户友好列名）
            sourceData.Columns.Add("黏土", typeof(double)); // 黏土列（用户友好列名）

            // 添加测试数据
            System.Data.DataRow row1 = sourceData.NewRow();
            row1["Column1"] = 100.0; // 顶深
            row1["Column2"] = 110.0; // 底深
            row1["石英"] = 25.5; // 石英
            row1["长石"] = 15.2; // 长石
            row1["黏土"] = 30.8; // 黏土
            sourceData.Rows.Add(row1);

            System.Data.DataRow row2 = sourceData.NewRow();
            row2["Column1"] = 110.0;
            row2["Column2"] = 120.0;
            row2["石英"] = 30.2;
            row2["长石"] = 18.5;
            row2["黏土"] = 25.3;
            sourceData.Rows.Add(row2);

            // 模拟用户选择的列名（包含用户友好名称和示例值）
            List<string> brittleColumns = new List<string>
            {
                "石英: 25.5", // 用户看到的是"石英"，实际数据也在"石英"列
                "长石: 15.2"  // 用户看到的是"长石"，实际数据也在"长石"列
            };

            List<string> ductileColumns = new List<string>
            {
                "黏土: 30.8"  // 用户看到的是"黏土"，实际数据也在"黏土"列
            };

            // 创建BrittlenessCalculator实例
            BrittlenessCalculator calculator = new BrittlenessCalculator(
                sourceData,
                brittleColumns,
                ductileColumns,
                0, // 顶深列索引
                1  // 底深列索引
            );

            // 执行计算
            System.Data.DataTable result = calculator.Calculate();

            // 验证结果
            Console.WriteLine("计算完成，验证结果...");
            Console.WriteLine($"结果表列数: {result.Columns.Count}");

            // 打印所有列名
            Console.WriteLine("结果表列名:");
            foreach (System.Data.DataColumn column in result.Columns)
            {
                Console.WriteLine($"  - {column.ColumnName}");
            }

            // 验证是否包含用户友好的列名
            bool hasQuartzColumn = result.Columns.Contains("石英");
            bool hasFeldsparColumn = result.Columns.Contains("长石");
            bool hasClayColumn = result.Columns.Contains("黏土");

            Console.WriteLine($"包含'石英'列: {hasQuartzColumn}");
            Console.WriteLine($"包含'长石'列: {hasFeldsparColumn}");
            Console.WriteLine($"包含'黏土'列: {hasClayColumn}");

            // 验证应该包含基本列
            bool hasGeoIDColumn = result.Columns.Contains("GeoID");
            bool hasTopDepthColumn = result.Columns.Contains("顶深/m");
            bool hasBottomDepthColumn = result.Columns.Contains("底深/m");

            Console.WriteLine($"包含'GeoID'列: {hasGeoIDColumn}");
            Console.WriteLine($"包含'顶深/m'列: {hasTopDepthColumn}");
            Console.WriteLine($"包含'底深/m'列: {hasBottomDepthColumn}");

            // 验证数据是否正确复制
            if (result.Rows.Count > 0)
            {
                System.Data.DataRow firstRow = result.Rows[0];
                if (hasQuartzColumn)
                {
                    double quartzValue = Convert.ToDouble(firstRow["石英"]);
                    Console.WriteLine($"第一行石英值: {quartzValue}");
                    Console.WriteLine($"石英值是否正确: {quartzValue == 25.5}");
                }
            }

            // 测试结果
            bool testPassed = hasQuartzColumn && hasFeldsparColumn && hasClayColumn &&
                             hasGeoIDColumn && hasTopDepthColumn && hasBottomDepthColumn;

            Console.WriteLine($"\n测试结果: {(testPassed ? "通过" : "失败")}");

            if (testPassed)
            {
                Console.WriteLine("✓ 用户友好列名映射功能正常工作");
            }
            else
            {
                Console.WriteLine("✗ 用户友好列名映射功能存在问题");
            }
        }

        /// <summary>
        /// 显示登录窗体
        /// </summary>
        /// <param name="loginSuccess">登录是否成功</param>
        /// <param name="username">用户名</param>
        /// <returns>对话框结果</returns>
        private static DialogResult ShowLoginForm(out bool loginSuccess, out string username)
        {
            loginSuccess = false;
            username = string.Empty;

            using (var loginForm = new LoginForm())
            {
                DialogResult result = loginForm.ShowDialog();
                if (result == DialogResult.OK)
                {
                    loginSuccess = true;
                    username = "admin"; // 简化的用户名
                }
                return result;
            }
        }
    }
}
