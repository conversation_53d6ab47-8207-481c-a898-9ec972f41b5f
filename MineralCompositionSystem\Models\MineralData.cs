using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace MineralCompositionSystem.Models
{
    /// <summary>
    /// 矿物数据模型类
    /// </summary>
    public class MineralData
    {
        /// <summary>
        /// 获取或设置样品ID
        /// </summary>
        public string SampleId { get; set; }

        /// <summary>
        /// 获取或设置顶深（米）
        /// </summary>
        public double TopDepth { get; set; }

        /// <summary>
        /// 获取或设置底深（米）
        /// </summary>
        public double BottomDepth { get; set; }

        /// <summary>
        /// 获取或设置脆性矿物含量字典
        /// </summary>
        public Dictionary<string, double> BrittleMinerals { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// 获取或设置塑性矿物含量字典
        /// </summary>
        public Dictionary<string, double> DuctileMinerals { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// 获取或设置黏土矿物总量
        /// </summary>
        public double ClayMineralTotal { get; set; }

        /// <summary>
        /// 获取或设置TOC（总有机碳）含量
        /// </summary>
        public double TOC { get; set; }

        /// <summary>
        /// 获取或设置脆性指数
        /// </summary>
        public double BrittlenessIndex { get; set; }

        /// <summary>
        /// 获取脆性矿物总量
        /// </summary>
        public double BrittleMineralTotal => BrittleMinerals.Values.Sum();

        /// <summary>
        /// 获取塑性矿物总量
        /// </summary>
        public double DuctileMineralTotal => DuctileMinerals.Values.Sum();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MineralData()
        {
            SampleId = string.Empty;
            TopDepth = 0;
            BottomDepth = 0;
            ClayMineralTotal = 0;
            TOC = 0;
            BrittlenessIndex = 0;
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="sampleId">样品ID</param>
        /// <param name="topDepth">顶深</param>
        /// <param name="bottomDepth">底深</param>
        public MineralData(string sampleId, double topDepth, double bottomDepth)
        {
            SampleId = sampleId;
            TopDepth = topDepth;
            BottomDepth = bottomDepth;
            ClayMineralTotal = 0;
            TOC = 0;
            BrittlenessIndex = 0;
        }

        /// <summary>
        /// 添加脆性矿物
        /// </summary>
        /// <param name="mineralName">矿物名称</param>
        /// <param name="content">含量</param>
        public void AddBrittleMineral(string mineralName, double content)
        {
            if (!string.IsNullOrEmpty(mineralName))
            {
                BrittleMinerals[mineralName] = content;
            }
        }

        /// <summary>
        /// 添加塑性矿物
        /// </summary>
        /// <param name="mineralName">矿物名称</param>
        /// <param name="content">含量</param>
        public void AddDuctileMineral(string mineralName, double content)
        {
            if (!string.IsNullOrEmpty(mineralName))
            {
                DuctileMinerals[mineralName] = content;
            }
        }

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        public void CalculateBrittlenessIndex()
        {
            double brittleTotal = BrittleMineralTotal;
            double ductileTotal = DuctileMineralTotal;

            if (brittleTotal + ductileTotal > 0)
            {
                BrittlenessIndex = brittleTotal / (brittleTotal + ductileTotal);
            }
            else
            {
                BrittlenessIndex = 0;
            }
        }

        /// <summary>
        /// 获取简短描述
        /// </summary>
        /// <returns>简短描述字符串</returns>
        public override string ToString()
        {
            return $"样品ID: {SampleId}, 深度: {TopDepth}-{BottomDepth}m, 脆性指数: {BrittlenessIndex:F2}";
        }
    }
}