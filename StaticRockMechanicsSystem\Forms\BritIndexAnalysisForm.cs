using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem
{
    public class BritIndexAnalysisForm : Form
    {
        // 控件
        private TabControl tabControl = new TabControl(); // 初始化非空字段，解决CS8618错误
        private TabPage tabFileInterpret;
        private TabPage tabKnownFormation;
        private TabPage tabOilGroupPoints;
        private TabPage tabExportTable;
        private SaveFileDialog saveFileDialog;

        private DataGridView dgvDepthData;
        private Chart chartBrittleness;
        private Panel pnlChart;
        private Button btnGenerateCurve;
        private Button btnResetView;
        private Button btnSaveImage;

        // 数据
        private DataTable depthData;
        private string username;

        public BritIndexAnalysisForm(string username)
        {
            this.username = username;
            saveFileDialog = new SaveFileDialog();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "矿物的脆性指数分析系统 (客户版)";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 240, 240);

            // 创建选项卡控件
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Microsoft YaHei", 9F);

            // 创建选项卡页
            tabFileInterpret = new TabPage("解译文件");
            tabKnownFormation = new TabPage("已知地层");
            tabOilGroupPoints = new TabPage("油群解释点");
            tabExportTable = new TabPage("导出表格");

            // 添加选项卡页到选项卡控件
            tabControl.TabPages.Add(tabFileInterpret);
            tabControl.TabPages.Add(tabKnownFormation);
            tabControl.TabPages.Add(tabOilGroupPoints);
            tabControl.TabPages.Add(tabExportTable);

            // 设置解译文件选项卡
            SetupFileInterpretTab();

            // 添加选项卡控件到窗体
            this.Controls.Add(tabControl);

            // 窗体大小调整事件
            this.Resize += BritIndexAnalysisForm_Resize;
        }

        private void SetupFileInterpretTab()
        {
            // 创建子选项卡控件
            TabControl subTabControl = new TabControl();
            subTabControl.Dock = DockStyle.Top;
            subTabControl.Height = 30;
            subTabControl.Font = new Font("Microsoft YaHei", 9F);
            subTabControl.DrawMode = TabDrawMode.OwnerDrawFixed;
            subTabControl.DrawItem += SubTabControl_DrawItem;

            // 创建子选项卡页
            TabPage tabAutoInterpret = new TabPage("自动识别");
            TabPage tabManualInterpret = new TabPage("手动识别");

            // 添加子选项卡页到子选项卡控件
            subTabControl.TabPages.Add(tabAutoInterpret);
            subTabControl.TabPages.Add(tabManualInterpret);

            // 创建数据面板
            Panel pnlData = new Panel();
            pnlData.Dock = DockStyle.Fill;
            pnlData.Padding = new Padding(10);

            // 创建数据表格
            dgvDepthData = new DataGridView();
            dgvDepthData.Location = new Point(10, 40);
            dgvDepthData.Size = new Size(450, tabFileInterpret.Height - 80);
            dgvDepthData.BackgroundColor = Color.White;
            dgvDepthData.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            dgvDepthData.RowHeadersVisible = false;
            dgvDepthData.AllowUserToAddRows = false;
            dgvDepthData.AllowUserToDeleteRows = false;
            dgvDepthData.ReadOnly = true;
            dgvDepthData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvDepthData.ColumnHeadersDefaultCellStyle.BackColor = Color.LightGray;
            dgvDepthData.ColumnHeadersDefaultCellStyle.Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold);
            dgvDepthData.AlternatingRowsDefaultCellStyle.BackColor = Color.WhiteSmoke;

            // 创建图表面板
            pnlChart = new Panel();
            pnlChart.Location = new Point(470, 40);
            pnlChart.Size = new Size(tabFileInterpret.Width - 490, tabFileInterpret.Height - 80);
            pnlChart.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;

            // 创建图表按钮
            btnGenerateCurve = new Button();
            btnGenerateCurve.Text = "生成曲线";
            btnGenerateCurve.Font = new Font("Microsoft YaHei", 9F);
            btnGenerateCurve.Location = new Point(470, 10);
            btnGenerateCurve.Size = new Size(100, 25);
            btnGenerateCurve.Click += BtnGenerateCurve_Click;

            btnResetView = new Button();
            btnResetView.Text = "还原视图";
            btnResetView.Font = new Font("Microsoft YaHei", 9F);
            btnResetView.Location = new Point(580, 10);
            btnResetView.Size = new Size(100, 25);
            btnResetView.Click += BtnResetView_Click;

            btnSaveImage = new Button();
            btnSaveImage.Text = "保存图像";
            btnSaveImage.Font = new Font("Microsoft YaHei", 9F);
            btnSaveImage.Location = new Point(690, 10);
            btnSaveImage.Size = new Size(100, 25);
            btnSaveImage.Click += BtnSaveImage_Click;

            // 创建图表控件
            chartBrittleness = new Chart();
            chartBrittleness.Dock = DockStyle.Fill;
            chartBrittleness.BackColor = Color.White;

            // 配置图表区域
            ChartArea chartArea = new ChartArea("BrittlenessArea");
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisX.TitleFont = new Font("Microsoft YaHei", 9F);
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisY.TitleFont = new Font("Microsoft YaHei", 9F);
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 20;
            chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisY.IsReversed = true; // 深度增加时向下显示
            chartBrittleness.ChartAreas.Add(chartArea);

            // 添加图例
            Legend legend = new Legend("BrittnessLegend");
            legend.Font = new Font("Microsoft YaHei", 9F);
            chartBrittleness.Legends.Add(legend);

            // 添加数据系列
            Series series = new Series("脆性指数");
            series.ChartType = SeriesChartType.Point;
            series.MarkerStyle = MarkerStyle.Circle;
            series.MarkerSize = 8;
            series.Color = Color.Red;
            series.BorderWidth = 2;
            series.IsVisibleInLegend = true;
            series.Legend = "BrittnessLegend";
            series.ChartArea = "BrittlenessArea";
            chartBrittleness.Series.Add(series);

            // 添加连线系列
            Series lineSeries = new Series("连线");
            lineSeries.ChartType = SeriesChartType.Line;
            lineSeries.Color = Color.Blue;
            lineSeries.BorderWidth = 1;
            lineSeries.IsVisibleInLegend = false;
            lineSeries.ChartArea = "BrittlenessArea";
            chartBrittleness.Series.Add(lineSeries);

            // 添加控件到图表面板
            pnlChart.Controls.Add(chartBrittleness);

            // 添加控件到数据面板
            pnlData.Controls.Add(dgvDepthData);
            pnlData.Controls.Add(pnlChart);
            pnlData.Controls.Add(btnGenerateCurve);
            pnlData.Controls.Add(btnResetView);
            pnlData.Controls.Add(btnSaveImage);

            // 添加子选项卡控件和数据面板到解译文件选项卡
            tabFileInterpret.Controls.Add(pnlData);
            tabFileInterpret.Controls.Add(subTabControl);
        }

        private void SubTabControl_DrawItem(object sender, DrawItemEventArgs e)
        {
            // 自定义绘制子选项卡
            TabControl tabControl = (TabControl)sender;
            TabPage tabPage = tabControl.TabPages[e.Index];
            Rectangle tabBounds = tabControl.GetTabRect(e.Index);

            // 选中的选项卡使用不同的颜色
            if (e.Index == tabControl.SelectedIndex)
            {
                e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 120, 215)), tabBounds);
                TextRenderer.DrawText(e.Graphics, tabPage.Text, tabPage.Font, tabBounds, Color.White, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }
            else
            {
                e.Graphics.FillRectangle(new SolidBrush(Color.LightGray), tabBounds);
                TextRenderer.DrawText(e.Graphics, tabPage.Text, tabPage.Font, tabBounds, Color.Black, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }
        }

        private void BritIndexAnalysisForm_Resize(object sender, EventArgs e)
        {
            if (tabFileInterpret.Controls.Count > 0 && tabFileInterpret.Controls[0] is Panel pnlData)
            {
                // 调整数据表格大小
                if (dgvDepthData != null)
                {
                    dgvDepthData.Size = new Size(450, tabFileInterpret.Height - 80);
                }

                // 调整图表面板大小和位置
                if (pnlChart != null)
                {
                    pnlChart.Location = new Point(470, 40);
                    pnlChart.Size = new Size(tabFileInterpret.Width - 490, tabFileInterpret.Height - 80);
                }
            }
        }

        private void LoadData()
        {
            try
            {
                // 初始化数据表
                depthData = new DataTable();
                depthData.Columns.Add("顶深/m", typeof(double));
                depthData.Columns.Add("底深/m", typeof(double));
                depthData.Columns.Add("脆性指数", typeof(double));

                // 添加示例数据（与截图中的数据一致）
                depthData.Rows.Add(705.46, 705.61, 60.22);
                depthData.Rows.Add(706.31, 706.41, 68.56);
                depthData.Rows.Add(706.96, 707.11, 60.99);
                depthData.Rows.Add(707.76, 707.86, 59.3);
                depthData.Rows.Add(708.09, 708.19, 76.72);
                depthData.Rows.Add(708.96, 709.26, 57.75);
                depthData.Rows.Add(712.19, 712.39, 35.7);
                depthData.Rows.Add(712.76, 713.06, 54.78);
                depthData.Rows.Add(713.46, 713.66, 28.31);
                depthData.Rows.Add(714.56, 714.66, 100);
                depthData.Rows.Add(715.11, 715.31, 38.74);
                depthData.Rows.Add(715.36, 715.66, 79.98);
                depthData.Rows.Add(716.34, 716.64, 72.22);
                depthData.Rows.Add(717.06, 717.26, 22.22);
                depthData.Rows.Add(717.46, 717.66, 41.8);
                depthData.Rows.Add(718.02, 718.24, 69.39);
                depthData.Rows.Add(719.36, 719.56, 23.42);
                depthData.Rows.Add(721.65, 721.78, 64.53);
                depthData.Rows.Add(722.46, 722.66, 57.97);
                depthData.Rows.Add(723.4, 723.6, 55.24);
                depthData.Rows.Add(724.06, 724.26, 60.98);

                // 设置数据表格列
                dgvDepthData.Columns.Clear();
                dgvDepthData.Columns.Add("topDepth", "顶深/m");
                dgvDepthData.Columns.Add("bottomDepth", "底深/m");
                dgvDepthData.Columns.Add("brittleness", "脆性指数");

                // 将数据表中的数据显示到数据网格中
                dgvDepthData.Rows.Clear();
                foreach (DataRow row in depthData.Rows)
                {
                    dgvDepthData.Rows.Add(
                        row["顶深/m"],
                        row["底深/m"],
                        row["脆性指数"]
                    );
                }

                // 更新图表
                UpdateChart();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateChart()
        {
            try
            {
                // 清除现有数据
                chartBrittleness.Series[0].Points.Clear();
                chartBrittleness.Series[1].Points.Clear();

                // 添加新数据
                foreach (DataRow row in depthData.Rows)
                {
                    double topDepth = Convert.ToDouble(row["顶深/m"]);
                    double bottomDepth = Convert.ToDouble(row["底深/m"]);
                    double brittleness = Convert.ToDouble(row["脆性指数"]);

                    // 使用中点深度
                    double midDepth = (topDepth + bottomDepth) / 2;

                    // 添加数据点
                    int pointIndex = chartBrittleness.Series[0].Points.AddXY(brittleness, midDepth);
                    chartBrittleness.Series[0].Points[pointIndex].MarkerSize = 8;
                    chartBrittleness.Series[0].Points[pointIndex].ToolTip =
                        $"深度: {midDepth}m, 脆性指数: {brittleness}%";

                    // 添加到连线系列
                    chartBrittleness.Series[1].Points.AddXY(brittleness, midDepth);
                }

                // 调整Y轴范围
                if (depthData.Rows.Count > 0)
                {
                    double minDepth = depthData.AsEnumerable().Min(r => Convert.ToDouble(r["顶深/m"])) - 5;
                    double maxDepth = depthData.AsEnumerable().Max(r => Convert.ToDouble(r["底深/m"])) + 5;

                    chartBrittleness.ChartAreas[0].AxisY.Minimum = minDepth;
                    chartBrittleness.ChartAreas[0].AxisY.Maximum = maxDepth;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新图表时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                // 更新图表
                UpdateChart();
                MessageBox.Show("曲线生成成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnResetView_Click(object sender, EventArgs e)
        {
            try
            {
                // 重置图表视图
                if (depthData.Rows.Count > 0)
                {
                    double minDepth = depthData.AsEnumerable().Min(r => Convert.ToDouble(r["顶深/m"])) - 5;
                    double maxDepth = depthData.AsEnumerable().Max(r => Convert.ToDouble(r["底深/m"])) + 5;

                    chartBrittleness.ChartAreas[0].AxisY.Minimum = minDepth;
                    chartBrittleness.ChartAreas[0].AxisY.Maximum = maxDepth;
                    chartBrittleness.ChartAreas[0].AxisX.Minimum = 0;
                    chartBrittleness.ChartAreas[0].AxisX.Maximum = 100;

                    // 刷新图表
                    chartBrittleness.Invalidate();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"还原视图时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建保存文件对话框
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "PNG图像 (*.png)|*.png|JPEG图像 (*.jpg)|*.jpg|BMP图像 (*.bmp)|*.bmp";
                saveFileDialog.Title = "保存图像";
                saveFileDialog.FileName = $"脆性指数曲线_{DateTime.Now:yyyyMMdd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 保存图表为图像
                    chartBrittleness.SaveImage(saveFileDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show($"图像已保存到: {saveFileDialog.FileName}", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图像时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveData_Click(object sender, EventArgs e)
        {
            try
            {
                if (depthData == null || depthData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可供保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    string extension = Path.GetExtension(filePath).ToLower();

                    switch (extension)
                    {
                        case ".xlsx":
                            ExportToExcel(filePath);
                            break;
                        case ".csv":
                            ExportToCsv(filePath);
                            break;
                        default:
                            MessageBox.Show("不支持的文件格式", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                    }

                    MessageBox.Show("数据保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string filePath)
        {
            XSSFWorkbook workbook = null;
            try
            {
                workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("脆性指数数据");

                // 创建表头
                IRow headerRow = sheet.CreateRow(0);
                for (int i = 0; i < depthData.Columns.Count; i++)
                {
                    headerRow.CreateCell(i).SetCellValue(depthData.Columns[i].ColumnName);
                }

                // 填充数据
                for (int rowIndex = 0; rowIndex < depthData.Rows.Count; rowIndex++)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex + 1);
                    for (int colIndex = 0; colIndex < depthData.Columns.Count; colIndex++)
                    {
                        dataRow.CreateCell(colIndex).SetCellValue(depthData.Rows[rowIndex][colIndex].ToString());
                    }
                }

                // 调整列宽
                for (int i = 0; i < depthData.Columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 写入文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }
            }
            finally
            {
                workbook?.Close();
            }
        }

        private void ExportToCsv(string filePath)
        {
            using (StreamWriter sw = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // 写入表头
                sw.WriteLine(string.Join(",", depthData.Columns.Cast<DataColumn>().Select(c => c.ColumnName)));

                // 写入数据行
                foreach (DataRow row in depthData.Rows)
                {
                    sw.WriteLine(string.Join(",", row.ItemArray.Select(item => item.ToString())));
                }
            }
        }
    }
}
